@include('inc.kanbanStyle')
@include('inc.success')
<!--kanban Page-->
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Kanban Board for {{ $sprint->sprint_name }}</title>

    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Roboto:wght@400;700&display=swap">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" />
    <!-- <link rel="stylesheet" href="{{ asset('/css/styles.css') }}" /> -->

</head>

<body>

<button onclick="window.location.href='{{ action('ProductFeatureController@index2', $project->proj_name) }}'"
        style="position: absolute; top: 10px; left: 10px; padding: 10px; background-color: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer;">
    Back
</button>

    <div class="board">
        
        <div class="button">
            <button id="add-lane-btn">Add New Lane</button>
            @if ($isSprintOverdue)
            <div class="overdue-message">
                The sprint is overdue!
            </div>
            @endif
        </div>
        <!-- Add this inline style to your button in your HTML -->
        <button id="save-btn" style="display: none">Save</button>
        <div class="lanes">
            @foreach ($statuses as $status)
                        <?php
                $taskList = $tasksByStatus[$status->id] ?? [];
                        ?>
                        <div class="swim-lane" data-status-id="{{ $status->id }}">
                            <h3 class="heading">{{ $status->title }}</h3>

                            <button type="button" class="rename-btn">Rename</button>

                            <button type="button" class="delete-btn">Delete</button>

                                <form action="{{ (route('kanban.createTask', [], false)) }}" method="post">

                                @csrf
                                <input type="hidden" name="sprintId" value="{{ $sprint->sprint_id }}">
                                <input type="hidden" name="statusId" class="status-id-input" value="{{ $status->id }}">
                                <button type="submit" class="new-submit-btn">Add +</button>
                            </form>
                            @foreach ($taskList as $task)
                            <div class="task" draggable="true" data-task-id="{{ $task->id }}">
                                <!-- Task Title (Bold) -->
                                <p class="task-title">
                                    {{ $task->title }}
                                </p>
                                <br>

                                <!-- Filter Comments by Created By -->
                                <div class="filter-comments">
                                    <label for="filter_created_by_{{ $task->id }}">Filter by Creator:</label>
                                    <select id="filter_created_by_{{ $task->id }}" class="form-control filter-created-by-select" data-task-id="{{ $task->id }}">
                                        <option value="all">All</option>
                                        @foreach ($task->comments->unique('created_by') as $comment)
                                            <option value="{{ $comment->created_by }}">{{ $comment->created_by }}</option>
                                        @endforeach
                                    </select>
                                </div>
                                
                                <form method="GET" action="{{ route('sprint.kanbanPage', ['proj_id' => $project->id, 'sprint_id' => $sprint->sprint_id]) }}" class="sort-comments-form" id="sort-comments-form-{{ $task->id }}">
                                    <label for="sort_date_{{ $task->id }}">Sort by Date:</label>
                                    <select name="sort_date" id="sort_date_{{ $task->id }}" class="form-control" onchange="this.form.submit()">
                                        <option value="desc" {{ request('sort_date') == 'desc' ? 'selected' : '' }}>Newest</option>
                                        <option value="asc" {{ request('sort_date') == 'asc' ? 'selected' : '' }}>Oldest</option>
                                    </select>
                                </form>
                                @if (!$task->isOverdue && !$isSprintOverdue)
                                <!-- Add Task Comments -->
                                <button type="button" class="add-comment-btn" data-task-id="{{ $task->id }}">Add Comment</button>
                                @endif
                                <br>

                                <!-- Task Comments -->
                                <div class="task-comments" id="comments-container-{{ $task->id }}">
                                    @if ($task->isOverdue)
                                        <div style="color: red">This task is overdue!</div>
                                    @endif
                                    @if ($task->comments->isEmpty())
                                        <p class="task-comment">No comments yet.</p>
                                    @else
                                        @foreach ($task->comments as $index => $comment)
                                        <div class="comment-wrapper {{ $index > 1 ? 'hidden-comment' : '' }}" filter-created-by="{{ $comment->created_by }}" data-created-date="{{ $comment->created_at }}">
                                                <div class="comment-container">
                                                    <p class="task-comment">
                                                        {{ $comment->comment }}
                                                        @if ($comment->updated_at && $comment->updated_at != $comment->created_at)
                                                        <span class="edited-indicator" style="color: gray; font-size: 12px;">(edited)</span>
                                                    @endif
                                                    </p>
                                                    @if (!$task->isOverdue && !$isSprintOverdue && $comment->created_by === auth()->user()->username)
                                                    <button type="button" class="edit-comment-btn" data-comment-id="{{ $comment->id }}">
                                                        <i class="fa fa-pencil"></i>
                                                    </button>
                                                    <form action="{{ route('tasks.deleteComment', $comment->id) }}" method="POST" style="display:inline;">
                                                    @csrf
                                                    @method('DELETE')
                                                    <button type="submit" class="delete-comment-btn">
                                                        <i class="fa fa-trash"></i>
                                                    </button>
                                                    </form>
                                                    @endif
                                                </div>
                                                <p class="comment-date">
                                                    (Comment At: {{ \Carbon\Carbon::parse($comment->created_at)->format('Y-m-d') }})
                                                </p>
                                                <p class="comment-date">
                                                    Created By: {{ $comment->created_by }}
                                                </p>
                                            </div>
                                        @endforeach

                                        @if ($task->comments->count() > 2)
                                            <button class="show-more-btn">Show More</button>
                                            <button class="show-less-btn" style="display: none;">Show Less</button>
                                        @endif
                                    @endif
                                </div>
                          <meta name="csrf-token" content="{{ csrf_token() }}">
                                <!-- Delete Task Button -->
                                <button type="button" class="delete-task-btn">X</button>
                            </div>
                        @endforeach
                        </div>
            @endforeach
            </div>
        </div>
        <div id="editCommentModal" class="modal">
            <div class="modal-content">
                <span class="close-modal">&times;</span>
                <h3>Edit Comment</h3>
                <textarea id="editCommentInput" rows="4" style="width: 100%;"></textarea>
                <button id="saveCommentBtn" class="save-comment-btn">Save</button>
            </div>
        </div>
        <div id="createCommentModal" class="modal">
            <div class="modal-content">
                <span class="close-modal">&times;</span>
                <h3>Create Comment</h3>
                <input id="createCommentInput" rows="4" style="width: 100%;"></input>
                <button id="saveCreateCommentBtn" class="save-comment-btn">Save</button>
            </div>
        </div>

    <script>
document.addEventListener('DOMContentLoaded', function () {
    // Prevent click event propagation for filter and sort elements
    document.querySelectorAll('.filter-created-by-select, .sort-comments-form').forEach(element => {
        element.addEventListener('click', function (e) {
            e.stopPropagation();
        });
    });

    // Handle filtering comments by creator
    document.querySelectorAll('.filter-created-by-select').forEach(select => {
        select.addEventListener('change', function (e) {
            e.stopPropagation();

            const taskId = this.getAttribute('data-task-id'); // Get the task ID
            const filterBy = this.value; // Get the selected value
            const commentsContainer = document.getElementById(`comments-container-${taskId}`);

            if (!commentsContainer) {
                console.error(`Comments container not found for task ID: ${taskId}`);
                return;
            }

            // Get all comment wrappers inside the task-comments container
            const comments = Array.from(commentsContainer.querySelectorAll('.comment-wrapper'));

            let visibleCount = 0;
            const sortByDate = new URLSearchParams(window.location.search).get('sort_date') || 'desc';

            // Sort comments by date
            comments.sort((a, b) => {
                const dateA = new Date(a.getAttribute('data-created-date'));
                const dateB = new Date(b.getAttribute('data-created-date'));
                return sortByDate === 'desc' ? dateB - dateA : dateA - dateB;
            });

            comments.forEach((comment, index) => {
                const createdBy = comment.getAttribute('filter-created-by'); // Get the created_by attribute for each comment

                // Show or hide comments based on the filter
                if (filterBy === 'all' || createdBy === filterBy) {
                    comment.style.display = visibleCount < 2 ? 'block' : 'none'; // Show first 2, hide the rest
                    if (visibleCount >= 2) {
                        comment.classList.add('hidden-comment');
                    } else {
                        comment.classList.remove('hidden-comment');
                    }
                    visibleCount++;
                } else {
                    comment.style.display = 'none';
                    comment.classList.remove('hidden-comment');
                }
            });

            // Show or hide "Show More" and "Show Less" buttons based on visible comments
            const showMoreBtn = commentsContainer.querySelector('.show-more-btn');
            const showLessBtn = commentsContainer.querySelector('.show-less-btn');

            if (visibleCount <= 2) {
                // Hide buttons if there are 2 or fewer visible comments
                if (showMoreBtn) showMoreBtn.style.display = 'none';
                if (showLessBtn) showLessBtn.style.display = 'none';
            } else {
                // Show "Show More" button if there are hidden comments
                if (showMoreBtn) showMoreBtn.style.display = 'inline-block';
                if (showLessBtn) showLessBtn.style.display = 'none';
            }
        });
    });

    // Handle Show More and Show Less buttons
    document.querySelectorAll('.show-more-btn').forEach(showMoreBtn => {
        const commentsContainer = showMoreBtn.closest('.task-comments'); // Get the parent comments container
        const showLessBtn = commentsContainer.querySelector('.show-less-btn');

        showMoreBtn.addEventListener('click', (event) => {
            event.stopPropagation();

            // Show all comments in the current container
            commentsContainer.querySelectorAll('.hidden-comment').forEach(wrapper => {
                wrapper.style.display = 'block';
            });

            // Toggle button visibility
            showMoreBtn.style.display = 'none';
            showLessBtn.style.display = 'inline-block';
        });

        showLessBtn.addEventListener('click', (event) => {
            event.stopPropagation();

            // Hide all comments beyond the first 2 in the current container
            let visibleCount = 0;
            commentsContainer.querySelectorAll('.comment-wrapper').forEach(wrapper => {
                visibleCount++;
                if (visibleCount > 2) {
                    wrapper.style.display = 'none';
                }
            });

            // Toggle button visibility
            showLessBtn.style.display = 'none';
            showMoreBtn.style.display = 'inline-block';
        });
    });

    // Handle sorting by date using AJAX
    document.querySelectorAll('.sort-comments-form').forEach(form => {
        form.querySelector('select').addEventListener('change', function (e) {
            e.preventDefault(); // Prevent the default form submission
            
            // Get the selected sort date value
            const sortByDate = this.value;

            // Prepare the form data
            const formData = new FormData(form);
            formData.append('sort_date', sortByDate);

            // Send the AJAX request
            fetch(form.action, {
                method: 'GET',
                body: formData
            })
            .then(response => response.text())
            .then(data => {
                // Update the page content with the new sorted data
                document.getElementById('comments-container').innerHTML = data; // Replace with your container's ID
            })
            .catch(error => {
                console.error('Error during AJAX request:', error);
            });
        });
    });
});


        //Function to handle common logic for creating a new task element
        function createTaskElement(value) {
            const newTask = document.createElement("p");
            newTask.classList.add("task");
            newTask.setAttribute("draggable", "true");
            newTask.innerText = value;

            newTask.addEventListener("dragstart", () => {
                newTask.classList.add("is-dragging");
            });

            newTask.addEventListener("dragend", () => {
                newTask.classList.remove("is-dragging");
            });

            return newTask;
        }

        //function to reload page upon AJAX request completion
        function handleAjaxResponse(response) {
            console.log(response.message);
            if (response.reload) {
                location.reload();
            }
        }

        // Function to handle common logic for handling drag and drop events
        function handleDragDropEvents(sourceLane, targetLane) {
            sourceLane.addEventListener("dragover", (e) => {
                e.preventDefault();
                const draggingTask = document.querySelector(".is-dragging");

                if (draggingTask) {
                    sourceLane.classList.add("drag-over");
                }
            });

            sourceLane.addEventListener("dragleave", () => {
                sourceLane.classList.remove("drag-over");
            });

            sourceLane.addEventListener("drop", (e) => {
                e.preventDefault();
                sourceLane.classList.remove("drag-over");

                const draggingTask = document.querySelector(".is-dragging");

                if (draggingTask) {
                    targetLane.appendChild(draggingTask);
                    draggingTask.classList.remove("is-dragging");
                }
            });
        }

        // Function to change the lane name
        function changeLaneName(lane, newName) {
            const heading = lane.querySelector(".heading");
            heading.innerText = newName;

            // Get the status ID associated with the lane
            const statusId = lane.dataset.statusId;

            // Make an AJAX request to update the lane name in the database
            fetch('{{ (route("kanban.updateStatus")) }}', {
                method: 'PUT',
                headers: {
                    'X-CSRF-TOKEN': '{{ csrf_token() }}',
                    'Content-Type': 'application/json',
                    'Accept': 'application/json',
                },
                body: JSON.stringify({
                    statusId: statusId,
                    newName: newName,
                }),
            })
                .then(response => response.json())
                .then(data => {
                    // Handle the response from the controller method
                    console.log('After AJAX request to update lane name');
                    console.log(data);
                    alert(data.message); // Display a message received from the server
                    handleAjaxResponse(data);
                })
                .catch(error => {
                    console.error('Error:', error);
                });
        }

        //EventListener After DOMContentLoaded
        document.addEventListener("DOMContentLoaded", () => {
            const addLaneBtn = document.getElementById("add-lane-btn");
            const renameBtns = document.querySelectorAll(".rename-btn");
            const deleteBtns = document.querySelectorAll(".delete-btn");

            //Listener for rename button
            renameBtns.forEach((btn) => {
                btn.addEventListener("click", () => {
                    const newName = prompt("Enter new name for the lane:");

                    if (newName !== null) {
                        const lane = btn.closest(".swim-lane");
                        changeLaneName(lane, newName);
                    }
                });
            });

            //Listener for delete button
            deleteBtns.forEach((btn) => {
                btn.addEventListener("click", () => {
                    const lane = btn.closest(".swim-lane");
                    const laneId = lane.dataset.statusId;

                    // Make an AJAX request to delete the lane and update task statuses
                    fetch('{{ (route("kanban.deleteStatus")) }}', {
                        method: 'DELETE',
                        headers: {
                            'X-CSRF-TOKEN': '{{ csrf_token() }}',
                            'Content-Type': 'application/json',
                            'Accept': 'application/json',
                        },
                        body: JSON.stringify({
                            laneId: laneId,
                        }),
                    })
                        .then(response => response.json())
                        .then(data => {
                            console.log('After AJAX request to delete lane');
                            console.log(data);

                            // Check if the deletion was successful before removing the lane from the UI
                            if (data.success) {
                                lane.remove();
                                alert(data.message); // Display a message received from the server
                            } else {
                                alert(data.error);
                            }

                            handleAjaxResponse(data);
                        })
                        .catch(error => {
                            console.error('Error:', error);
                        });
                });
            });

            // Listener for delete task button
            document.querySelectorAll('.delete-task-btn').forEach((btn) => {
            btn.addEventListener("click", (e) => {
                const taskContainer = btn.closest(".task");
                const taskId = taskContainer.dataset.taskId;


                // Show a confirmation alert before proceeding with deletion
                const confirmDelete = window.confirm("Are you sure you want to delete this task?");

                if (confirmDelete) {
                    // Make an AJAX request to delete the task
                    fetch('{{ (route("kanban.deleteTask")) }}', {
                        method: 'DELETE',
                        headers: {
                            'X-CSRF-TOKEN': '{{ csrf_token() }}',
                            'Content-Type': 'application/json',
                            'Accept': 'application/json',
                        },
                        body: JSON.stringify({
                            taskId: taskId,
                        }),
                    })
                    .then(response => response.json())
                    .then(data => {
                        console.log('After AJAX request to delete task');
                        console.log(data);

                        // Check if the deletion was successful before removing the task from the UI
                        if (data.success) {
                            taskContainer.remove();
                        } else {
                            console.error(data.error);
                        }
                    })
                    .catch(error => {
                        console.error('Error:', error);
                    });
                } else {
                    console.log("Task deletion was canceled.");
                }

                e.stopPropagation(); // Prevent the drag and drop event from triggering
            });
        });

            // Listener for save button - triggered through task movement
            const saveBtn = document.getElementById("save-btn");
            saveBtn.addEventListener("click", () => {
                console.log("Save button clicked");

                // Iterate through all lanes and tasks to gather their positions
                const positions = [];

                document.querySelectorAll(".swim-lane").forEach((lane, laneIndex) => {
                    const laneId = lane.dataset.statusId;

                    lane.querySelectorAll(".task").forEach((task, taskIndex) => {
                        const taskId = task.dataset.taskId;

                        positions.push({
                            taskId: taskId,
                            statusId: laneId,
                            position: taskIndex +
                                1, // Add 1 to make positions 1-based
                        });
                    });
                });

                console.log("Task positions to save:", positions);

                // Make an AJAX request to save the task positions in the database
                fetch('{{ (route("kanban.updateTaskStatus")) }}', {
                    method: 'PUT',
                    headers: {
                        'X-CSRF-TOKEN': '{{ csrf_token() }}',
                        'Content-Type': 'application/json',
                        'Accept': 'application/json',
                    },
                    body: JSON.stringify({
                        positions: positions,
                    }),
                })
                    .then(response => response.json())
                    .then(data => {
                        // Handle the response from the controller method
                        console.log('After AJAX request to save task positions');
                        console.log(data);
                        // alert(data.message); // Display a message received from the server
                        handleAjaxResponse(data);
                    })
                    .catch(error => {
                        console.error('Error:', error);
                    });
            });

            // Task Dragging Logic
            const draggables = document.querySelectorAll(".task");
            const droppables = document.querySelectorAll(".swim-lane");

            draggables.forEach((task) => {
                task.addEventListener("dragstart", () => {
                    task.classList.add("is-dragging");
                });

                task.addEventListener("dragend", () => {
                    task.classList.remove("is-dragging");

                    // Trigger the click event on the save button after the task is dropped
                    saveBtn.click();
                });
            });

            droppables.forEach((zone) => {
                zone.addEventListener("dragover", (e) => {
                    e.preventDefault();

                    const bottomTask = insertAboveTask(zone, e.clientY);
                    const curTask = document.querySelector(".is-dragging");

                    if (!bottomTask) {
                        zone.appendChild(curTask);
                    } else {
                        zone.insertBefore(curTask, bottomTask);
                    }
                });
            });

            // Add event listener for adding a new lane
            addLaneBtn.addEventListener("click", () => {
                const newLaneName = prompt("Enter the name for the new lane:");
                if (newLaneName !== null) {

                    var projectID = "{{ $project->id }}"
                    var sprintID = "{{ $sprint->sprint_ID }}";

                    var dataToSend = {
                        statusName: newLaneName,
                        sprintID: sprintID,
                        project_id: projectID
                    };

                    // Make an AJAX request to call the controller method
                    fetch('{{ (route("kanban.createStatus")) }}', {
                        method: 'POST',
                        headers: {
                            'X-CSRF-TOKEN': '{{ csrf_token() }}',
                            'Content-Type': 'application/json',
                            'Accept': 'application/json',
                        },
                        body: JSON.stringify(dataToSend),
                    })
                        .then(response => response.json())
                        .then(data => {
                            // Handle the response from the controller method
                            console.log('After AJAX request');
                            console.log(data);
                            alert(data.message); // Display a message received from the server
                            handleAjaxResponse(data);
                        })
                        .catch(error => {
                            console.error('Error:', error);
                        });

                }
            });

        });

        //////////////////////////////////////////////////////////////////////

        // Logic for putting task above other task - drag&drop logic
        const insertAboveTask = (zone, mouseY) => {
            const els = zone.querySelectorAll(".task:not(.is-dragging)");

            let closestTask = null;
            let closestOffset = Number.NEGATIVE_INFINITY;

            els.forEach((task) => {
                const {
                    top
                } = task.getBoundingClientRect();

                const offset = mouseY - top;

                if (offset < 0 && offset > closestOffset) {
                    closestOffset = offset;
                    closestTask = task;
                }
            });

            return closestTask;
        };

        // Listener for edit task page
        document.querySelectorAll('.task').forEach(function (task) {
            task.addEventListener('click', function () {
                
                var taskId = task.getAttribute('data-task-id');
                // Redirect to the updateTask page with the task ID
                window.location.href = '{{ (route("kanban.updateTaskPage", ["taskId" => ":taskId"])) }}'
                    .replace(':taskId', taskId);
            });
        });
        document.querySelectorAll('.add-comment-btn').forEach(button => {
            button.addEventListener('click', function (event) {
                event.stopPropagation();

                const taskId = this.getAttribute('data-task-id');
                const modal = document.getElementById('createCommentModal');
                modal.style.display = 'flex';

                const saveCreateButton = document.getElementById('saveCreateCommentBtn');
                saveCreateButton.onclick = function () {
                    const comment = document.getElementById('createCommentInput').value.trim();

                    if (comment) {
                        fetch(`/tasks/${taskId}/createKanbanComment`, {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                            },
                            body: JSON.stringify({ comment: comment })
                        })
                        .then(response => {
                            console.log('Fetch response:', response);
                            if (!response.ok) {
                                throw new Error(`HTTP error! status: ${response.status}`);
                            }
                            return response.json();
                        })
                        .then(data => {
                            if (data.success) {
                                console.log('Comment created successfully.');
                                modal.style.display = 'none'; // Close modal
                                if (data.success) {
                                    location.reload(); // Auto refresh the page
                                } else {
                                    alert('Error creating comment: ' + data.message);
                                }
                            } else {
                                alert('Error creating comment: ' + data.message);
                            }
                        })
                        .catch(error => {
                            console.error('Fetch error:', error);
                        });
                    } else {
                        console.log('No comment provided. Closing modal.');
                        modal.style.display = 'none'; // Close modal
                    }
                };
            });
        });

        
        document.querySelectorAll('.edit-comment-btn').forEach(button => {
    button.addEventListener('click', function (event) {
        event.stopPropagation();

        const commentId = this.getAttribute('data-comment-id');
        const commentElement = this.previousElementSibling;

        // Extract the raw comment text without the (edited) indicator
        let rawComment = commentElement.textContent.trim();
        const editedIndicatorIndex = rawComment.indexOf('(edited)');
        if (editedIndicatorIndex !== -1) {
            rawComment = rawComment.slice(0, editedIndicatorIndex).trim();
        }

        // Show the modal
        const modal = document.getElementById('editCommentModal');
        const inputField = document.getElementById('editCommentInput');
        modal.style.display = 'flex';
        inputField.value = rawComment;

        // Save button functionality
        const saveButton = document.getElementById('saveCommentBtn');
        saveButton.onclick = function () {
            const newComment = inputField.value.trim();

            if (newComment && newComment !== rawComment) {
                fetch(`/tasks/${commentId}/updateKanbanComment`, {
                    method: 'PUT',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                    },
                    body: JSON.stringify({ comment: newComment })
                })
                .then(response => {
                    if (!response.ok) {
                        throw new Error(`HTTP error! status: ${response.status}`);
                    }
                    return response.json();
                })
                .then(data => {
                    if (data.success) {
                        // Update the comment text
                        commentElement.textContent = newComment;

                        // Add the (edited) indicator if it was updated
                        let editedIndicator = commentElement.querySelector('.edited-indicator');
                        if (!editedIndicator) {
                            editedIndicator = document.createElement('span');
                            editedIndicator.classList.add('edited-indicator');
                            editedIndicator.style.color = 'gray';
                            editedIndicator.style.fontSize = '12px';
                            editedIndicator.textContent = ' (edited)';
                            commentElement.appendChild(editedIndicator); // Append directly to the comment element
                        }
                        modal.style.display = 'none'; // Close the modal
                        setTimeout(() => {
                            alert('Comment updated successfully!');
                        }, 100);
                    } else {
                        alert('Error updating comment: ' + data.message);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                });
            } else {
                modal.style.display = 'none'; // Close modal if no changes
            }
        };
    });
});
document.addEventListener('DOMContentLoaded', function () {
    // Prevent click event propagation for filter and sort elements
    document.querySelectorAll('.delete-comment-btn').forEach(button => {
        button.addEventListener('click', function (e) {
            e.stopPropagation();
            const confirmDelete = confirm('Are you sure you want to delete this comment?');
            if (!confirmDelete) {
                // Stop the deletion and prevent any further actions
                e.preventDefault(); // Prevent the default action (deletion)
                return false; // Explicitly stop further propagation
            }
            // If confirmed, allow the deletion to proceed (the default action will be executed)
        });
    });
});

// Close modal functionality
// Close modal functionality
document.querySelectorAll('.close-modal').forEach(closeButton => {
    closeButton.addEventListener('click', function () {
        const modal = this.closest('.modal'); // Find the closest modal container
        if (modal) {
            modal.style.display = 'none'; // Hide the modal
        }
    });
});


    </script>
</body>
</html>