/* ---- RESET/BASIC STYLING ---- */

* {
    padding: 0;
    margin: 0;
    box-sizing: border-box;
    font-family: 'Roboto', sans-serif;
    -ms-overflow-style: none;
    /* IE and Edge */
    scrollbar-width: none;
    /* Firefox */
}

*::-webkit-scrollbar {
    display: none;
}


/* ---- BOARD ---- */

.board {
    width: 100%;
    height: 100vh;
    overflow: hidden;
    background-image: url('/images/KanbanBG.png');
    background-position: center;
    background-size: cover;
    display: flex;
    flex-direction: column;
    align-items: center;
}

#add-lane-btn {
    margin: 20px 0;
    padding: 10px 15px;
    background-color: #4caf50;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 16px;
}

#add-lane-btn:hover {
    background-color: #45a049;
}

.lanes {
    display: flex;
    align-items: flex-start;
    justify-content: start;
    gap: 16px;
    padding: 20px 32px;
    overflow-x: auto;
    width: 100%;
}

.heading {
    font-size: 20px;
    font-weight: bold;
    margin-bottom: 8px;
}

.swim-lane {
    display: flex;
    flex-direction: column;
    gap: 12px;
    background: #f4f4f4;
    box-shadow: 0px 5px 15px rgba(0, 0, 0, 0.2);
    padding: 12px;
    border-radius: 8px;
    width: 225px;
    min-height: 120px;
    flex-shrink: 0;
}

.swim-lane button {
    margin: 5px;
    padding: 8px 12px;
    background-color: #3498db;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
}

.swim-lane button:hover {
    background-color: #2980b9;
}

.swim-lane form {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-top: 10px;
}

.swim-lane input {
    padding: 10px;
    flex-grow: 1;
    border: none;
    box-shadow: 0px 5px 15px rgba(0, 0, 0, 0.2);
    background: white;
    outline: none;
    border-radius: 4px;
}

.task {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: white;
    color: black;
    box-shadow: 0px 5px 15px rgba(0, 0, 0, 0.2);
    padding: 12px;
    border-radius: 8px;
    font-size: 16px;
    margin-bottom: 8px;
    cursor: move;
}

.task-title {
    flex-grow: 1;
}

.task .delete-task-btn {
    margin-left: 10px;
    padding: 6px 10px;
    background-color: #e74c3c;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 12px;
}

.task .delete-task-btn:hover {
    background-color: #c0392b;
}