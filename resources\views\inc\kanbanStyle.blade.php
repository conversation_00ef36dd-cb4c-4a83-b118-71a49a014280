<style>
/* ---- RESET/BASIC STYLING ---- */

* {
    padding: 0;
    margin: 0;
    box-sizing: border-box;
    font-family: '<PERSON>o', sans-serif;
    -ms-overflow-style: none;
    /* IE and Edge */
    scrollbar-width: none;
    /* Firefox */
}

*::-webkit-scrollbar {
    display: none;
}


/* ---- BOARD ---- */

.board {
    width: 100%;
    height: 100vh;
    overflow: hidden;
    background-image: url('/images/KanbanBG.png');
    background-position: center;
    background-size: cover;
    display: flex;
    flex-direction: column;
    align-items: center;
}

.board .button{
    position: relative;
    z-index: 100;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
}

#add-lane-btn {
    margin: 20px 0;
    padding: 10px 15px;
    background-color: #4caf50;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 16px;
}

#add-lane-btn:hover {
    background-color: #45a049;
}

.lanes {
    display: flex;
    align-items: flex-start;
    justify-content: start;
    gap: 16px;
    padding: 20px 32px;
    overflow-x: auto;
    width: 100%;
}

.heading {
    font-size: 20px;
    font-weight: bold;
    margin-bottom: 8px;
}

.swim-lane {
    display: flex;
    flex-direction: column;
    gap: 12px;
    background: #f4f4f4;
    box-shadow: 0px 5px 15px rgba(0, 0, 0, 0.2);
    padding: 12px;
    border-radius: 8px;
    width: 225px;
    min-height: 120px;
    flex-shrink: 0;
}

.swim-lane button {
    margin: 5px;
    padding: 8px 12px;
    background-color: #3498db;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
}

.swim-lane button:hover {
    background-color: #2980b9;
}

.swim-lane form {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-top: 10px;
}

.swim-lane input {
    padding: 10px;
    flex-grow: 1;
    border: none;
    box-shadow: 0px 5px 15px rgba(0, 0, 0, 0.2);
    background: white;
    outline: none;
    border-radius: 4px;
}
.overdue-message {
    margin-top: 10px; 
    color: white;
    background-color:rgb(255, 86, 67);
    padding: 10px;
    width: 100%;
    text-align: center;
    border-radius: 4px;
    box-shadow: 0px 2px 5px rgba(0, 0, 0, 0.2);
}

.task {
    position: relative;
    background: white;
    color: black;
    box-shadow: 0px 5px 15px rgba(0, 0, 0, 0.2);
    padding: 12px;
    border-radius: 8px;
    font-size: 16px;
    margin-bottom: 8px;
    cursor: move;
    width: 100%; /* Ensure the task container takes up full width */
    box-sizing: border-box; /* Prevent padding from affecting the width */
}

.task-title {
    font-weight: bold;
    word-wrap: break-word; /* Allow the title to wrap to the next line */
    overflow-wrap: break-word; /* Break long words to prevent overflow */
    margin-right: 40px; /* Add space to the right for the delete button */
    line-height: 1.2; /* Adjust line height for better readability */
}

.task-comment {
    font-size: 14px;
    color: #333; /* Darker color for better readability */
    line-height: 1.6; /* Slightly increased for improved spacing */
    word-wrap: break-word;
    margin: 8px 0; /* Adds more space between the text and surrounding elements */
}

.comment-date {
    font-size: 12px; /* Slightly smaller than the comment text */
    color: #777; /* Lighter shade to differentiate from the comment text */
    margin-top: 4px; /* Adds space between the comment text and date */
    font-style: italic; /* Makes the date less visually prominent */
}

.comment-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px; /* Adds more space between comments */
    background-color: #f9f9f9; /* Subtle background color for better grouping */
    padding: 10px 12px; /* Adds padding for a more comfortable reading area */
    border-radius: 8px; /* Rounded corners for a cleaner look */
    box-shadow: 0px 2px 5px rgba(0, 0, 0, 0.1); /* Subtle shadow for better separation */
    border: 1px solid #ddd; /* Adds a light border for structure */
}

.comment-container:hover {
    background-color: #f0f0f0; 
}

button.edit-comment-btn {
    background-color: rgb(255, 208, 0);
    border: none;
    color: white;
    cursor: pointer;
    font-size: 12px; 
    padding: 6px 10px;
    border-radius: 4px;
}

button.edit-comment-btn:hover {
    background-color: rgb(211, 172, 0);
}

button.delete-comment-btn {
    background-color: rgb(255, 85, 0);
    border: none;
    color: white;
    cursor: pointer;
    font-size: 12px; 
    padding: 6px 10px;
    border-radius: 4px;
    margin-top:-5px;
}

button.delete-comment-btn:hover {
    background-color: rgb(146, 49, 0);
}

.comment-wrapper {
    margin-bottom: 30px; 
}

.task .delete-task-btn {
    position: absolute; /* Positions the button relative to the .task container */
    top: 8px; /* Distance from the top */
    right: 8px; /* Distance from the right */
    padding: 6px 10px;
    background-color: #e74c3c;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 12px;
}

.task .delete-task-btn:hover {
    background-color: #c0392b;
}
.modal {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.5);
        display: none;
        justify-content: center;
        align-items: center;
        z-index: 1000; /* Ensure the modal appears above other content */
    }

    .modal-content {
        background-color: white;
        padding: 20px;
        border-radius: 8px;
        width: 50%;
        box-shadow: 0 0 15px rgba(0, 0, 0, 0.3);
        position: relative;
        z-index: 1010; /* Higher than other elements in the modal */
    }

    .close-modal {
        position: absolute;
        top: 10px;
        right: 15px;
        font-size: 20px;
        cursor: pointer;
        z-index: 1020; /* Ensures the close button is on top */
    }

.save-comment-btn {
    margin-top: 10px;
    background-color: #3498db;
    color: white;
    border: none;
    padding: 10px 20px;
    cursor: pointer;
    border-radius: 4px;
}

.save-comment-btn:hover {
    background-color: #0056b3;
}
.hidden-comment {
    display: none;
}

button.show-more-btn ,button.show-less-btn {
    margin-top: 10px;
    background-color:rgb(0, 216, 83);
    color: white;
    border: none;
    padding: 8px 12px;
    cursor: pointer;
    border-radius: 4px;
    font-size: 14px;
}

button.show-more-btn:hover,button.show-less-btn:hover {
    background-color:rgb(0, 139, 53);
}
button.delete-comment-btn {
    background-color: rgb(255, 85, 0);
    border: none;
    color: white;
    cursor: pointer;
    font-size: 12px; 
    padding: 6px 10px;
    border-radius: 4px;
    margin-top:-5px;
}
button.delete-comment-btn:hover {
    background-color: rgb(146, 49, 0);
}
</style>