{"__meta": {"id": "X50c70f757084543f9f4a029329c55017", "datetime": "2025-06-25 19:13:19", "utime": 1750849999.029502, "method": "GET", "uri": "/home", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 1, "messages": [{"message": "[19:13:18] LOG.warning: Callables of the form [\"Swift_SmtpTransport\", \"Swift_Transport_EsmtpTransport::__construct\"] are deprecated in C:\\xampp\\htdocs\\SAgilePMT_UTM-1\\vendor\\swiftmailer\\swiftmailer\\lib\\classes\\Swift\\SmtpTransport.php on line 36", "message_html": null, "is_string": false, "label": "warning", "time": 1750849998.751183, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1750849998.267492, "end": 1750849999.029523, "duration": 0.762030839920044, "duration_str": "762ms", "measures": [{"label": "Booting", "start": 1750849998.267492, "relative_start": 0, "end": 1750849998.718074, "relative_end": 1750849998.718074, "duration": 0.45058202743530273, "duration_str": "451ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}, {"label": "Application", "start": 1750849998.71809, "relative_start": 0.45059800148010254, "end": 1750849999.029525, "relative_end": 2.1457672119140625e-06, "duration": 0.3114349842071533, "duration_str": "311ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}]}, "memory": {"peak_usage": 24901944, "peak_usage_str": "24MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 16, "templates": [{"name": "home (\\resources\\views\\home.blade.php)", "param_count": 3, "params": ["pros", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bugChartData"], "type": "blade"}, {"name": "inc.success (\\resources\\views\\inc\\success.blade.php)", "param_count": 7, "params": ["__env", "app", "menuData", "errors", "pros", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bugChartData"], "type": "blade"}, {"name": "inc.dashboard (\\resources\\views\\inc\\dashboard.blade.php)", "param_count": 7, "params": ["__env", "app", "menuData", "errors", "pros", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bugChartData"], "type": "blade"}, {"name": "inc.navbar (\\resources\\views\\inc\\navbar.blade.php)", "param_count": 7, "params": ["__env", "app", "menuData", "errors", "pros", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bugChartData"], "type": "blade"}, {"name": "layouts.app3 (\\resources\\views\\layouts\\app3.blade.php)", "param_count": 7, "params": ["__env", "app", "menuData", "errors", "pros", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bugChartData"], "type": "blade"}, {"name": "inc.apex-charts-css (\\resources\\views\\inc\\apex-charts-css.blade.php)", "param_count": 7, "params": ["__env", "app", "menuData", "errors", "pros", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bugChartData"], "type": "blade"}, {"name": "inc.apex-charts-js (\\resources\\views\\inc\\apex-charts-js.blade.php)", "param_count": 7, "params": ["__env", "app", "menuData", "errors", "pros", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bugChartData"], "type": "blade"}, {"name": "inc.dashboard-analytics-js (\\resources\\views\\inc\\dashboard-analytics-js.blade.php)", "param_count": 7, "params": ["__env", "app", "menuData", "errors", "pros", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bugChartData"], "type": "blade"}, {"name": "layouts.contentNavbarLayout (\\resources\\views\\layouts\\contentNavbarLayout.blade.php)", "param_count": 7, "params": ["__env", "app", "menuData", "errors", "pros", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bugChartData"], "type": "blade"}, {"name": "layouts.sections.menu.verticalMenu (\\resources\\views\\layouts\\sections\\menu\\verticalMenu.blade.php)", "param_count": 15, "params": ["__env", "app", "menuData", "errors", "pros", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bugChartData", "contentNavbar", "containerNav", "isNavbar", "isMenu", "isFlex", "<PERSON><PERSON>ooter", "navbarDetached", "container"], "type": "blade"}, {"name": "layouts.sections.navbar.navbar (\\resources\\views\\layouts\\sections\\navbar\\navbar.blade.php)", "param_count": 15, "params": ["__env", "app", "menuData", "errors", "pros", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bugChartData", "contentNavbar", "containerNav", "isNavbar", "isMenu", "isFlex", "<PERSON><PERSON>ooter", "navbarDetached", "container"], "type": "blade"}, {"name": "layouts.sections.footer.footer (\\resources\\views\\layouts\\sections\\footer\\footer.blade.php)", "param_count": 15, "params": ["__env", "app", "menuData", "errors", "pros", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bugChartData", "contentNavbar", "containerNav", "isNavbar", "isMenu", "isFlex", "<PERSON><PERSON>ooter", "navbarDetached", "container"], "type": "blade"}, {"name": "layouts.commonMaster (\\resources\\views\\layouts\\commonMaster.blade.php)", "param_count": 15, "params": ["__env", "app", "menuData", "errors", "pros", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bugChartData", "contentNavbar", "containerNav", "isNavbar", "isMenu", "isFlex", "<PERSON><PERSON>ooter", "navbarDetached", "container"], "type": "blade"}, {"name": "layouts.sections.styles (\\resources\\views\\layouts\\sections\\styles.blade.php)", "param_count": 15, "params": ["__env", "app", "menuData", "errors", "pros", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bugChartData", "contentNavbar", "containerNav", "isNavbar", "isMenu", "isFlex", "<PERSON><PERSON>ooter", "navbarDetached", "container"], "type": "blade"}, {"name": "layouts.sections.scriptsIncludes (\\resources\\views\\layouts\\sections\\scriptsIncludes.blade.php)", "param_count": 15, "params": ["__env", "app", "menuData", "errors", "pros", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bugChartData", "contentNavbar", "containerNav", "isNavbar", "isMenu", "isFlex", "<PERSON><PERSON>ooter", "navbarDetached", "container"], "type": "blade"}, {"name": "layouts.sections.scripts (\\resources\\views\\layouts\\sections\\scripts.blade.php)", "param_count": 15, "params": ["__env", "app", "menuData", "errors", "pros", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bugChartData", "contentNavbar", "containerNav", "isNavbar", "isMenu", "isFlex", "<PERSON><PERSON>ooter", "navbarDetached", "container"], "type": "blade"}]}, "route": {"uri": "GET home", "middleware": "web", "controller": "App\\Http\\Controllers\\HomeController@index", "namespace": "App\\Http\\Controllers", "prefix": "", "where": [], "as": "home", "file": "<a href=\"phpstorm://open?file=C:\\xampp\\htdocs\\SAgilePMT_UTM-1\\app\\Http\\Controllers\\HomeController.php&line=27\">\\app\\Http\\Controllers\\HomeController.php:27-133</a>"}, "queries": {"nb_statements": 9, "nb_failed_statements": 0, "accumulated_duration": 0.008549999999999999, "accumulated_duration_str": "8.55ms", "statements": [{"sql": "select * from `users` where `id` = 7 limit 1", "type": "query", "params": [], "bindings": ["7"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 53}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 18, "namespace": "middleware", "name": "auth", "line": 63}, {"index": 19, "namespace": "middleware", "name": "auth", "line": 42}], "duration": 0.00524, "duration_str": "5.24ms", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php:53", "connection": "kanban", "start_percent": 0, "width_percent": 61.287}, {"sql": "select `team_name` from `teammappings` where `username` = 'ivlyn'", "type": "query", "params": [], "bindings": ["ivlyn"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\app\\Http\\Controllers\\HomeController.php", "line": 33}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00039, "duration_str": "390μs", "stmt_id": "\\app\\Http\\Controllers\\HomeController.php:33", "connection": "kanban", "start_percent": 61.287, "width_percent": 4.561}, {"sql": "select * from `projects` where `team_name` in ('999', 'iv<PERSON>\\'s team')", "type": "query", "params": [], "bindings": ["999", "ivlyn&#039;s team"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\HomeController.php", "line": 36}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.0005600000000000001, "duration_str": "560μs", "stmt_id": "\\app\\Http\\Controllers\\HomeController.php:36", "connection": "kanban", "start_percent": 65.848, "width_percent": 6.55}, {"sql": "select * from `sprint` where `proj_name` = 'SAgile'", "type": "query", "params": [], "bindings": ["SAgile"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\HomeController.php", "line": 55}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00039, "duration_str": "390μs", "stmt_id": "\\app\\Http\\Controllers\\HomeController.php:55", "connection": "kanban", "start_percent": 72.398, "width_percent": 4.561}, {"sql": "select * from `sprint` where `proj_name` = 'web tech'", "type": "query", "params": [], "bindings": ["web tech"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\HomeController.php", "line": 55}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00039, "duration_str": "390μs", "stmt_id": "\\app\\Http\\Controllers\\HomeController.php:55", "connection": "kanban", "start_percent": 76.959, "width_percent": 4.561}, {"sql": "select * from `sprint` where `proj_name` = 'test2'", "type": "query", "params": [], "bindings": ["test2"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\HomeController.php", "line": 55}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00039, "duration_str": "390μs", "stmt_id": "\\app\\Http\\Controllers\\HomeController.php:55", "connection": "kanban", "start_percent": 81.52, "width_percent": 4.561}, {"sql": "select * from `calendar`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Controllers\\HomeController.php", "line": 77}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00041, "duration_str": "410μs", "stmt_id": "\\app\\Http\\Controllers\\HomeController.php:77", "connection": "kanban", "start_percent": 86.082, "width_percent": 4.795}, {"sql": "select `status`, COUNT(*) as count from `bugtrack` group by `status`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\HomeController.php", "line": 107}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00043, "duration_str": "430μs", "stmt_id": "\\app\\Http\\Controllers\\HomeController.php:107", "connection": "kanban", "start_percent": 90.877, "width_percent": 5.029}, {"sql": "select `severity`, COUNT(*) as count from `bugtrack` group by `severity`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\HomeController.php", "line": 113}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00035, "duration_str": "350μs", "stmt_id": "\\app\\Http\\Controllers\\HomeController.php:113", "connection": "kanban", "start_percent": 95.906, "width_percent": 4.094}]}, "models": {"data": {"App\\Calendar": 2, "App\\Sprint": 4, "App\\Project": 3, "App\\User": 1}, "count": 10}, "swiftmailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "aTj1X4I0RaN2yculSQ2EOvLTsaaci2d8EUFsmYNZ", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/home\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "7", "auth": "array:1 [\n  \"password_confirmed_at\" => **********\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/home", "status_code": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-1260821232 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1260821232\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-680142975 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-680142975\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-434088856 data-indent-pad=\"  \"><span class=sf-dump-note>array:17</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"27 characters\">http://127.0.0.1:8000/login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"68 characters\">en-US,en-GB;q=0.9,en;q=0.8,ms;q=0.7,zh-GB;q=0.6,zh;q=0.5,zh-CN;q=0.4</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6IllWdENrYkRIT2dZdzVnTmhwVCtjRFE9PSIsInZhbHVlIjoiYmloUGRkMG5vZjd4Vk0wS1dQT0xRd1hBbkFHMnRXbE9HMU9aWGtveXdQR0VtMVQrZUlQNm9HTlVNU00zQTdRMEVJZGxQek5vZHh6aysyOTczd3RPTDk4NnMweTc0V3JBTlN3RVdWRXh4VDZjUlloSkpWZ0VhMmFTMnAySXFLRG4iLCJtYWMiOiJkZGFhMzFmMDk4OWFhZDgxNjcwODNmNzEwMGUzYjZhNWUxMDMyM2Y3MmM5ZGJmYTc3NWQxZjRiZmI2MjhkOGMyIiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6InEvZTNYMTE4T0Y2VFR5YTFCMGRNNmc9PSIsInZhbHVlIjoiK0lIUXZKbkdPd20zd0p6SDZoU0IxYmMrZ0ROYy9MeEluekVENWwxaFJBUVgrS0VJbHBMNGR0cnJQcVBEMWMvRjVvcjJBekRhN083d05tQTgvcTZNbG12M01wOTNWcmpvRWNHWlBmUzQ4aUhsQlJOQTFxZGphVUh4RDRuZG1ZcXMiLCJtYWMiOiI3ZWFiZmI4OGU3OTNjYjg4MGYxMjYwOGRlMzI5ZDFlMzZiZGM0MjE1YWUyOTczYjE4ZTBlNDFlYjg5OWQ0NGQzIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-434088856\", {\"maxDepth\":0})</script>\n", "request_server": "<pre class=sf-dump id=sf-dump-1816367614 data-indent-pad=\"  \"><span class=sf-dump-note>array:32</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"38 characters\">C:\\xampp\\htdocs\\SAgilePMT_UTM-1\\public</span>\"\n  \"<span class=sf-dump-key>REMOTE_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>REMOTE_PORT</span>\" => \"<span class=sf-dump-str title=\"5 characters\">57499</span>\"\n  \"<span class=sf-dump-key>SERVER_SOFTWARE</span>\" => \"<span class=sf-dump-str title=\"29 characters\">PHP 8.2.12 Development Server</span>\"\n  \"<span class=sf-dump-key>SERVER_PROTOCOL</span>\" => \"<span class=sf-dump-str title=\"8 characters\">HTTP/1.1</span>\"\n  \"<span class=sf-dump-key>SERVER_NAME</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PORT</span>\" => \"<span class=sf-dump-str title=\"4 characters\">8000</span>\"\n  \"<span class=sf-dump-key>REQUEST_URI</span>\" => \"<span class=sf-dump-str title=\"5 characters\">/home</span>\"\n  \"<span class=sf-dump-key>REQUEST_METHOD</span>\" => \"<span class=sf-dump-str title=\"3 characters\">GET</span>\"\n  \"<span class=sf-dump-key>SCRIPT_NAME</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>SCRIPT_FILENAME</span>\" => \"<span class=sf-dump-str title=\"48 characters\">C:\\xampp\\htdocs\\SAgilePMT_UTM-1\\public\\index.php</span>\"\n  \"<span class=sf-dump-key>PATH_INFO</span>\" => \"<span class=sf-dump-str title=\"5 characters\">/home</span>\"\n  \"<span class=sf-dump-key>PHP_SELF</span>\" => \"<span class=sf-dump-str title=\"15 characters\">/index.php/home</span>\"\n  \"<span class=sf-dump-key>HTTP_HOST</span>\" => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  \"<span class=sf-dump-key>HTTP_CONNECTION</span>\" => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  \"<span class=sf-dump-key>HTTP_CACHE_CONTROL</span>\" => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA</span>\" => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_MOBILE</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_PLATFORM</span>\" => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_UPGRADE_INSECURE_REQUESTS</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>HTTP_USER_AGENT</span>\" => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT</span>\" => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_SITE</span>\" => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_MODE</span>\" => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_USER</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_DEST</span>\" => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  \"<span class=sf-dump-key>HTTP_REFERER</span>\" => \"<span class=sf-dump-str title=\"27 characters\">http://127.0.0.1:8000/login</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_ENCODING</span>\" => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_LANGUAGE</span>\" => \"<span class=sf-dump-str title=\"68 characters\">en-US,en-GB;q=0.9,en;q=0.8,ms;q=0.7,zh-GB;q=0.6,zh;q=0.5,zh-CN;q=0.4</span>\"\n  \"<span class=sf-dump-key>HTTP_COOKIE</span>\" => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6IllWdENrYkRIT2dZdzVnTmhwVCtjRFE9PSIsInZhbHVlIjoiYmloUGRkMG5vZjd4Vk0wS1dQT0xRd1hBbkFHMnRXbE9HMU9aWGtveXdQR0VtMVQrZUlQNm9HTlVNU00zQTdRMEVJZGxQek5vZHh6aysyOTczd3RPTDk4NnMweTc0V3JBTlN3RVdWRXh4VDZjUlloSkpWZ0VhMmFTMnAySXFLRG4iLCJtYWMiOiJkZGFhMzFmMDk4OWFhZDgxNjcwODNmNzEwMGUzYjZhNWUxMDMyM2Y3MmM5ZGJmYTc3NWQxZjRiZmI2MjhkOGMyIiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6InEvZTNYMTE4T0Y2VFR5YTFCMGRNNmc9PSIsInZhbHVlIjoiK0lIUXZKbkdPd20zd0p6SDZoU0IxYmMrZ0ROYy9MeEluekVENWwxaFJBUVgrS0VJbHBMNGR0cnJQcVBEMWMvRjVvcjJBekRhN083d05tQTgvcTZNbG12M01wOTNWcmpvRWNHWlBmUzQ4aUhsQlJOQTFxZGphVUh4RDRuZG1ZcXMiLCJtYWMiOiI3ZWFiZmI4OGU3OTNjYjg4MGYxMjYwOGRlMzI5ZDFlMzZiZGM0MjE1YWUyOTczYjE4ZTBlNDFlYjg5OWQ0NGQzIiwidGFnIjoiIn0%3D</span>\"\n  \"<span class=sf-dump-key>REQUEST_TIME_FLOAT</span>\" => <span class=sf-dump-num>1750849998.2675</span>\n  \"<span class=sf-dump-key>REQUEST_TIME</span>\" => <span class=sf-dump-num>1750849998</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1816367614\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1186356817 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">aTj1X4I0RaN2yculSQ2EOvLTsaaci2d8EUFsmYNZ</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">MYF7oMYgsbPPXDsp4s3xDKc21smzF5HotROtRpDs</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1186356817\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-789040463 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 25 Jun 2025 11:13:18 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6IldyOXZ5MHlsZnlBV1ZIK0Y4NXhtQXc9PSIsInZhbHVlIjoiNHEyYlpGVzlxSEZWbjZiTXdLQ1pjVFJMS1hvVzJLcUFZWGtrNFF6dHdZMDMzdzBacjdoNks4ZkV4czI3N29icUl6dE1QcUJjT21LRjhGSGNua213RWYzUDNhV0w2R01KQm0xaWNDVGQ2OWdLRGFYV2dJRWNialdtVGE3RjNSNHciLCJtYWMiOiIwN2ZmYzdmNWZlMmFjNTJkYzA3Mjk2MmRlZDBhNjM2YzcwYzdhMmFlYTM1NjUxOTM2ZDk2ZWUwZWU0NzNlZmRhIiwidGFnIjoiIn0%3D; expires=Wed, 25-Jun-2025 13:13:19 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"443 characters\">laravel_session=eyJpdiI6IjErOGtLbzFZWktLRjhISDRuem15OHc9PSIsInZhbHVlIjoiUzBzQVZFM1MwMHZ4QXJ2U21nd0pIZjFLSE9OVkhkR0IyeFNFblFib3BXcjA1dkN5YzVxL1dOWlRUZnpNdlpjdWJsOTQyaUZOdkJ5YWVJN3QvYnVhWXJUSDgvVno5L2ovM2hERkhNMnZBMW1xRkYrcy94Q3ZwVmNKbUR6RTUxQUUiLCJtYWMiOiI2NzEwOGNjMTg4NTIwMjQ5OTEzOTdmOTIwOWNjYWRhMjcxZjFjNjI5NTlkNzUzYjBhZDFiNTFlM2I4NWZkMjRlIiwidGFnIjoiIn0%3D; expires=Wed, 25-Jun-2025 13:13:19 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6IldyOXZ5MHlsZnlBV1ZIK0Y4NXhtQXc9PSIsInZhbHVlIjoiNHEyYlpGVzlxSEZWbjZiTXdLQ1pjVFJMS1hvVzJLcUFZWGtrNFF6dHdZMDMzdzBacjdoNks4ZkV4czI3N29icUl6dE1QcUJjT21LRjhGSGNua213RWYzUDNhV0w2R01KQm0xaWNDVGQ2OWdLRGFYV2dJRWNialdtVGE3RjNSNHciLCJtYWMiOiIwN2ZmYzdmNWZlMmFjNTJkYzA3Mjk2MmRlZDBhNjM2YzcwYzdhMmFlYTM1NjUxOTM2ZDk2ZWUwZWU0NzNlZmRhIiwidGFnIjoiIn0%3D; expires=Wed, 25-Jun-2025 13:13:19 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"415 characters\">laravel_session=eyJpdiI6IjErOGtLbzFZWktLRjhISDRuem15OHc9PSIsInZhbHVlIjoiUzBzQVZFM1MwMHZ4QXJ2U21nd0pIZjFLSE9OVkhkR0IyeFNFblFib3BXcjA1dkN5YzVxL1dOWlRUZnpNdlpjdWJsOTQyaUZOdkJ5YWVJN3QvYnVhWXJUSDgvVno5L2ovM2hERkhNMnZBMW1xRkYrcy94Q3ZwVmNKbUR6RTUxQUUiLCJtYWMiOiI2NzEwOGNjMTg4NTIwMjQ5OTEzOTdmOTIwOWNjYWRhMjcxZjFjNjI5NTlkNzUzYjBhZDFiNTFlM2I4NWZkMjRlIiwidGFnIjoiIn0%3D; expires=Wed, 25-Jun-2025 13:13:19 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-789040463\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1050302204 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">aTj1X4I0RaN2yculSQ2EOvLTsaaci2d8EUFsmYNZ</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"26 characters\">http://127.0.0.1:8000/home</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>7</span>\n  \"<span class=sf-dump-key>auth</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>password_confirmed_at</span>\" => <span class=sf-dump-num>**********</span>\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1050302204\", {\"maxDepth\":0})</script>\n"}}