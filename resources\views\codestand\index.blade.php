<!--Coding Standard Index-->

@extends('layouts.app2')
@include('inc.success')
@include('inc.style')

@include('inc.dashboard')
@include('inc.navbar')

@section('content')
@include('inc.title')
<br>
  <table>
  <tr>
      <th>Coding Standard</th>
      <th>Edit</th>
      <th>Delete</th>
      
  </tr>

  @forelse($codestands as $codestand)
  <tr> 
      <th>
        {{ $codestand->codestand_name }}
      </th>

      <th>
          <a href="{{route('codestand.edit', $codestand)}}" class="btn btn-secondary">Edit</a>
      </th>

      <th>
          <a href="{{route('codestand.destroy', $codestand)}}" class="btn btn-danger" onclick="return confirm('Are you sure you want to delete this Coding Standard?');">Delete</a>
      </th>

  </tr>
  @empty
  <tr>
    <td colspan="3">No coding standard added yet</td>
  </tr>

  @endforelse
  </table>
  <br><br><br>

  <a href="{{route('codestand.create')}}" class="btn btn-success">Add Coding Standard</a>

@endsection