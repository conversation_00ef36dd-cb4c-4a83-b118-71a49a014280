@extends('layouts.app2')
@include('inc.success')
@include('inc.style')

@section('navbar')
  @include('inc.navbar')
@endsection

@section('content')
@include('inc.title')

<br>
@if ($errors->any())
    <div class="alert alert-danger">
        <ul>
            @foreach ($errors->all() as $error)
                <li>{{ $error }}</li>
            @endforeach
        </ul>
    </div>
@endif
<script>
    function confirmAction(event) {
        event.preventDefault();
        if (confirm("Are you sure you want to proceed?")) {
            event.target.closest('form').submit();
        }
    }
</script>
@if ($user->user_type == 1 && $nfrs->created_by == $user->id) 
<form action="{{ route('nfr.update', ['general_nfr_id' => $nfrs->general_nfr_id]) }}" method="post" enctype="multipart/form-data" onsubmit="confirmAction(event)">
    @csrf
    @method('PUT')
    <br><br>
        <label for="general_nfr">General Requirement:</label>
        <input type="text" name="general_nfr"  class="form-control" value="{{ $nfrs->general_nfr }}" required>
        <br><br>
        <label for="general_nfr_desc">Description:</label>
        <input type="text" name="general_nfr_desc"  class="form-control" value="{{ $nfrs->general_nfr_desc }}">
        <br><br>

    <button type="submit" class="btn btn-success">Update</button>
    <a href="{{ route('nfr.index') }}" class="btn btn-secondary">Cancel</a>
</form>
@else
<div>You do not have access to this page.</div>
@endif


@endsection