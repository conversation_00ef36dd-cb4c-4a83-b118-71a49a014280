<?php

namespace App;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class GeneralNFR extends Model
{
    use HasFactory;
    
    protected $table = 'generalnfr';
    protected $primaryKey = 'general_nfr_id'; // Use the correct primary key name if it's not 'id'
    
    public $incrementing = true; // Only set to false if the primary key is non-numeric
    protected $keyType = 'int'; // Use 'string' if the primary key is non-numeric

    protected $fillable = [
        'general_nfr',
        'general_nfr_desc',
        'created_by'
    ];

    public function userStories()
    {
        return $this->belongsToMany(UserStory::class);
    }

    public function users()
    {
        return $this->belongsToMany(User::class)->withPivot('secfeature_access');
    }

    public static function findGeneralNFRById($id)
    {
        return self::where('general_nfr_id', $id)->first();
    }

    public static function getAllGeneralNFRNameAndId()
    {
        $nfrs = self::all();
        $names = $nfrs->pluck('general_nfr')->toArray();
        $ids = $nfrs->pluck('general_nfr_id')->toArray();
        return compact('names', 'ids');
    }

    public static function getGeneralNFRList()
    {
        return self::pluck('general_nfr', 'general_nfr_id');
    }

}
