@extends('layouts.app2')
@include('inc.style')
@include('inc.navbar')

@section('content')
@include('inc.title')
<div class="container mt-5">
    <form action="{{ route('tvt.store') }}" method="post" enctype="multipart/form-data">
        @csrf
        <input type="hidden" name="proj_id" value="{{ $proj_id }}">

        <!-- Sprint Dropdown -->
        <div class="form-group row">
            <label for="sprint" class="col-sm-2 col-form-label">Sprint:</label>
            <div class="col-sm-10">
                <select name="sprint" id="sprint" class="form-control">
                    <option value="">Select Sprint</option>
                    @foreach($sprints as $sprint)
                        <option value="{{ $sprint->sprint_id }}">{{ $sprint->sprint_name }}</option>
                    @endforeach
                </select>
            </div>
        </div>
        
        <!-- Backlog Panel -->
        <div class="form-group row mt-3">
            <label for="backlog_panel" class="col-sm-2 col-form-label">Backlog Panel:</label>
            <div class="col-sm-10">
                <input type="text" name="backlog_panel" id="backlog_panel" class="form-control" readonly>
            </div>
        </div>

        <!-- User Story Panel -->
        <div class="form-group row mt-3">
            <label for="user_story_panel" class="col-sm-2 col-form-label">User Story Panel:</label>
            <div class="col-sm-10">
                <select name="user_story_panel" id="user_story_panel" class="form-control">
                    <option value="">Select User Story</option>
                </select>
            </div>
        </div>

        <!-- QAW Dropdown -->
        <div class="form-group row mt-3">
            <label for="qaw" class="col-sm-2 col-form-label">QAW:</label>
            <div class="col-sm-10">
                <select name="qaw" id="qaw" class="form-control">
                    <option value="">Select QAW</option>
                </select>
            </div>
        </div>

        <!-- FR Input with dynamic width based on text length -->
        <div class="form-group row mt-3">
            <label for="fr" class="col-sm-2 col-form-label">FR:</label>
            <div class="col-sm-10">
                <textarea name="fr" id="fr" class="form-control" readonly style="min-height: 40px; resize: none;"></textarea>
            </div>
        </div>

        <!-- QAW Dropdown -->
        <div class="form-group row mt-3">
            <label for="qaw" class="col-sm-2 col-form-label">QAW:</label>
            <div class="col-sm-10">
                <select name="qaw" id="qaw" class="form-control">
                    <option value="">Select QAW</option>
                </select>
            </div>
        </div>

        <!-- NFR Dropdown -->
        <div class="form-group row mt-3">
            <label for="nfr" class="col-sm-2 col-form-label">NFR:</label>
            <div class="col-sm-10">
                <select name="nfr" class="form-control">
                    @foreach($nfrs as $nfr)
                        <option value="{{ $nfr }}">{{ $nfr }}</option>
                    @endforeach
                </select>
            </div>
        </div>

        <!-- Submit Button -->
        <div class="form-group row mt-4">
            <div class="col-sm-10 offset-sm-2">
                <button type="submit" class="btn btn-primary">Submit</button>
            </div>
        </div>
    </form>
</div>

<!-- Link to app.js -->
<script src="{{ asset('js/app2.js') }}"></script>
@endsection