<!DOCTYPE html>
<html>
<head>
    <title>New Comment Notification</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background-color: #f9f9f9;
        }
        h1 {
            color: #444;
            font-size: 24px;
        }
        .details {
            margin: 15px 0;
        }
        .details p {
            margin: 8px 0;
        }
        .footer {
            text-align: center;
            margin-top: 20px;
            font-size: 12px;
            color: #777;
        }
        .footer a {
            color: #007BFF;
            text-decoration: none;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>{{ $subject }}</h1>
        <div class="details">
            <p><strong>Project:</strong> {{ $project->proj_name }}</p>
            <p><strong>Sprint:</strong> {{ $sprint->sprint_name }}</p>
            <p><strong>Task:</strong> {{ $task->title }}</p>
            <p><strong>Comment:</strong> {{ $comment->comment }}</p>
            <p><strong>Created By:</strong> {{ $comment->created_by }}</p>
            <p><strong>Assigned To:</strong> 
                @php
                    $assignedTo = json_decode($comment->assigned_to, true);
                    $assignedToList = is_array($assignedTo) ? implode(', ', $assignedTo) : $comment->assigned_to;
                @endphp
                {{ $assignedToList }}
            </p>

            <p><strong>Created At:</strong> {{ \Carbon\Carbon::parse($comment->created_at)->format('Y-m-d H:i:s') }}</p>
            <a href="{{ url('/tasks/' . $task->id . '/comment/'.$comment->id ) }}" class="btn">View Task Comments</a>
        </div>
        <p>If you have any questions, please contact your project manager.</p>
        <div class="footer">
            <p>© {{ now()->year }} SAgile Developement Team. All rights reserved.</p>
            <p>This is an automated message. Please do not reply.</p>
            <p><a href="{{ url('/') }}">Visit Dashboard</a></p>
        </div>
    </div>
</body>
</html>
