<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\GeneralNFR;
use App\SpecificNFR;
use App\Project;
use App\TeamMapping;
use App\Sprint;
use App\UserStoryGeneralNfr;
use Illuminate\Support\Facades\Auth;
use Illuminate\Validation\Rule;
use Illuminate\Support\Facades\DB;

class GeneralNFRController extends Controller
{
    /**
     * Display a listing of NFRs.
     */
    public function index(Request $request)
{
    $user = Auth::user();

    // Retrieve the user's role(s) from the teammapping table
    $roles = TeamMapping::where('username', $user->username)->pluck('role_name')->toArray();

    // Access control check
    $isAdmin = $user->user_type == 1;
    $isProjectManager = TeamMapping::where('username', $user->username)
        ->get()
        ->contains(function ($mapping) {
            return $mapping->isProjectManager();
        });

    if (!($isAdmin || $isProjectManager)) {
        return redirect()->route('home')->with('error', 'Only Admins and Project Managers can access the NFR Management.');
    }

    // Get all General NFRs with pagination (8 per page)
    $generalNFR = GeneralNFR::paginate(8);

    return view('nfr.index', compact('generalNFR', 'roles', 'user'))
        ->with('title', 'General NFR List');
}

    public function viewGeneral(Request $request, $general_nfr_id)
    {
        $user = Auth::user();
        $isAdmin = $user->user_type == 1;
        $isProjectManager = TeamMapping::where('username', $user->username)
        ->get()
        ->contains(function ($mapping) {
            return $mapping->isProjectManager();
        });

        if (!($isAdmin || $isProjectManager)) {
            return redirect()->route('home')->with('error', 'Only Admins and Project Managers can access the NFR Management.');
        }
        // Get the user's projects
        if ($isAdmin) {
            $pro = Project::all();
        } else {
            $pro = TeamMapping::getProjectsByUser($user->username);
        }
        //$projectIds = Project::getProjectIDs($pro); //for filtering

        // Get the General NFR
        $generalNFR = GeneralNFR::findGeneralNFRById($general_nfr_id);

        if (!$generalNFR) {
            return redirect()->route('nfr.index')->with('error', 'General NFR not found.');
        }

        // Project and Sprint filters from the request
        $projectFilter = request('project_filter');
        $sprintFilter = request('sprint_filter');

        // Filter linked user stories with additional details
        $linkedUserStories = UserStoryGeneralNfr::getLinkedUserStoriesByNFR(
        $general_nfr_id,
        'general',
        $projectFilter,
        $sprintFilter,
    );

        // Fetch sprints based on role
        $sprints = Sprint::getFilteredSprints($isAdmin, $projectFilter, $pro);

        return view('nfr.viewGeneral', compact('generalNFR', 'pro','user', 'linkedUserStories', 'sprints'))
            ->with('title', 'General NFR Details - ' . $generalNFR->general_nfr)
            ->with('pros', $pro);
    }


    /**
     * Show the form for creating a new NFR.
     */
    public function create()
    {
        $user = Auth::user();
        
        if (!$user->user_type == 1) {
         return redirect()->route('home')->with('error', 'Only Admins can create NFR.');
        }else {
            return view('nfr.create', compact('user'))->with('title', 'Create General NFR');
        }
    }

    /**
     * Store a newly created NFR in storage.
     */
    public function store(Request $request)
    {
        $request->validate([
            'general_nfr' => [
                'required',
                'string',
                'max:255',
                'unique:generalnfr,general_nfr',
                'regex:/^[a-zA-Z\s]+$/', // Only alphabetic characters and spaces are allowed
            ],
            'general_nfr_desc' => 'nullable|string|max:255',
        ], [
            'general_nfr.unique' => 'This general NFR already exists.',
            'general_nfr.required' => 'The general NFR field is required.',
            'general_nfr.regex' => 'The general NFR field must contain only alphabetic characters.',
        ]);

        $nfr = new GeneralNFR();
        $nfr->general_nfr = $request->general_nfr;
        $nfr->general_nfr_desc = $request->general_nfr_desc;
        $nfr->created_at = now();
        $nfr->created_by = Auth::user()->id;
        $nfr->save();

        SpecificNFR::create([
            'general_nfr_id' => $nfr->general_nfr_id,
            'specific_nfr' =>  $request->general_nfr,
            'specific_nfr_desc' => $request->general_nfr_desc,
            'created_by' => Auth::user()->id,
            'created_at' => now(),
            'updated_at' => now(),
        ]);

        return redirect()->route('nfr.index')
            ->with('success', 'General NFR created successfully.');
    }


    /**
     * Show the form for editing the NFR.
     */
    public function edit($id)
    {
        $user = Auth::user();
        $isAdmin = $user->user_type == 1;
        if (!$isAdmin) {
            return redirect()->route('home')->with('error', 'Only Admins can edit NFR.');
        }
            // Get the NFR that has the same general_nfr_id as the provided ID
            $nfrs = GeneralNFR::findGeneralNFRById($id);
            if (!$nfrs) {
                return redirect()->route('nfr.index')->with('error', 'NFR not found');
            }
            return view('nfr.edit', compact('nfrs','user'))
                ->with('title', 'Edit General NFR');
    }

    /**
     * Update the NFR in storage.
     */
    public function update(Request $request, $id)
    {   
        $request->validate([
            'general_nfr' => [
                'required',
                'string',
                'max:255',
                'regex:/^[a-zA-Z\s]+$/', // Alphabetic characters only
                Rule::unique('generalnfr')->ignore($id, 'general_nfr_id'),
            ],
            'general_nfr_desc' => 'nullable|string|max:255',
        ], [
            'general_nfr.unique' => 'This general NFR already exists.',
            'general_nfr.required' => 'The general NFR field is required.',
            'general_nfr.regex' => 'The general NFR field must contain only alphabetic characters.',
        ]);

        $nfr = GeneralNFR::findGeneralNFRById($id);
        $nfr->general_nfr = $request->general_nfr;
        $nfr->general_nfr_desc = $request->general_nfr_desc;
        $nfr->updated_at = now();
        $nfr->save();

        return redirect()->route('nfr.index')
            ->with('success', 'General NFR updated successfully.');
    }

    public function destroy($id)
    {
    $nfr = GeneralNFR::findGeneralNFRById($id);
    $nfr->delete();

    return redirect()->route('nfr.index')
        ->with('success', 'General NFR deleted successfully.');
    }

}
