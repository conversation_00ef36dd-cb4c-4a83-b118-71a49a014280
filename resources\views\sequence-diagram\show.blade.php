@extends('layouts.app2')
@include('inc.success')
@include('inc.style')

@section('navbar')
  @include('inc.navbar')
@endsection

@section('content')
@include('inc.title')
<br>

<div style="background-color: #f8f9fa; padding: 20px; border-radius: 8px; margin-bottom: 20px;">
    <h3>{{ $sequenceDiagram->name }}</h3>
    @if($sequenceDiagram->description)
        <p><strong>Description:</strong> {{ $sequenceDiagram->description }}</p>
    @endif
    
    <div style="display: flex; gap: 20px; margin-top: 15px;">
        @if($sequenceDiagram->project)
            <span style="background-color: #007bff; color: white; padding: 5px 10px; border-radius: 4px; font-size: 12px;">
                Project: {{ $sequenceDiagram->project->proj_name }}
            </span>
        @endif
        
        @if($sequenceDiagram->sprint)
            <span style="background-color: #17a2b8; color: white; padding: 5px 10px; border-radius: 4px; font-size: 12px;">
                Sprint: {{ $sequenceDiagram->sprint->sprint_name }}
            </span>
        @endif
        
        @if($sequenceDiagram->objects_identified)
            <span style="background-color: #28a745; color: white; padding: 5px 10px; border-radius: 4px; font-size: 12px;">
                ✓ Objects Identified
            </span>
        @else
            <span style="background-color: #ffc107; color: #212529; padding: 5px 10px; border-radius: 4px; font-size: 12px;">
                ⏳ Pending Identification
            </span>
        @endif
    </div>
</div>

@if(!$sequenceDiagram->objects_identified && $sequenceDiagram->hasLifelines())
<div style="text-align: center; margin-bottom: 20px;">
    <button onclick="identifyObjects()" class="btn btn-success" style="font-size: 16px; padding: 10px 20px;">
        🔍 Identify Objects
    </button>
</div>
@endif

<!-- Lifelines Table -->
@if($sequenceDiagram->lifelines && count($sequenceDiagram->lifelines) > 0)
<h4>Lifelines in Sequence Diagram:</h4>
<table style="margin-bottom: 30px;">
    <tr>
        <th>Lifeline Name</th>
        <th>Position</th>
        <th>Type</th>
    </tr>
    @foreach($sequenceDiagram->lifelines as $index => $lifeline)
    <tr>
        <td>{{ $lifeline['name'] }}</td>
        <td>{{ $index + 1 }}</td>
        <td>{{ $lifeline['type'] ?? 'Object' }}</td>
    </tr>
    @endforeach
</table>
@endif

<!-- Object Roles Table -->
@if($sequenceDiagram->objects_identified && $sequenceDiagram->objects->count() > 0)
<h4>Identified Object Roles:</h4>
<table>
    <tr>
        <th>Object Name</th>
        <th>Assigned Role</th>
        <th>Role Color</th>
        <th>Identified Date</th>
        <th>Actions</th>
    </tr>
    
    @foreach($sequenceDiagram->objects as $object)
    <tr>
        <td><strong>{{ $object->name }}</strong></td>
        <td>
            <span style="background-color: {{ $object->role_color }}; color: white; padding: 5px 10px; border-radius: 4px; font-weight: bold;">
                {{ ucfirst($object->role) }}
            </span>
        </td>
        <td>
            <div style="width: 30px; height: 20px; background-color: {{ $object->role_color }}; border-radius: 4px; border: 1px solid #ccc;"></div>
        </td>
        <td>{{ $object->created_at->format('M d, Y H:i') }}</td>
        <td>
            <select onchange="updateRole({{ $object->id }}, this.value)" style="padding: 5px;">
                @foreach($availableRoles as $roleKey => $roleLabel)
                    <option value="{{ $roleKey }}" {{ $object->role === $roleKey ? 'selected' : '' }}>
                        {{ $roleLabel }}
                    </option>
                @endforeach
            </select>
        </td>
    </tr>
    @endforeach
</table>

<br>
<div style="background-color: #e9ecef; padding: 15px; border-radius: 5px;">
    <h5>Role Legend:</h5>
    <div style="display: flex; flex-wrap: wrap; gap: 10px; margin-top: 10px;">
        @foreach($availableRoles as $roleKey => $roleLabel)
            <span style="background-color: {{ \App\SequenceDiagram::getRoleColor($roleKey) }}; color: white; padding: 5px 10px; border-radius: 4px; font-size: 12px;">
                {{ $roleLabel }}
            </span>
        @endforeach
    </div>
</div>

@elseif($sequenceDiagram->objects_identified)
<div style="text-align: center; padding: 40px; background-color: #f8f9fa; border-radius: 8px;">
    <h5>No Objects Found</h5>
    <p>This sequence diagram doesn't contain any identifiable objects.</p>
</div>

@else
<div style="text-align: center; padding: 40px; background-color: #fff3cd; border-radius: 8px; border: 1px solid #ffeaa7;">
    <h5>Objects Not Yet Identified</h5>
    <p>Click the "Identify Objects" button above to automatically analyze and classify the objects in this sequence diagram.</p>
</div>
@endif

<br><br>

<script>
function identifyObjects() {
    if (confirm('Are you sure you want to proceed with identifying objects?\n\nThe system will automatically analyze the lifelines and assign appropriate roles based on naming patterns.')) {
        // Show loading message
        const button = event.target;
        button.innerHTML = '⏳ Identifying...';
        button.disabled = true;
        
        // Make AJAX request
        fetch('{{ route("sequence-diagram.identify-objects", $sequenceDiagram->id) }}', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': '{{ csrf_token() }}'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert(data.message);
                if (data.warning && data.unknown_count > 0) {
                    alert(`Warning: ${data.unknown_count} object(s) could not be automatically identified. Please review manually.`);
                }
                location.reload();
            } else {
                alert('Error: ' + data.message);
                button.innerHTML = '🔍 Identify Objects';
                button.disabled = false;
            }
        })
        .catch(error => {
            alert('An error occurred during object identification.');
            button.innerHTML = '🔍 Identify Objects';
            button.disabled = false;
        });
    }
}

function updateRole(objectId, newRole) {
    if (confirm('Are you sure you want to update this object role?')) {
        fetch('{{ route("sequence-diagram.update-role", $sequenceDiagram->id) }}', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': '{{ csrf_token() }}'
            },
            body: JSON.stringify({
                object_id: objectId,
                role: newRole
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert(data.message);
                location.reload();
            } else {
                alert('Error: ' + data.message);
                location.reload();
            }
        })
        .catch(error => {
            alert('An error occurred while updating the role.');
            location.reload();
        });
    } else {
        // Reset the select to original value
        location.reload();
    }
}
</script>

@endsection
