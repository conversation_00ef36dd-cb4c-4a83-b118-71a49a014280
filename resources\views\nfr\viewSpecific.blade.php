@extends('layouts.app2')
@include('inc.success')
@include('inc.style')
@include('inc.dashboard')

@section('navbar')
  @include('inc.navbar')
@endsection

@section('content')
  @include('inc.title')

  <br>
  @if ($specificNFR)
      <p>Specific NFR: {{ $specificNFR->specific_nfr }}</p>
      <p>Description: {{ $specificNFR->specific_nfr_desc }}</p>


      <!-- Filter Bar Form -->
      <form action="{{ route('nfr.viewSpecific', ['general_nfr_id' => $specificNFR->general_nfr_id, 'nfr_id' => $specificNFR->nfr_id]) }}" method="GET">
      <div class="form-group">
        <label for="project_filter">Filter by Project:</label>
            <select name="project_filter" id="project_filter" class="form-control" onchange="this.form.submit()">
                <option value="">Select a project</option>
                @foreach($pro as $project)
                    <option value="{{ $project->id }}" {{ request('project_filter') == $project->id ? 'selected' : '' }}>
                        {{ $project->proj_name }}
                    </option>
                @endforeach
            </select>
      </div>
      <br>
      <div class="form-group">
        <label for="sprint_filter">Filter by Sprint:</label>
            <select name="sprint_filter" id="sprint_filter" class="form-control" onchange="this.form.submit()">
                <option value="">Select a sprint</option>
                @foreach($sprints as $sprint)
                    <option value="{{ $sprint->sprint_id }}" {{ request('sprint_filter') == $sprint->sprint_id ? 'selected' : '' }}>
                        {{ $sprint->sprint_name }}
                    </option>
                @endforeach
            </select>
      </div>
      </form>

      <br>
      <h3>Linked User Stories</h3>
      <table class="table table-bordered">
          <thead>
              <tr>
                <th>Project</th>
                <th>Sprint</th>
                <th>User Story</th>
              </tr>
          </thead>
          <tbody>
              @if (isset($linkedUserStories) && !$linkedUserStories->isEmpty())
                  @foreach ($linkedUserStories as $story)
                      <tr>
                        <td>{{ $story->proj_name }}</td>
                        <td>{{ $story->sprint_name }}</td>
                        <td>{{ $story->user_story }}</td>
                      </tr>
                  @endforeach
              @else
                  <tr>
                      <td>No linked user stories found.</td>
                  </tr>
              @endif
          </tbody>
      </table>

      <!-- Pagination -->
      <div class="pagination-container">
          {{ $linkedUserStories->links('pagination::bootstrap-4') }}
      </div>

  @else
      <p>Specific NFR not found.</p>
  @endif
@endsection