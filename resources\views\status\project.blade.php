<!-- Status for Specific Project Page -->
@extends('layouts.app2')
@include('inc.style')
@include('inc.dashboard')
@include('inc.navbar')
@include('inc.success')

@section('content')
@include('inc.title')
<br>
    <table>
    <tr>
        <th>Status</th>
        <th>Delete</th>
    </tr>
    @forelse($statuses as $status)
        <tr> 
            <th>
                {{ $status->title}}
            </th>
            <th>
               <a href="{{route('statuses.destroy', $status)}}" class="btn btn-danger" onclick="return confirm('Are you sure you want to delete this status?');">Delete
            </th>
        </tr>
    @empty
    <tr>
        <td colspan="2">No status added yet</td>
    </tr>
    @endforelse
    </table>
  <br><br>

  <a href="{{route('statuses.create', $project['id'])}}" class="btn btn-success">Add Status</a>

@endsection

