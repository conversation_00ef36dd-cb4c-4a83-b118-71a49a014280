// * Carets
// *******************************************************************************

@mixin caret-up($caret-width) {
  margin-top: -0;
  width: $caret-width;
  height: $caret-width;
  border: 1px solid;
  border-bottom: 0;
  border-left: 0;
  transform: rotate(-45deg);
}

@mixin caret-down($caret-width) {
  margin-top: -1 * divide($caret-width, 1.5);
  width: $caret-width;
  height: $caret-width;
  border: 1px solid;
  border-top: 0;
  border-left: 0;
  transform: rotate(45deg);
}

@mixin caret-left($caret-width) {
  margin-top: -1 * divide($caret-width, 2.5);
  width: $caret-width;
  height: $caret-width;
  border: 1px solid;
  border-top: 0;
  border-right: 0;
  transform: rotate(45deg);
}

@mixin caret-right($caret-width) {
  margin-top: -1 * divide($caret-width, 2.5);
  width: $caret-width;
  height: $caret-width;
  border: 1px solid;
  border-top: 0;
  border-left: 0;
  transform: rotate(-45deg);
}
