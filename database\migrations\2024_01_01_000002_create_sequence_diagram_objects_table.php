<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateSequenceDiagramObjectsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('sequence_diagram_objects', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('sequence_diagram_id');
            $table->string('name');
            $table->enum('role', ['controller', 'model', 'view', 'service', 'actor', 'entity', 'repository', 'helper', 'unknown'])->default('unknown');
            $table->integer('position_x')->nullable();
            $table->integer('position_y')->nullable();
            $table->timestamp('identified_at')->nullable();
            $table->timestamps();

            $table->foreign('sequence_diagram_id')->references('id')->on('sequence_diagrams')->onDelete('cascade');
            $table->unique(['sequence_diagram_id', 'name']);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('sequence_diagram_objects');
    }
}
