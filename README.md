# SAgile Project Management Tool
SAgile is a project management tool built with Laravel 8. It allows teams to manage projects, tasks, and deadlines in a collaborative environment. The tool is designed to improve team productivity and collaboration, enabling you to easily manage your project from start to finish.

Features:
- SAgile comes with the following features:
- User authentication and authorization
- Project management
- Task management
- Collaborative environment
- Dashboard for overview


# Installation

To run SAgile on your local machine, you need to follow these steps:

**Prerequisites**

Before you start, make sure you have the following software installed on your system:

- XAMPP web server
- MySQL database
- Composer
- Git

Ensure PHP version installed in XAMPP is **v8.1.10**
- [XAMPP PHP Version Change Tutorial](https://www.youtube.com/watch?v=Uto36GI6HIg)
- [XAMPP Version 8.1.10](https://sourceforge.net/projects/xampp/files/XAMPP%20Windows/8.1.10/)

The project must be installed in `C:\path\to\xampp\htdocs\SAgilePMT_UTM`

In your phpMyAdmin create a database `kanban` and import [`kanban.sql`](kanban.sql)


# Start the Server

To start the server, navigate to the root directory of your Laravel project and run the following commands:

```
composer install
```
```
composer update
```
```
cp .env.example .env
```
```
php artisan key:generate
```
```
php artisan serve
```

This will start the server on http://localhost:8000.

# If CIG Radar Chart not generated in CIG Report
run the command below:
```
php artisan storage:link
```
This command creates a symbolic link from public/storage to storage/app/public, allowing you to access files stored in storage/app/public via the public/storage URL.
After running this command, you can access files in the storage/app/public directory using the public/storage

# Email Notification for Task Comments
Configure Email Settings
Ensure your .env file is correctly configured for email sending. For Gmail, use the following settings:

.env

```
MAIL_MAILER=smtp
MAIL_HOST=smtp.gmail.com
MAIL_PORT=587
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your_app_password
MAIL_ENCRYPTION=tls
MAIL_FROM_ADDRESS=<EMAIL>
MAIL_FROM_NAME="Your Application Name"

```

Clear and Cache the Configuration

```
php artisan config:clear

```

```
php artisan config:cache

```


# Conclusion

Congratulations! You have successfully installed SAgile Project Management Tool on your local machine. You can now use the tool to manage your projects and tasks, collaborate with your team members, and track deadlines and milestones.
