@extends('layouts.app2')
@include('inc.success')
@include('inc.style')


@section('navbar')
  @include('inc.navbar')
@endsection

@section('content')
@include('inc.title')
<br>

@if($user->user_type == 1 || in_array('Project Manager', $roles))

@if ($generalNFR && $generalNFR->isNotEmpty())
    
<table>
  <tr>
      <th>General NFR</th>
      <th>Description</th>
      <th>List</th>
      <th>View</th>
      <th>Edit</th>
      <th>Delete</th>
  </tr>

  @foreach($generalNFR as $nfr)
                <tr>
                    <td>{{ $nfr->general_nfr }}</td>
                    <td>{{ $nfr->general_nfr_desc }}</td>
                    <td><a href="{{ route('nfr.show', ['general_nfr_id' => $nfr->general_nfr_id]) }}" class="btn btn-primary">List</a></td>
                    <td><a href="{{ route('nfr.viewGeneral', ['general_nfr_id' => $nfr->general_nfr_id]) }}" class="btn btn-primary">View</a></td>
                    @if($user->user_type == 1 && $nfr->created_by == $user->id)
                    <td><a href="{{ route('nfr.edit', ['general_nfr_id' => $nfr->general_nfr_id]) }}" class="btn btn-warning">Edit</a></td>
                    <td>
                    <br>
                        <form action="{{ route('nfr.destroy', ['general_nfr_id' => $nfr->general_nfr_id]) }}" method="post">
                            @csrf
                            @method('DELETE')
                            <button type="submit" class="btn btn-danger" onclick="return confirm('Are you sure want to delete this non-functional requirement?')">Delete</button>
                        </form>
                    </td>
                    @else
                    <td colspan="2">Only creator can edit or delete</td>
                    @endif
                </tr>
            @endforeach

</table>

<!-- Pagination Links -->
<div class="pagination-container">
    {{ $generalNFR->links('pagination::bootstrap-4') }}
</div>

@else
    <p>No General NFRs found.</p>
@endif

<br><br><br>
@if($user->user_type == 1)
    <a href="{{ route('nfr.create') }}" class="btn btn-success">Add General NFR</a>
@endif
<!-- @else
    <div>Only Project Managers and Admins can view this page.</div>
@endif -->

@endsection
