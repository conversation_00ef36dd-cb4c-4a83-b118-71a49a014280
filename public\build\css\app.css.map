{"version": 3, "sources": ["app.css", "minireset.scss", "app.scss", "../../../node_modules/bootstrap-sass/assets/stylesheets/_bootstrap.scss", "../../../node_modules/bootstrap-sass/assets/stylesheets/bootstrap/_normalize.scss", "../../../node_modules/bootstrap-sass/assets/stylesheets/bootstrap/_print.scss", "../../../node_modules/bootstrap-sass/assets/stylesheets/bootstrap/_glyphicons.scss", "../../../node_modules/bootstrap-sass/assets/stylesheets/bootstrap/_scaffolding.scss", "../../../node_modules/bootstrap-sass/assets/stylesheets/bootstrap/mixins/_vendor-prefixes.scss", "../../../node_modules/bootstrap-sass/assets/stylesheets/bootstrap/_variables.scss", "../../../node_modules/bootstrap-sass/assets/stylesheets/bootstrap/mixins/_tab-focus.scss", "../../../node_modules/bootstrap-sass/assets/stylesheets/bootstrap/mixins/_image.scss", "../../../node_modules/bootstrap-sass/assets/stylesheets/bootstrap/_type.scss", "../../../node_modules/bootstrap-sass/assets/stylesheets/bootstrap/mixins/_text-emphasis.scss", "../../../node_modules/bootstrap-sass/assets/stylesheets/bootstrap/mixins/_background-variant.scss", "../../../node_modules/bootstrap-sass/assets/stylesheets/bootstrap/mixins/_clearfix.scss", "../../../node_modules/bootstrap-sass/assets/stylesheets/bootstrap/mixins/_text-overflow.scss", "../../../node_modules/bootstrap-sass/assets/stylesheets/bootstrap/_code.scss", "../../../node_modules/bootstrap-sass/assets/stylesheets/bootstrap/_grid.scss", "../../../node_modules/bootstrap-sass/assets/stylesheets/bootstrap/mixins/_grid.scss", "../../../node_modules/bootstrap-sass/assets/stylesheets/bootstrap/mixins/_grid-framework.scss", "../../../node_modules/bootstrap-sass/assets/stylesheets/bootstrap/_tables.scss", "../../../node_modules/bootstrap-sass/assets/stylesheets/bootstrap/mixins/_table-row.scss", "../../../node_modules/bootstrap-sass/assets/stylesheets/bootstrap/_forms.scss", "../../../node_modules/bootstrap-sass/assets/stylesheets/bootstrap/mixins/_forms.scss", "../../../node_modules/bootstrap-sass/assets/stylesheets/bootstrap/_buttons.scss", "../../../node_modules/bootstrap-sass/assets/stylesheets/bootstrap/mixins/_buttons.scss", "../../../node_modules/bootstrap-sass/assets/stylesheets/bootstrap/mixins/_opacity.scss", "../../../node_modules/bootstrap-sass/assets/stylesheets/bootstrap/_component-animations.scss", "../../../node_modules/bootstrap-sass/assets/stylesheets/bootstrap/_dropdowns.scss", "../../../node_modules/bootstrap-sass/assets/stylesheets/bootstrap/mixins/_nav-divider.scss", "../../../node_modules/bootstrap-sass/assets/stylesheets/bootstrap/mixins/_reset-filter.scss", "../../../node_modules/bootstrap-sass/assets/stylesheets/bootstrap/_button-groups.scss", "../../../node_modules/bootstrap-sass/assets/stylesheets/bootstrap/mixins/_border-radius.scss", "../../../node_modules/bootstrap-sass/assets/stylesheets/bootstrap/_input-groups.scss", "../../../node_modules/bootstrap-sass/assets/stylesheets/bootstrap/_navs.scss", "../../../node_modules/bootstrap-sass/assets/stylesheets/bootstrap/_navbar.scss", "../../../node_modules/bootstrap-sass/assets/stylesheets/bootstrap/mixins/_nav-vertical-align.scss", "../../../node_modules/bootstrap-sass/assets/stylesheets/bootstrap/_breadcrumbs.scss", "../../../node_modules/bootstrap-sass/assets/stylesheets/bootstrap/_pagination.scss", "../../../node_modules/bootstrap-sass/assets/stylesheets/bootstrap/mixins/_pagination.scss", "../../../node_modules/bootstrap-sass/assets/stylesheets/bootstrap/_pager.scss", "../../../node_modules/bootstrap-sass/assets/stylesheets/bootstrap/_labels.scss", "../../../node_modules/bootstrap-sass/assets/stylesheets/bootstrap/mixins/_labels.scss", "../../../node_modules/bootstrap-sass/assets/stylesheets/bootstrap/_badges.scss", "../../../node_modules/bootstrap-sass/assets/stylesheets/bootstrap/_jumbotron.scss", "../../../node_modules/bootstrap-sass/assets/stylesheets/bootstrap/_thumbnails.scss", "../../../node_modules/bootstrap-sass/assets/stylesheets/bootstrap/_alerts.scss", "../../../node_modules/bootstrap-sass/assets/stylesheets/bootstrap/mixins/_alerts.scss", "../../../node_modules/bootstrap-sass/assets/stylesheets/bootstrap/_progress-bars.scss", "../../../node_modules/bootstrap-sass/assets/stylesheets/bootstrap/mixins/_gradients.scss", "../../../node_modules/bootstrap-sass/assets/stylesheets/bootstrap/mixins/_progress-bar.scss", "../../../node_modules/bootstrap-sass/assets/stylesheets/bootstrap/_media.scss", "../../../node_modules/bootstrap-sass/assets/stylesheets/bootstrap/_list-group.scss", "../../../node_modules/bootstrap-sass/assets/stylesheets/bootstrap/mixins/_list-group.scss", "../../../node_modules/bootstrap-sass/assets/stylesheets/bootstrap/_panels.scss", "../../../node_modules/bootstrap-sass/assets/stylesheets/bootstrap/mixins/_panels.scss", "../../../node_modules/bootstrap-sass/assets/stylesheets/bootstrap/_responsive-embed.scss", "../../../node_modules/bootstrap-sass/assets/stylesheets/bootstrap/_wells.scss", "../../../node_modules/bootstrap-sass/assets/stylesheets/bootstrap/_close.scss", "../../../node_modules/bootstrap-sass/assets/stylesheets/bootstrap/_modals.scss", "../../../node_modules/bootstrap-sass/assets/stylesheets/bootstrap/_tooltip.scss", "../../../node_modules/bootstrap-sass/assets/stylesheets/bootstrap/mixins/_reset-text.scss", "../../../node_modules/bootstrap-sass/assets/stylesheets/bootstrap/_popovers.scss", "../../../node_modules/bootstrap-sass/assets/stylesheets/bootstrap/_carousel.scss", "../../../node_modules/bootstrap-sass/assets/stylesheets/bootstrap/_utilities.scss", "../../../node_modules/bootstrap-sass/assets/stylesheets/bootstrap/mixins/_center-block.scss", "../../../node_modules/bootstrap-sass/assets/stylesheets/bootstrap/mixins/_hide-text.scss", "../../../node_modules/bootstrap-sass/assets/stylesheets/bootstrap/_responsive-utilities.scss", "../../../node_modules/bootstrap-sass/assets/stylesheets/bootstrap/mixins/_responsive-visibility.scss", "_variables.scss"], "names": [], "mappings": "AAAA,iBAAiB;ACAjB,2EAA2E;ACG3E,6EAAY;ADFZ;;;;;;;;;;;;;;;;;;;;;;;EAuBE,UAAU;EACV,WAAW;CACZ;;AAED;;;;;;EAME,gBAAgB;EAChB,oBAAoB;CACrB;;AAED;EACE,iBAAiB;CAClB;;AAED;;;;EAIE,UAAU;CACX;;AAED;EACE,uBAAuB;CACxB;;AAED;EACE,oBAAoB;CACrB;;AAED;;;;;EAKE,aAAa;EACb,gBAAgB;CACjB;;AAED;EACE,UAAU;CACX;;AAED;EACE,0BAA0B;EAC1B,kBAAkB;CACnB;;AAED;;EAEE,WAAW;EACX,iBAAiB;CAClB;;AE/ED;;;;GAIG;ACJH,4EAA4E;AAQ5E;EACE,wBAAwB;EACxB,2BAA2B;EAC3B,+BAA+B;CAChC;;AAMD;EACE,UAAU;CACX;;AAYD;;;;;;;;;;;;;EAaE,eAAe;CAChB;;AAOD;;;;EAIE,sBAAsB;EACtB,yBAAyB;CAC1B;;AAOD;EACE,cAAc;EACd,UAAU;CACX;;AJ2DD;;EIlDE,cAAc;CACf;;AASD;EACE,8BAA8B;CAC/B;;AAOD;;EAEE,WAAW;CACZ;;AASD;EACE,0BAA0B;CAC3B;;AAMD;;EAEE,kBAAkB;CACnB;;AAMD;EACE,mBAAmB;CACpB;;AAOD;EACE,eAAe;EACf,iBAAiB;CAClB;;AAMD;EACE,iBAAiB;EACjB,YAAY;CACb;;AAMD;EACE,eAAe;CAChB;;AAMD;;EAEE,eAAe;EACf,eAAe;EACf,mBAAmB;EACnB,yBAAyB;CAC1B;;AAED;EACE,YAAY;CACb;;AAED;EACE,gBAAgB;CACjB;;AASD;EACE,UAAU;CACX;;AAMD;EACE,iBAAiB;CAClB;;AASD;EACE,iBAAiB;CAClB;;AAMD;EACE,wBAAwB;EACxB,UAAU;CACX;;AAMD;EACE,eAAe;CAChB;;AAMD;;;;EAIE,kCAAkC;EAClC,eAAe;CAChB;;AAiBD;;;;;EAKE,eAAe;EACf,cAAc;EACd,UAAU;CACX;;AAMD;EACE,kBAAkB;CACnB;;AASD;;EAEE,qBAAqB;CACtB;;AAUD;;;;EAIE,2BAA2B;EAC3B,gBAAgB;CACjB;;AAMD;;EAEE,gBAAgB;CACjB;;AAMD;;EAEE,UAAU;EACV,WAAW;CACZ;;AAOD;EACE,oBAAoB;CACrB;;AAUD;;EAEE,uBAAuB;EACvB,WAAW;CACZ;;AAQD;;EAEE,aAAa;CACd;;AAOD;EACE,8BAA8B;EAC9B,wBAAwB;CACzB;;AAQD;;EAEE,yBAAyB;CAC1B;;AAMD;EACE,0BAA0B;EAC1B,cAAc;EACd,+BAA+B;CAChC;;AAOD;EACE,UAAU;EACV,WAAW;CACZ;;AAMD;EACE,eAAe;CAChB;;AAOD;EACE,kBAAkB;CACnB;;AASD;EACE,0BAA0B;EAC1B,kBAAkB;CACnB;;AAED;;EAEE,WAAW;CACZ;;ACvaD,qFAAqF;AAOrF;EACI;;;IAGI,mCAAmC;IACnC,uBAAuB;IACvB,4BAA4B;IAC5B,6BAA6B;GAChC;EAED;;IAEI,2BAA2B;GAC9B;EAED;IACI,6BAA4B;GAC/B;EAED;IACI,8BAA6B;GAChC;EAID;;IAEI,YAAY;GACf;EAED;;IAEI,uBAAuB;IACvB,yBAAyB;GAC5B;EAED;IACI,4BAA4B;GAC/B;EAED;;IAEI,yBAAyB;GAC5B;EAED;IACI,2BAA2B;GAC9B;EAED;;;IAGI,WAAW;IACX,UAAU;GACb;EAED;;IAEI,wBAAwB;GAC3B;EAKD;IACI,cAAc;GACjB;EACD;;IAGQ,kCAAkC;GACrC;EAEL;IACI,uBAAuB;GAC1B;EAED;IACI,qCAAqC;GAMxC;EAPD;;IAKQ,kCAAkC;GACrC;EAEL;;IAGQ,kCAAkC;GACrC;CLwRR;;AM7WC;EACE,oCAAoC;EACpC,gEAAQ;EACR,kbAImM;CN4WtM;;AMvWD;EACE,mBAAmB;EACnB,SAAS;EACT,sBAAsB;EACtB,oCAAoC;EACpC,mBAAmB;EACnB,oBAAoB;EACpB,eAAe;EACf,oCAAoC;EACpC,mCAAmC;CACpC;;AAGD;EAA+C,iBAAiB;CAAI;;AACpE;EAA+C,iBAAiB;CAAI;;AACpE;;EAC+C,iBAAiB;CAAI;;AACpE;EAA+C,iBAAiB;CAAI;;AACpE;EAA+C,iBAAiB;CAAI;;AACpE;EAA+C,iBAAiB;CAAI;;AACpE;EAA+C,iBAAiB;CAAI;;AACpE;EAA+C,iBAAiB;CAAI;;AACpE;EAA+C,iBAAiB;CAAI;;AACpE;EAA+C,iBAAiB;CAAI;;AACpE;EAA+C,iBAAiB;CAAI;;AACpE;EAA+C,iBAAiB;CAAI;;AACpE;EAA+C,iBAAiB;CAAI;;AACpE;EAA+C,iBAAiB;CAAI;;AACpE;EAA+C,iBAAiB;CAAI;;AACpE;EAA+C,iBAAiB;CAAI;;AACpE;EAA+C,iBAAiB;CAAI;;AACpE;EAA+C,iBAAiB;CAAI;;AACpE;EAA+C,iBAAiB;CAAI;;AACpE;EAA+C,iBAAiB;CAAI;;AACpE;EAA+C,iBAAiB;CAAI;;AACpE;EAA+C,iBAAiB;CAAI;;AACpE;EAA+C,iBAAiB;CAAI;;AACpE;EAA+C,iBAAiB;CAAI;;AACpE;EAA+C,iBAAiB;CAAI;;AACpE;EAA+C,iBAAiB;CAAI;;AACpE;EAA+C,iBAAiB;CAAI;;AACpE;EAA+C,iBAAiB;CAAI;;AACpE;EAA+C,iBAAiB;CAAI;;AACpE;EAA+C,iBAAiB;CAAI;;AACpE;EAA+C,iBAAiB;CAAI;;AACpE;EAA+C,iBAAiB;CAAI;;AACpE;EAA+C,iBAAiB;CAAI;;AACpE;EAA+C,iBAAiB;CAAI;;AACpE;EAA+C,iBAAiB;CAAI;;AACpE;EAA+C,iBAAiB;CAAI;;AACpE;EAA+C,iBAAiB;CAAI;;AACpE;EAA+C,iBAAiB;CAAI;;AACpE;EAA+C,iBAAiB;CAAI;;AACpE;EAA+C,iBAAiB;CAAI;;AACpE;EAA+C,iBAAiB;CAAI;;AACpE;EAA+C,iBAAiB;CAAI;;AACpE;EAA+C,iBAAiB;CAAI;;AACpE;EAA+C,iBAAiB;CAAI;;AACpE;EAA+C,iBAAiB;CAAI;;AACpE;EAA+C,iBAAiB;CAAI;;AACpE;EAA+C,iBAAiB;CAAI;;AACpE;EAA+C,iBAAiB;CAAI;;AACpE;EAA+C,iBAAiB;CAAI;;AACpE;EAA+C,iBAAiB;CAAI;;AACpE;EAA+C,iBAAiB;CAAI;;AACpE;EAA+C,iBAAiB;CAAI;;AACpE;EAA+C,iBAAiB;CAAI;;AACpE;EAA+C,iBAAiB;CAAI;;AACpE;EAA+C,iBAAiB;CAAI;;AACpE;EAA+C,iBAAiB;CAAI;;AACpE;EAA+C,iBAAiB;CAAI;;AACpE;EAA+C,iBAAiB;CAAI;;AACpE;EAA+C,iBAAiB;CAAI;;AACpE;EAA+C,iBAAiB;CAAI;;AACpE;EAA+C,iBAAiB;CAAI;;AACpE;EAA+C,iBAAiB;CAAI;;AACpE;EAA+C,iBAAiB;CAAI;;AACpE;EAA+C,iBAAiB;CAAI;;AACpE;EAA+C,iBAAiB;CAAI;;AACpE;EAA+C,iBAAiB;CAAI;;AACpE;EAA+C,iBAAiB;CAAI;;AACpE;EAA+C,iBAAiB;CAAI;;AACpE;EAA+C,iBAAiB;CAAI;;AACpE;EAA+C,iBAAiB;CAAI;;AACpE;EAA+C,iBAAiB;CAAI;;AACpE;EAA+C,iBAAiB;CAAI;;AACpE;EAA+C,iBAAiB;CAAI;;AACpE;EAA+C,iBAAiB;CAAI;;AACpE;EAA+C,iBAAiB;CAAI;;AACpE;EAA+C,iBAAiB;CAAI;;AACpE;EAA+C,iBAAiB;CAAI;;AACpE;EAA+C,iBAAiB;CAAI;;AACpE;EAA+C,iBAAiB;CAAI;;AACpE;EAA+C,iBAAiB;CAAI;;AACpE;EAA+C,iBAAiB;CAAI;;AACpE;EAA+C,iBAAiB;CAAI;;AACpE;EAA+C,iBAAiB;CAAI;;AACpE;EAA+C,iBAAiB;CAAI;;AACpE;EAA+C,iBAAiB;CAAI;;AACpE;EAA+C,iBAAiB;CAAI;;AACpE;EAA+C,iBAAiB;CAAI;;AACpE;EAA+C,iBAAiB;CAAI;;AACpE;EAA+C,iBAAiB;CAAI;;AACpE;EAA+C,iBAAiB;CAAI;;AACpE;EAA+C,iBAAiB;CAAI;;AACpE;EAA+C,iBAAiB;CAAI;;AACpE;EAA+C,iBAAiB;CAAI;;AACpE;EAA+C,iBAAiB;CAAI;;AACpE;EAA+C,iBAAiB;CAAI;;AACpE;EAA+C,iBAAiB;CAAI;;AACpE;EAA+C,iBAAiB;CAAI;;AACpE;EAA+C,iBAAiB;CAAI;;AACpE;EAA+C,iBAAiB;CAAI;;AACpE;EAA+C,iBAAiB;CAAI;;AACpE;EAA+C,iBAAiB;CAAI;;AACpE;EAA+C,iBAAiB;CAAI;;AACpE;EAA+C,iBAAiB;CAAI;;AACpE;EAA+C,iBAAiB;CAAI;;AACpE;EAA+C,iBAAiB;CAAI;;AACpE;EAA+C,iBAAiB;CAAI;;AACpE;EAA+C,iBAAiB;CAAI;;AACpE;EAA+C,iBAAiB;CAAI;;AACpE;EAA+C,iBAAiB;CAAI;;AACpE;EAA+C,iBAAiB;CAAI;;AACpE;EAA+C,iBAAiB;CAAI;;AACpE;EAA+C,iBAAiB;CAAI;;AACpE;EAA+C,iBAAiB;CAAI;;AACpE;EAA+C,iBAAiB;CAAI;;AACpE;EAA+C,iBAAiB;CAAI;;AACpE;EAA+C,iBAAiB;CAAI;;AACpE;EAA+C,iBAAiB;CAAI;;AACpE;EAA+C,iBAAiB;CAAI;;AACpE;EAA+C,iBAAiB;CAAI;;AACpE;EAA+C,iBAAiB;CAAI;;AACpE;EAA+C,iBAAiB;CAAI;;AACpE;EAA+C,iBAAiB;CAAI;;AACpE;EAA+C,iBAAiB;CAAI;;AACpE;EAA+C,iBAAiB;CAAI;;AACpE;EAA+C,iBAAiB;CAAI;;AACpE;EAA+C,iBAAiB;CAAI;;AACpE;EAA+C,iBAAiB;CAAI;;AACpE;EAA+C,iBAAiB;CAAI;;AACpE;EAA+C,iBAAiB;CAAI;;AACpE;EAA+C,iBAAiB;CAAI;;AACpE;EAA+C,iBAAiB;CAAI;;AACpE;EAA+C,iBAAiB;CAAI;;AACpE;EAA+C,iBAAiB;CAAI;;AACpE;EAA+C,iBAAiB;CAAI;;AACpE;EAA+C,iBAAiB;CAAI;;AACpE;EAA+C,iBAAiB;CAAI;;AACpE;EAA+C,iBAAiB;CAAI;;AACpE;EAA+C,iBAAiB;CAAI;;AACpE;EAA+C,iBAAiB;CAAI;;AACpE;EAA+C,iBAAiB;CAAI;;AACpE;EAA+C,iBAAiB;CAAI;;AACpE;EAA+C,iBAAiB;CAAI;;AACpE;EAA+C,iBAAiB;CAAI;;AACpE;EAA+C,iBAAiB;CAAI;;AACpE;EAA+C,iBAAiB;CAAI;;AACpE;EAA+C,iBAAiB;CAAI;;AACpE;EAA+C,iBAAiB;CAAI;;AACpE;EAA+C,iBAAiB;CAAI;;AACpE;EAA+C,iBAAiB;CAAI;;AACpE;EAA+C,iBAAiB;CAAI;;AACpE;EAA+C,iBAAiB;CAAI;;AACpE;EAA+C,iBAAiB;CAAI;;AACpE;EAA+C,iBAAiB;CAAI;;AACpE;EAA+C,iBAAiB;CAAI;;AACpE;EAA+C,iBAAiB;CAAI;;AACpE;EAA+C,iBAAiB;CAAI;;AACpE;EAA+C,iBAAiB;CAAI;;AACpE;EAA+C,iBAAiB;CAAI;;AACpE;EAA+C,iBAAiB;CAAI;;AACpE;EAA+C,iBAAiB;CAAI;;AACpE;EAA+C,iBAAiB;CAAI;;AACpE;EAA+C,iBAAiB;CAAI;;AACpE;EAA+C,iBAAiB;CAAI;;AACpE;EAA+C,iBAAiB;CAAI;;AACpE;EAA+C,iBAAiB;CAAI;;AACpE;EAA+C,iBAAiB;CAAI;;AACpE;EAA+C,iBAAiB;CAAI;;AACpE;EAA+C,iBAAiB;CAAI;;AACpE;EAA+C,iBAAiB;CAAI;;AACpE;EAA+C,iBAAiB;CAAI;;AACpE;EAA+C,iBAAiB;CAAI;;AACpE;EAA+C,iBAAiB;CAAI;;AACpE;EAA+C,iBAAiB;CAAI;;AACpE;EAA+C,iBAAiB;CAAI;;AACpE;EAA+C,iBAAiB;CAAI;;AACpE;EAA+C,iBAAiB;CAAI;;AACpE;EAA+C,iBAAiB;CAAI;;AACpE;EAA+C,iBAAiB;CAAI;;AACpE;EAA+C,iBAAiB;CAAI;;AACpE;EAA+C,iBAAiB;CAAI;;AACpE;EAA+C,iBAAiB;CAAI;;AACpE;EAA+C,iBAAiB;CAAI;;AACpE;EAA+C,iBAAiB;CAAI;;AACpE;EAA+C,iBAAiB;CAAI;;AACpE;EAA+C,iBAAiB;CAAI;;AACpE;EAA+C,iBAAiB;CAAI;;AACpE;EAA+C,iBAAiB;CAAI;;AACpE;EAA+C,iBAAiB;CAAI;;AACpE;EAA+C,iBAAiB;CAAI;;AACpE;EAA+C,iBAAiB;CAAI;;AACpE;EAA+C,iBAAiB;CAAI;;AACpE;EAA+C,iBAAiB;CAAI;;AACpE;EAA+C,iBAAiB;CAAI;;AACpE;EAA+C,iBAAiB;CAAI;;AACpE;EAA+C,iBAAiB;CAAI;;AACpE;EAA+C,iBAAiB;CAAI;;AACpE;EAA+C,iBAAiB;CAAI;;AACpE;EAA+C,iBAAiB;CAAI;;AACpE;EAA+C,iBAAiB;CAAI;;AACpE;EAA+C,iBAAiB;CAAI;;AACpE;EAA+C,iBAAiB;CAAI;;AACpE;EAA+C,iBAAiB;CAAI;;AACpE;EAA+C,iBAAiB;CAAI;;AACpE;EAA+C,iBAAiB;CAAI;;AACpE;EAA+C,iBAAiB;CAAI;;AACpE;EAA+C,iBAAiB;CAAI;;AASpE;EAA+C,iBAAiB;CAAI;;AACpE;EAA+C,iBAAiB;CAAI;;AACpE;EAA+C,iBAAiB;CAAI;;AACpE;EAA+C,iBAAiB;CAAI;;AACpE;EAA+C,iBAAiB;CAAI;;AACpE;EAA+C,iBAAiB;CAAI;;AACpE;EAA+C,iBAAiB;CAAI;;AACpE;EAA+C,iBAAiB;CAAI;;AACpE;EAA+C,iBAAiB;CAAI;;AACpE;EAA+C,iBAAiB;CAAI;;AACpE;EAA+C,iBAAiB;CAAI;;AACpE;EAA+C,iBAAiB;CAAI;;AACpE;EAA+C,iBAAiB;CAAI;;AACpE;EAA+C,iBAAiB;CAAI;;AACpE;EAA+C,iBAAiB;CAAI;;AACpE;EAA+C,iBAAiB;CAAI;;AACpE;EAA+C,iBAAiB;CAAI;;AACpE;EAA+C,iBAAiB;CAAI;;AACpE;EAA+C,iBAAiB;CAAI;;AACpE;EAA+C,iBAAiB;CAAI;;AACpE;EAA+C,iBAAiB;CAAI;;AACpE;EAA+C,iBAAiB;CAAI;;AACpE;EAA+C,iBAAiB;CAAI;;AACpE;EAA+C,iBAAiB;CAAI;;AACpE;EAA+C,iBAAiB;CAAI;;AACpE;EAA+C,iBAAiB;CAAI;;AACpE;EAA+C,iBAAiB;CAAI;;AACpE;EAA+C,iBAAiB;CAAI;;AACpE;EAA+C,iBAAiB;CAAI;;AACpE;EAA+C,iBAAiB;CAAI;;AACpE;EAA+C,iBAAiB;CAAI;;AACpE;EAA+C,iBAAiB;CAAI;;AACpE;EAA+C,iBAAiB;CAAI;;AACpE;EAA+C,iBAAiB;CAAI;;AACpE;EAA+C,iBAAiB;CAAI;;AACpE;EAA+C,iBAAiB;CAAI;;AACpE;EAA+C,iBAAiB;CAAI;;AACpE;EAA+C,iBAAiB;CAAI;;AACpE;EAA+C,iBAAiB;CAAI;;AACpE;EAA+C,iBAAiB;CAAI;;AACpE;EAA+C,iBAAiB;CAAI;;AACpE;EAA+C,iBAAiB;CAAI;;AACpE;EAA+C,iBAAiB;CAAI;;AACpE;EAA+C,iBAAiB;CAAI;;AACpE;EAA+C,iBAAiB;CAAI;;AACpE;EAA+C,iBAAiB;CAAI;;AACpE;EAA+C,iBAAiB;CAAI;;AACpE;EAA+C,iBAAiB;CAAI;;AACpE;EAA+C,iBAAiB;CAAI;;AACpE;EAA+C,iBAAiB;CAAI;;AACpE;EAA+C,iBAAiB;CAAI;;AACpE;EAA+C,iBAAiB;CAAI;;AACpE;EAA+C,iBAAiB;CAAI;;AACpE;EAA+C,iBAAiB;CAAI;;AACpE;EAA+C,iBAAiB;CAAI;;AACpE;EAA+C,iBAAiB;CAAI;;ACxSpE;ECkEU,uBDjEsB;CAC/B;;AACD;;EC+DU,uBD7DsB;CAC/B;;AAKD;EACE,gBAAgB;EAChB,yCAAiC;CAClC;;AAED;EACE,4DEkBsE;EFjBtE,gBEuB4B;EFtB5B,yBEkCmC;EFjCnC,eElB8B;EFmB9B,uBEF0B;CFG3B;;AAGD;;;;EAIE,qBAAqB;EACrB,mBAAmB;EACnB,qBAAqB;CACtB;;AAKD;EACE,eE/B4B;EFgC5B,sBAAsB;CAWvB;;AAbD;EAMI,eEjB0B;EFkB1B,2BEhB6B;CFiB9B;;AARH;EGzCE,2CAA2C;EAC3C,qBAAqB;CHoDpB;;AASH;EACE,UAAU;CACX;;AAKD;EACE,uBAAuB;CACxB;;AAGD;EIvEE,eADmC;EAEnC,gBAAgB;EAChB,aAAa;CJuEd;;AAGD;EACE,mBEwB6B;CFvB9B;;AAKD;EACE,aEgpB+B;EF/oB/B,yBE/BmC;EFgCnC,uBElE0B;EFmE1B,uBEipBgC;EFhpBhC,mBEY6B;ED8ErB,iCDzF+B;EIzFvC,sBJ4FoC;EI3FpC,gBAAgB;EAChB,aAAa;CJ2Fd;;AAGD;EACE,mBAAmB;CACpB;;AAKD;EACE,iBEhD6B;EFiD7B,oBEjD6B;EFkD7B,UAAU;EACV,8BErG8B;CFsG/B;;AAOD;EACE,mBAAmB;EACnB,WAAW;EACX,YAAY;EACZ,aAAa;EACb,WAAW;EACX,iBAAiB;EACjB,uBAAU;EACV,UAAU;CACX;;AAMD;EAGI,iBAAiB;EACjB,YAAY;EACZ,aAAa;EACb,UAAU;EACV,kBAAkB;EAClB,WAAW;CACZ;;APk4CH;EOv3CE,gBAAgB;CACjB;;AKxJD;;EAEE,qBH0D+B;EGzD/B,iBH0D2B;EGzD3B,iBH0D2B;EGzD3B,eH0D+B;CGlDhC;;AAbD;;;;;;;;;;;;;;EASI,oBAAoB;EACpB,eAAe;EACf,eHL4B;CGM7B;;AAGH;;;EAGE,iBHuC6B;EGtC7B,oBAAqC;CAMtC;;AAVD;;;;;;;;;EAQI,eAAe;CAChB;;AAEH;;;EAGE,iBAAkC;EAClC,oBAAqC;CAMtC;;AAVD;;;;;;;;;EAQI,eAAe;CAChB;;AAGH;EAAU,gBHSqB;CGTO;;AACtC;EAAU,gBHSqB;CGTO;;AACtC;EAAU,gBHSoB;CGTQ;;AACtC;EAAU,gBHSoB;CGTQ;;AACtC;EAAU,gBHCoB;CGDQ;;AACtC;EAAU,gBHSoB;CGTQ;;AAMtC;EACE,iBAAkC;CACnC;;AAED;EACE,oBHG6B;EGF7B,gBAAgB;EAChB,iBAAiB;EACjB,iBAAiB;CAKlB;;AAHC;EANF;IAOI,gBAA2B;GAE9B;CZ4jDA;;AYrjDD;;EAEE,eAAgB;CACjB;;AAED;;EAEE,0BH4asC;EG3atC,cAAc;CACf;;AAGD;EAAuB,iBAAiB;CAAI;;AAC5C;EAAuB,kBAAkB;CAAI;;AAC7C;EAAuB,mBAAmB;CAAI;;AAC9C;EAAuB,oBAAoB;CAAI;;AAC/C;EAAuB,oBAAoB;CAAI;;AAG/C;EAAuB,0BAA0B;CAAI;;AACrD;EAAuB,0BAA0B;CAAI;;AACrD;EAAuB,2BAA2B;CAAI;;AAGtD;EACE,eHxF8B;CGyF/B;;ACnGC;EACE,eJY0B;CIX3B;;AACD;;EACE,eAAa;CACd;;AALD;EACE,eJkfoC;CIjfrC;;AACD;;EACE,eAAa;CACd;;AALD;EACE,eJsfoC;CIrfrC;;AACD;;EACE,eAAa;CACd;;AALD;EACE,eJ0foC;CIzfrC;;AACD;;EACE,eAAa;CACd;;AALD;EACE,eJ8foC;CI7frC;;AACD;;EACE,eAAa;CACd;;AD6GH;EAGE,YAAY;CACb;;AEtHC;EACE,0BLY0B;CKX3B;;AACD;;EACE,0BAAwB;CACzB;;AALD;EACE,0BLmfoC;CKlfrC;;AACD;;EACE,0BAAwB;CACzB;;AALD;EACE,0BLufoC;CKtfrC;;AACD;;EACE,0BAAwB;CACzB;;AALD;EACE,0BL2foC;CK1frC;;AACD;;EACE,0BAAwB;CACzB;;AALD;EACE,0BL+foC;CK9frC;;AACD;;EACE,0BAAwB;CACzB;;AFgIH;EACE,oBAAuC;EACvC,oBH1E6B;EG2E7B,iCH7H8B;CG8H/B;;AAOD;;EAEE,cAAc;EACd,oBAAqC;CAKtC;;AARD;;;;EAMI,iBAAiB;CAClB;;AAWH;EAJE,gBAAgB;EAChB,iBAAiB;CAKlB;;AAID;EAVE,gBAAgB;EAChB,iBAAiB;EAWjB,kBAAkB;CAOnB;;AATD;EAKI,sBAAsB;EACtB,kBAAkB;EAClB,mBAAmB;CACpB;;AAIH;EACE,cAAc;EACd,oBHzH6B;CG0H9B;;AACD;;EAEE,yBH/HmC;CGgIpC;;AACD;EACE,kBAAkB;CACnB;;AACD;EACE,eAAe;CAChB;;AAOD;EG7LI,aAAa;EACb,eAAe;CAChB;;AH2LH;EGzLI,YAAY;CACb;;AH6LD;EALF;IAOM,YAAY;IACZ,aAA6B;IAC7B,YAAY;IACZ,kBAAkB;IIlNtB,iBAAiB;IACjB,wBAAwB;IACxB,oBAAoB;GJkNjB;EAZL;IAcM,mBH2nB6B;GG1nB9B;CZkoDJ;;AYznDD;;EAGE,aAAa;EACb,kCH1N8B;CG2N/B;;AACD;EACE,eAAe;CAEhB;;AAGD;EACE,mBHhL6B;EGiL7B,iBHjL6B;EGkL7B,kBH4mB4C;EG3mB5C,+BHrO8B;CG6P/B;;AA5BD;;;EAUM,iBAAiB;CAClB;;AAXL;;;EAmBI,eAAe;EACf,eAAe;EACf,yBHtMiC;EGuMjC,eHxP4B;CG6P7B;;AA3BH;;;EAyBM,uBAAuB;CACxB;;AAOL;;EAEE,oBAAoB;EACpB,gBAAgB;EAChB,gCHtQ8B;EGuQ9B,eAAe;EACf,kBAAkB;CAWnB;;AAjBD;;;;;;EAYe,YAAY;CAAI;;AAZ/B;;;;;;EAcM,uBAAuB;CACxB;;AAKL;EACE,oBHrO6B;EGsO7B,mBAAmB;EACnB,yBHzOmC;CG0OpC;;AKnSD;;;;EAIE,+DRsCyE;CQrC1E;;AAGD;EACE,iBAAiB;EACjB,eAAe;EACf,eRmzBmC;EQlzBnC,0BRmzBmC;EQlzBnC,mBR0F6B;CQzF9B;;AAGD;EACE,iBAAiB;EACjB,eAAe;EACf,YR6yBgC;EQ5yBhC,uBR6yBgC;EQ5yBhC,mBRmF6B;EQlF7B,+CAA+B;CAQhC;;AAdD;EASI,WAAW;EACX,gBAAgB;EAChB,kBAAkB;EAClB,iBAAiB;CAClB;;AAIH;EACE,eAAe;EACf,eAAgC;EAChC,iBAAkC;EAClC,gBAA2B;EAC3B,yBRkBmC;EQjBnC,sBAAsB;EACtB,sBAAsB;EACtB,eRpC8B;EQqC9B,0BRyxBmC;EQxxBnC,uBR0xBgC;EQzxBhC,mBR0D6B;CQ/C9B;;AAtBD;EAeI,WAAW;EACX,mBAAmB;EACnB,eAAe;EACf,sBAAsB;EACtB,8BAA8B;EAC9B,iBAAiB;CAClB;;AAIH;EACE,kBR2wBiC;EQ1wBjC,mBAAmB;CACpB;;AC3DD;ECHE,mBAAmB;EACnB,kBAAkB;EAClB,mBAAoB;EACpB,oBAAmB;CDYpB;;AAZD;EHMI,aAAa;EACb,eAAe;CAChB;;AGRH;EHUI,YAAY;CACb;;AGRD;EAHF;IAII,aT2UiC;GSnUpC;ClBg+DA;;AkBt+DC;EANF;IAOI,aT6UiC;GSxUpC;ClBs+DA;;AkBz+DC;EATF;IAUI,cT+UkC;GS7UrC;ClB4+DA;;AkBp+DD;ECvBE,mBAAmB;EACnB,kBAAkB;EAClB,mBAAoB;EACpB,oBAAmB;CDsBpB;;AAFD;EHdI,aAAa;EACb,eAAe;CAChB;;AGYH;EHVI,YAAY;CACb;;AGkBH;ECvBE,mBAAkB;EAClB,oBAAmB;CDwBpB;;AAFD;EHvBI,aAAa;EACb,eAAe;CAChB;;AGqBH;EHnBI,YAAY;CACb;;AKVD;EACE,mBAAmB;EAEnB,gBAAgB;EAEhB,mBAAmB;EACnB,oBAAoB;CACrB;;AASD;EACE,YAAY;CACb;;AAMC;EACE,qBAAiB;CAClB;;AAFD;EACE,sBAAiB;CAClB;;AAFD;EACE,WAAiB;CAClB;;AAFD;EACE,sBAAiB;CAClB;;AAFD;EACE,sBAAiB;CAClB;;AAFD;EACE,WAAiB;CAClB;;AAFD;EACE,sBAAiB;CAClB;;AAFD;EACE,sBAAiB;CAClB;;AAFD;EACE,WAAiB;CAClB;;AAFD;EACE,sBAAiB;CAClB;;AAFD;EACE,sBAAiB;CAClB;;AAFD;EACE,YAAiB;CAClB;;AAkBD;EACE,YAAY;CACb;;AAPD;EACE,qBAAiB;CAClB;;AAFD;EACE,sBAAiB;CAClB;;AAFD;EACE,WAAiB;CAClB;;AAFD;EACE,sBAAiB;CAClB;;AAFD;EACE,sBAAiB;CAClB;;AAFD;EACE,WAAiB;CAClB;;AAFD;EACE,sBAAiB;CAClB;;AAFD;EACE,sBAAiB;CAClB;;AAFD;EACE,WAAiB;CAClB;;AAFD;EACE,sBAAiB;CAClB;;AAFD;EACE,sBAAiB;CAClB;;AAFD;EACE,YAAiB;CAClB;;AAPD;EACE,WAAW;CACZ;;AAPD;EACE,oBAAgB;CACjB;;AAFD;EACE,qBAAgB;CACjB;;AAFD;EACE,UAAgB;CACjB;;AAFD;EACE,qBAAgB;CACjB;;AAFD;EACE,qBAAgB;CACjB;;AAFD;EACE,UAAgB;CACjB;;AAFD;EACE,qBAAgB;CACjB;;AAFD;EACE,qBAAgB;CACjB;;AAFD;EACE,UAAgB;CACjB;;AAFD;EACE,qBAAgB;CACjB;;AAFD;EACE,qBAAgB;CACjB;;AAFD;EACE,WAAgB;CACjB;;AAkBD;EACE,gBAAuB;CACxB;;AAFD;EACE,2BAAuB;CACxB;;AAFD;EACE,4BAAuB;CACxB;;AAFD;EACE,iBAAuB;CACxB;;AAFD;EACE,4BAAuB;CACxB;;AAFD;EACE,4BAAuB;CACxB;;AAFD;EACE,iBAAuB;CACxB;;AAFD;EACE,4BAAuB;CACxB;;AAFD;EACE,4BAAuB;CACxB;;AAFD;EACE,iBAAuB;CACxB;;AAFD;EACE,4BAAuB;CACxB;;AAFD;EACE,4BAAuB;CACxB;;AAFD;EACE,kBAAuB;CACxB;;AFEL;EErCE;IACE,YAAY;GACb;EAMC;IACE,qBAAiB;GAClB;EAFD;IACE,sBAAiB;GAClB;EAFD;IACE,WAAiB;GAClB;EAFD;IACE,sBAAiB;GAClB;EAFD;IACE,sBAAiB;GAClB;EAFD;IACE,WAAiB;GAClB;EAFD;IACE,sBAAiB;GAClB;EAFD;IACE,sBAAiB;GAClB;EAFD;IACE,WAAiB;GAClB;EAFD;IACE,sBAAiB;GAClB;EAFD;IACE,sBAAiB;GAClB;EAFD;IACE,YAAiB;GAClB;EAkBD;IACE,YAAY;GACb;EAPD;IACE,qBAAiB;GAClB;EAFD;IACE,sBAAiB;GAClB;EAFD;IACE,WAAiB;GAClB;EAFD;IACE,sBAAiB;GAClB;EAFD;IACE,sBAAiB;GAClB;EAFD;IACE,WAAiB;GAClB;EAFD;IACE,sBAAiB;GAClB;EAFD;IACE,sBAAiB;GAClB;EAFD;IACE,WAAiB;GAClB;EAFD;IACE,sBAAiB;GAClB;EAFD;IACE,sBAAiB;GAClB;EAFD;IACE,YAAiB;GAClB;EAPD;IACE,WAAW;GACZ;EAPD;IACE,oBAAgB;GACjB;EAFD;IACE,qBAAgB;GACjB;EAFD;IACE,UAAgB;GACjB;EAFD;IACE,qBAAgB;GACjB;EAFD;IACE,qBAAgB;GACjB;EAFD;IACE,UAAgB;GACjB;EAFD;IACE,qBAAgB;GACjB;EAFD;IACE,qBAAgB;GACjB;EAFD;IACE,UAAgB;GACjB;EAFD;IACE,qBAAgB;GACjB;EAFD;IACE,qBAAgB;GACjB;EAFD;IACE,WAAgB;GACjB;EAkBD;IACE,gBAAuB;GACxB;EAFD;IACE,2BAAuB;GACxB;EAFD;IACE,4BAAuB;GACxB;EAFD;IACE,iBAAuB;GACxB;EAFD;IACE,4BAAuB;GACxB;EAFD;IACE,4BAAuB;GACxB;EAFD;IACE,iBAAuB;GACxB;EAFD;IACE,4BAAuB;GACxB;EAFD;IACE,4BAAuB;GACxB;EAFD;IACE,iBAAuB;GACxB;EAFD;IACE,4BAAuB;GACxB;EAFD;IACE,4BAAuB;GACxB;EAFD;IACE,kBAAuB;GACxB;CpBw1EJ;;AkB70ED;EE9CE;IACE,YAAY;GACb;EAMC;IACE,qBAAiB;GAClB;EAFD;IACE,sBAAiB;GAClB;EAFD;IACE,WAAiB;GAClB;EAFD;IACE,sBAAiB;GAClB;EAFD;IACE,sBAAiB;GAClB;EAFD;IACE,WAAiB;GAClB;EAFD;IACE,sBAAiB;GAClB;EAFD;IACE,sBAAiB;GAClB;EAFD;IACE,WAAiB;GAClB;EAFD;IACE,sBAAiB;GAClB;EAFD;IACE,sBAAiB;GAClB;EAFD;IACE,YAAiB;GAClB;EAkBD;IACE,YAAY;GACb;EAPD;IACE,qBAAiB;GAClB;EAFD;IACE,sBAAiB;GAClB;EAFD;IACE,WAAiB;GAClB;EAFD;IACE,sBAAiB;GAClB;EAFD;IACE,sBAAiB;GAClB;EAFD;IACE,WAAiB;GAClB;EAFD;IACE,sBAAiB;GAClB;EAFD;IACE,sBAAiB;GAClB;EAFD;IACE,WAAiB;GAClB;EAFD;IACE,sBAAiB;GAClB;EAFD;IACE,sBAAiB;GAClB;EAFD;IACE,YAAiB;GAClB;EAPD;IACE,WAAW;GACZ;EAPD;IACE,oBAAgB;GACjB;EAFD;IACE,qBAAgB;GACjB;EAFD;IACE,UAAgB;GACjB;EAFD;IACE,qBAAgB;GACjB;EAFD;IACE,qBAAgB;GACjB;EAFD;IACE,UAAgB;GACjB;EAFD;IACE,qBAAgB;GACjB;EAFD;IACE,qBAAgB;GACjB;EAFD;IACE,UAAgB;GACjB;EAFD;IACE,qBAAgB;GACjB;EAFD;IACE,qBAAgB;GACjB;EAFD;IACE,WAAgB;GACjB;EAkBD;IACE,gBAAuB;GACxB;EAFD;IACE,2BAAuB;GACxB;EAFD;IACE,4BAAuB;GACxB;EAFD;IACE,iBAAuB;GACxB;EAFD;IACE,4BAAuB;GACxB;EAFD;IACE,4BAAuB;GACxB;EAFD;IACE,iBAAuB;GACxB;EAFD;IACE,4BAAuB;GACxB;EAFD;IACE,4BAAuB;GACxB;EAFD;IACE,iBAAuB;GACxB;EAFD;IACE,4BAAuB;GACxB;EAFD;IACE,4BAAuB;GACxB;EAFD;IACE,kBAAuB;GACxB;CpBu/EJ;;AkBn+ED;EEvDE;IACE,YAAY;GACb;EAMC;IACE,qBAAiB;GAClB;EAFD;IACE,sBAAiB;GAClB;EAFD;IACE,WAAiB;GAClB;EAFD;IACE,sBAAiB;GAClB;EAFD;IACE,sBAAiB;GAClB;EAFD;IACE,WAAiB;GAClB;EAFD;IACE,sBAAiB;GAClB;EAFD;IACE,sBAAiB;GAClB;EAFD;IACE,WAAiB;GAClB;EAFD;IACE,sBAAiB;GAClB;EAFD;IACE,sBAAiB;GAClB;EAFD;IACE,YAAiB;GAClB;EAkBD;IACE,YAAY;GACb;EAPD;IACE,qBAAiB;GAClB;EAFD;IACE,sBAAiB;GAClB;EAFD;IACE,WAAiB;GAClB;EAFD;IACE,sBAAiB;GAClB;EAFD;IACE,sBAAiB;GAClB;EAFD;IACE,WAAiB;GAClB;EAFD;IACE,sBAAiB;GAClB;EAFD;IACE,sBAAiB;GAClB;EAFD;IACE,WAAiB;GAClB;EAFD;IACE,sBAAiB;GAClB;EAFD;IACE,sBAAiB;GAClB;EAFD;IACE,YAAiB;GAClB;EAPD;IACE,WAAW;GACZ;EAPD;IACE,oBAAgB;GACjB;EAFD;IACE,qBAAgB;GACjB;EAFD;IACE,UAAgB;GACjB;EAFD;IACE,qBAAgB;GACjB;EAFD;IACE,qBAAgB;GACjB;EAFD;IACE,UAAgB;GACjB;EAFD;IACE,qBAAgB;GACjB;EAFD;IACE,qBAAgB;GACjB;EAFD;IACE,UAAgB;GACjB;EAFD;IACE,qBAAgB;GACjB;EAFD;IACE,qBAAgB;GACjB;EAFD;IACE,WAAgB;GACjB;EAkBD;IACE,gBAAuB;GACxB;EAFD;IACE,2BAAuB;GACxB;EAFD;IACE,4BAAuB;GACxB;EAFD;IACE,iBAAuB;GACxB;EAFD;IACE,4BAAuB;GACxB;EAFD;IACE,4BAAuB;GACxB;EAFD;IACE,iBAAuB;GACxB;EAFD;IACE,4BAAuB;GACxB;EAFD;IACE,4BAAuB;GACxB;EAFD;IACE,iBAAuB;GACxB;EAFD;IACE,4BAAuB;GACxB;EAFD;IACE,4BAAuB;GACxB;EAFD;IACE,kBAAuB;GACxB;CpBspFJ;;AqB9sFD;EACE,8BZgIyC;CY/H1C;;AACD;EACE,iBZwHiC;EYvHjC,oBZuHiC;EYtHjC,eZG8B;EYF9B,iBAAiB;CAClB;;AACD;EACE,iBAAiB;CAClB;;AAKD;EACE,YAAY;EACZ,gBAAgB;EAChB,oBZyC6B;CYD9B;;AA3CD;;;;;;EAWQ,aZiG2B;EYhG3B,yBZ8B6B;EY7B7B,oBAAoB;EACpB,2BZ2G4B;CY1G7B;;AAfP;EAoBI,uBAAuB;EACvB,8BZoGgC;CYnGjC;;AAtBH;;;;;;EA8BQ,cAAc;CACf;;AA/BP;EAoCI,2BZqFgC;CYpFjC;;AArCH;EAyCI,uBZjCwB;CYkCzB;;AAMH;;;;;;EAOQ,aZuD2B;CYtD5B;;AAUP;EACE,uBZsDkC;CYrCnC;;AAlBD;;;;;;EAQQ,uBZ+C4B;CY9C7B;;AATP;;EAeM,yBAAyB;CAC1B;;AASL;EAEI,0BZsBmC;CYrBpC;;AAQH;EAEI,0BZamC;CYZpC;;AAQH;EACE,iBAAiB;EACjB,YAAY;EACZ,sBAAsB;CACvB;;AACD;;EAIM,iBAAiB;EACjB,YAAY;EACZ,oBAAoB;CACrB;;AC7IH;;;;;;;;;;;;EAII,0BbiIiC;CahIlC;;AAKH;;;;;EAEI,0BAAwB;CACzB;;AAbH;;;;;;;;;;;;EAII,0Bb+ekC;Ca9enC;;AAKH;;;;;EAEI,0BAAwB;CACzB;;AAbH;;;;;;;;;;;;EAII,0BbmfkC;CalfnC;;AAKH;;;;;EAEI,0BAAwB;CACzB;;AAbH;;;;;;;;;;;;EAII,0BbufkC;CatfnC;;AAKH;;;;;EAEI,0BAAwB;CACzB;;AAbH;;;;;;;;;;;;EAII,0Bb2fkC;Ca1fnC;;AAKH;;;;;EAEI,0BAAwB;CACzB;;ADwJL;EACE,iBAAiB;EACjB,kBAAkB;CA6DnB;;AA3DC;EAJF;IAKI,YAAY;IACZ,oBAAqC;IACrC,mBAAmB;IACnB,6CAA6C;IAC7C,uBZrCgC;GY2FnC;EA/DD;IAaM,iBAAiB;GAalB;EA1BL;;;;;;IAsBY,oBAAoB;GACrB;EAvBX;IA8BM,UAAU;GA+BX;EA7DL;;;;;;IAuCY,eAAe;GAChB;EAxCX;;;;;;IA2CY,gBAAgB;GACjB;EA5CX;;;;IAwDY,iBAAiB;GAClB;CrB6vFV;;AuBv9FD;EACE,WAAW;EACX,UAAU;EACV,UAAU;EAIV,aAAa;CACd;;AAED;EACE,eAAe;EACf,YAAY;EACZ,WAAW;EACX,oBd0C6B;EczC7B,gBAA2B;EAC3B,qBAAqB;EACrB,edd8B;Ece9B,UAAU;EACV,iCdmMsC;CclMvC;;AAED;EACE,sBAAsB;EACtB,gBAAgB;EAChB,mBAAmB;EACnB,kBAAkB;CACnB;;AAUD;Ef8BU,uBe7BsB;CAC/B;;AAGD;;EAEE,gBAAgB;EAChB,mBAAmB;EACnB,oBAAoB;CACrB;;AAED;EACE,eAAe;CAChB;;AAGD;EACE,eAAe;EACf,YAAY;CACb;;AAGD;;EAEE,aAAa;CACd;;AAGD;;;EbrEE,2CAA2C;EAC3C,qBAAqB;CawEtB;;AAGD;EACE,eAAe;EACf,iBAAoC;EACpC,gBdlC4B;EcmC5B,yBdvBmC;EcwBnC,ed1E8B;Cc2E/B;;AAyBD;EACE,eAAe;EACf,YAAY;EACZ,adiGqD;EchGrD,kBdtB8B;EcuB9B,gBdnE4B;EcoE5B,yBdxDmC;EcyDnC,ed3G8B;Ec4G9B,uBdmEmC;EclEnC,uBAAuB;EACvB,uBdwEmC;EcvEnC,mBdf6B;EDxCrB,iDewDgC;Ef8DhC,yEe7DsE;CAgC/E;;AA7CD;ECxDI,sBfsJoC;EerJpC,WAAW;EhBWL,mFgBdS;CAKhB;;ADqDH;EfVI,YC2GiC;ED1GjC,WAAW;CACZ;;AeQH;EfP4B,YCwGS;CDxGQ;;AeO7C;EfNkC,YCuGG;CDvGc;;AeMnD;EAuBI,UAAU;EACV,8BAA8B;CAC/B;;AAzBH;;EAmCI,0BdrI4B;EcsI5B,WAAW;CACZ;;AArCH;;EAyCI,oBd6EwC;Cc5EzC;;AAMH;EACE,aAAa;CACd;;AAUD;EACE,yBAAyB;CAC1B;;AAYD;EACE;;;;IAKI,kBdoBiD;GcnBlD;EANH;;;;;;;;;;;;;;;;;;;;;;IAUI,kBdmBiC;GclBlC;EAXH;;;;;;;;;;;;;;;;;;;;;;IAeI,kBdYgC;GcXjC;CvBg9FJ;;AuBt8FD;EACE,oBdKmC;CcJpC;;AAOD;;EAEE,mBAAmB;EACnB,eAAe;EACf,iBAAiB;EACjB,oBAAoB;CASrB;;AAdD;;EAQI,iBdtK2B;EcuK3B,mBAAmB;EACnB,iBAAiB;EACjB,oBAAoB;EACpB,gBAAgB;CACjB;;AAEH;;;;EAIE,mBAAmB;EACnB,mBAAmB;EACnB,mBAAmB;CACpB;;AAED;;EAEE,iBAAiB;CAClB;;AAGD;;EAEE,mBAAmB;EACnB,sBAAsB;EACtB,mBAAmB;EACnB,iBAAiB;EACjB,uBAAuB;EACvB,oBAAoB;EACpB,gBAAgB;CACjB;;AACD;;EAEE,cAAc;EACd,kBAAkB;CACnB;;AAMD;;;;;;EAKI,oBd/CwC;CcgDzC;;AAGH;;;;;EAII,oBdvDwC;CcwDzC;;AAGH;;;;;EAKM,oBdhEsC;CciEvC;;AAUL;EAEE,iBAAoC;EACpC,oBAAuC;EAEvC,iBAAiB;EACjB,iBAAkC;CAOnC;;AAbD;;;;;EAUI,gBAAgB;EAChB,iBAAiB;CAClB;;ACxPD;;;EACE,afkJmC;EejJnC,kBf6B4B;Ee5B5B,gBfpB0B;EeqB1B,iBfiC2B;EehC3B,mBfoC2B;CenC5B;;AAED;;;EACE,af0ImC;EezInC,kBfyImC;CexIpC;;AAED;;;;;;;EACE,aAAa;CACd;;ADsPH;EAEI,adpHmC;EcqHnC,kBdzO4B;Ec0O5B,gBd1R0B;Ec2R1B,iBdrO2B;EcsO3B,mBdlO2B;CcmO5B;;AAPH;EASI,ad3HmC;Ec4HnC,kBd5HmC;Cc6HpC;;AAXH;;EAcI,aAAa;CACd;;AAfH;EAiBI,adnImC;EcoInC,iBAAkC;EAClC,kBdzP4B;Ec0P5B,gBd1S0B;Ec2S1B,iBdrP2B;CcsP5B;;AC3RD;;;EACE,afgJkC;Ee/IlC,mBf0B4B;EezB5B,gBfrB0B;EesB1B,uBfgCiC;Ee/BjC,mBfmC2B;CelC5B;;AAED;;;EACE,afwIkC;EevIlC,kBfuIkC;CetInC;;AAED;;;;;;;EACE,aAAa;CACd;;ADgRH;EAEI,adhJkC;EciJlC,mBdtQ4B;EcuQ5B,gBdrT0B;EcsT1B,uBdhQiC;EciQjC,mBd7P2B;Cc8P5B;;AAPH;EASI,advJkC;EcwJlC,kBdxJkC;CcyJnC;;AAXH;;EAcI,aAAa;CACd;;AAfH;EAiBI,ad/JkC;EcgKlC,iBAAkC;EAClC,mBdtR4B;EcuR5B,gBdrU0B;EcsU1B,uBdhRiC;CciRlC;;AAQH;EAEE,mBAAmB;CAMpB;;AARD;EAMI,sBAAkC;CACnC;;AAGH;EACE,mBAAmB;EACnB,OAAO;EACP,SAAS;EACT,WAAW;EACX,eAAe;EACf,Yd9LqD;Ec+LrD,ad/LqD;EcgMrD,kBdhMqD;EciMrD,mBAAmB;EACnB,qBAAqB;CACtB;;AACD;;;;;EAGE,YdrMoC;EcsMpC,adtMoC;EcuMpC,kBdvMoC;CcwMrC;;AACD;;;;;EAGE,Yd1MqC;Ec2MrC,ad3MqC;Ec4MrC,kBd5MqC;Cc6MtC;;AC/ZC;;;;;;;;;;EAUE,efseoC;CererC;;AAED;EACE,sBfkeoC;EDlb9B,iDgB/CkC;CAMzC;;AARD;EAII,sBAAoB;EhB6ChB,kEgB5CsD;CAE3D;;AAGH;EACE,efwdoC;EevdpC,sBfudoC;EetdpC,0BfudoC;CetdrC;;AAED;EACE,efkdoC;CejdrC;;AA/BD;;;;;;;;;;EAUE,ef8eoC;Ce7erC;;AAED;EACE,sBf0eoC;ED1b9B,iDgB/CkC;CAMzC;;AARD;EAII,sBAAoB;EhB6ChB,kEgB5CsD;CAE3D;;AAGH;EACE,efgeoC;Ee/dpC,sBf+doC;Ee9dpC,0Bf+doC;Ce9drC;;AAED;EACE,ef0doC;CezdrC;;AA/BD;;;;;;;;;;EAUE,efkfoC;CejfrC;;AAED;EACE,sBf8eoC;ED9b9B,iDgB/CkC;CAMzC;;AARD;EAII,sBAAoB;EhB6ChB,kEgB5CsD;CAE3D;;AAGH;EACE,efoeoC;EenepC,sBfmeoC;EelepC,0BfmeoC;CelerC;;AAED;EACE,ef8doC;Ce7drC;;AD8YH;EAGI,UAA2B;CAC5B;;AAJH;EAMI,OAAO;CACR;;AASH;EACE,eAAe;EACf,gBAAgB;EAChB,oBAAoB;EACpB,eAAc;CACf;;AAkBC;EAEE;IACE,sBAAsB;IACtB,iBAAiB;IACjB,uBAAuB;GACxB;EAGD;IACE,sBAAsB;IACtB,YAAY;IACZ,uBAAuB;GACxB;EAGD;IACE,sBAAsB;GACvB;EAED;IACE,sBAAsB;IACtB,uBAAuB;GAOxB;EALC;;;IAGE,YAAY;GACb;EAIY;IACb,YAAY;GACb;EAED;IACE,iBAAiB;IACjB,uBAAuB;GACxB;EAID;;IAEE,sBAAsB;IACtB,cAAc;IACd,iBAAiB;IACjB,uBAAuB;GAKxB;EAHC;;IACE,gBAAgB;GACjB;EAEsB;;IAEvB,mBAAmB;IACnB,eAAe;GAChB;EAGa;IACZ,OAAO;GACR;CvB6hGJ;;AuB9gGD;;;;EASI,cAAc;EACd,iBAAiB;EACjB,iBAAoC;CACrC;;AAZH;;EAiBI,iBAAkC;CACnC;;AAlBH;EJ1hBE,mBAAkB;EAClB,oBAAmB;CIgjBlB;;AAvBH;ER1hBI,aAAa;EACb,eAAe;CAChB;;AQwhBH;ERthBI,YAAY;CACb;;AQgjBD;EA3BF;IA6BM,kBAAkB;IAClB,iBAAiB;IACjB,iBAAoC;GACrC;CvBkhGJ;;AuBljGD;EAwCI,YAAY;CACb;;AAOC;EAhDJ;IAkDQ,kBAAqC;IACrC,gBdxiBsB;GcyiBvB;CvBygGN;;AuBrgGG;EAxDJ;IA0DQ,iBAAqC;IACrC,gBd/iBsB;GcgjBvB;CvBwgGN;;AyBrmHD;EACE,sBAAsB;EACtB,iBAAiB;EACjB,oBhB0IqC;EgBzIrC,mBAAmB;EACnB,uBAAuB;EACvB,2BAA2B;EAC3B,gBAAgB;EAChB,uBAAuB;EACvB,8BAA8B;EAC9B,oBAAoB;EC0CpB,kBjBmC8B;EiBlC9B,gBjBV4B;EiBW5B,yBjBCmC;EiBAnC,mBjB8C6B;ED4G7B,0BiBrMyB;EjBsMtB,uBiBtMsB;EjBuMrB,sBiBvMqB;EjBwMjB,kBiBxMiB;CAkC1B;;AA9CD;EfFE,2CAA2C;EAC3C,qBAAqB;CeqBlB;;AApBL;EA0BI,YhBqHiC;EgBpHjC,sBAAsB;CACvB;;AA5BH;EAgCI,WAAW;EACX,uBAAuB;EjB4BjB,iDiB3BkC;CACzC;;AAnCH;;EAwCI,oBhBuLwC;EkBpO1C,cF8CsB;EE3CtB,0BAAa;EnB+DL,iBiBnBkB;CACzB;;AAKH;;EAGI,qBAAqB;CACtB;;AAOH;EC7DE,YjBiJmC;EiBhJnC,uBjBiJmC;EiBhJnC,mBjBiJmC;CgBpFpC;;AAFD;ECvDI,YjB2IiC;EiB1IjC,0BAAwB;EACpB,sBAAoB;CACzB;;ADoDH;EClDI,YjBsIiC;EiBrIjC,0BAAwB;EACpB,sBAAoB;CACzB;;AD+CH;;EC3CI,YjB+HiC;EiB9HjC,0BAAwB;EACpB,sBAAoB;CASzB;;ADgCH;;;;ECpCM,YjBwH+B;EiBvH/B,0BAAwB;EACpB,sBAAoB;CACzB;;ADiCL;;EC5BI,uBAAuB;CACxB;;AD2BH;;;;ECpBM,uBjByG+B;EiBxG3B,mBjByG2B;CiBxGhC;;AAGH;EACE,YjBmGiC;EiBlGjC,uBjBiGiC;CiBhGlC;;ADeH;EChEE,YjBqJmC;EiBpJnC,0BjBU4B;EiBT5B,sBjBqJqC;CgBrFtC;;AAFD;EC1DI,YjB+IiC;EiB9IjC,0BAAwB;EACpB,sBAAoB;CACzB;;ADuDH;ECrDI,YjB0IiC;EiBzIjC,0BAAwB;EACpB,sBAAoB;CACzB;;ADkDH;;EC9CI,YjBmIiC;EiBlIjC,0BAAwB;EACpB,sBAAoB;CASzB;;ADmCH;;;;ECvCM,YjB4H+B;EiB3H/B,0BAAwB;EACpB,sBAAoB;CACzB;;ADoCL;;EC/BI,uBAAuB;CACxB;;AD8BH;;;;ECvBM,0BjB9BwB;EiB+BpB,sBjB6G6B;CiB5GlC;;AAGH;EACE,ejBpC0B;EiBqC1B,uBjBqGiC;CiBpGlC;;ADmBH;ECpEE,YjByJmC;EiBxJnC,0BjBW6B;EiBV7B,sBjByJqC;CgBrFtC;;AAFD;EC9DI,YjBmJiC;EiBlJjC,0BAAwB;EACpB,sBAAoB;CACzB;;AD2DH;ECzDI,YjB8IiC;EiB7IjC,0BAAwB;EACpB,sBAAoB;CACzB;;ADsDH;;EClDI,YjBuIiC;EiBtIjC,0BAAwB;EACpB,sBAAoB;CASzB;;ADuCH;;;;EC3CM,YjBgI+B;EiB/H/B,0BAAwB;EACpB,sBAAoB;CACzB;;ADwCL;;ECnCI,uBAAuB;CACxB;;ADkCH;;;;EC3BM,0BjB7ByB;EiB8BrB,sBjBiH6B;CiBhHlC;;AAGH;EACE,ejBnC2B;EiBoC3B,uBjByGiC;CiBxGlC;;ADuBH;ECxEE,YjB6JmC;EiB5JnC,0BjBY6B;EiBX7B,sBjB6JqC;CgBrFtC;;AAFD;EClEI,YjBuJiC;EiBtJjC,0BAAwB;EACpB,sBAAoB;CACzB;;AD+DH;EC7DI,YjBkJiC;EiBjJjC,0BAAwB;EACpB,sBAAoB;CACzB;;AD0DH;;ECtDI,YjB2IiC;EiB1IjC,0BAAwB;EACpB,sBAAoB;CASzB;;AD2CH;;;;EC/CM,YjBoI+B;EiBnI/B,0BAAwB;EACpB,sBAAoB;CACzB;;AD4CL;;ECvCI,uBAAuB;CACxB;;ADsCH;;;;EC/BM,0BjB5ByB;EiB6BrB,sBjBqH6B;CiBpHlC;;AAGH;EACE,ejBlC2B;EiBmC3B,uBjB6GiC;CiB5GlC;;AD2BH;EC5EE,YjBiKmC;EiBhKnC,0BjBa6B;EiBZ7B,sBjBiKqC;CgBrFtC;;AAFD;ECtEI,YjB2JiC;EiB1JjC,0BAAwB;EACpB,sBAAoB;CACzB;;ADmEH;ECjEI,YjBsJiC;EiBrJjC,0BAAwB;EACpB,sBAAoB;CACzB;;AD8DH;;EC1DI,YjB+IiC;EiB9IjC,0BAAwB;EACpB,sBAAoB;CASzB;;AD+CH;;;;ECnDM,YjBwI+B;EiBvI/B,0BAAwB;EACpB,sBAAoB;CACzB;;ADgDL;;EC3CI,uBAAuB;CACxB;;AD0CH;;;;ECnCM,0BjB3ByB;EiB4BrB,sBjByH6B;CiBxHlC;;AAGH;EACE,ejBjC2B;EiBkC3B,uBjBiHiC;CiBhHlC;;AD+BH;EChFE,YjBqKmC;EiBpKnC,0BjBc6B;EiBb7B,sBjBqKqC;CgBrFtC;;AAFD;EC1EI,YjB+JiC;EiB9JjC,0BAAwB;EACpB,sBAAoB;CACzB;;ADuEH;ECrEI,YjB0JiC;EiBzJjC,0BAAwB;EACpB,sBAAoB;CACzB;;ADkEH;;EC9DI,YjBmJiC;EiBlJjC,0BAAwB;EACpB,sBAAoB;CASzB;;ADmDH;;;;ECvDM,YjB4I+B;EiB3I/B,0BAAwB;EACpB,sBAAoB;CACzB;;ADoDL;;EC/CI,uBAAuB;CACxB;;AD8CH;;;;ECvCM,0BjB1ByB;EiB2BrB,sBjB6H6B;CiB5HlC;;AAGH;EACE,ejBhC2B;EiBiC3B,uBjBqHiC;CiBpHlC;;ADwCH;EACE,ehB/E4B;EgBgF5B,oBAAoB;EACpB,iBAAiB;CA8BlB;;AAjCD;;EAUI,8BAA8B;EjBpCxB,iBiBqCkB;CACzB;;AAZH;EAiBI,0BAA0B;CAC3B;;AAlBH;EAqBI,ehBhF0B;EgBiF1B,2BhB/E6B;EgBgF7B,8BAA8B;CAC/B;;AAxBH;;;EA6BM,ehB9G0B;EgB+G1B,sBAAsB;CACvB;;AAQL;EC1EE,mBjBsC8B;EiBrC9B,gBjBT4B;EiBU5B,uBjB4CmC;EiB3CnC,mBjB+C6B;CgB2B9B;;AACD;EC9EE,kBjByC8B;EiBxC9B,gBjBR4B;EiBS5B,iBjB6C6B;EiB5C7B,mBjBgD6B;CgB8B9B;;AACD;EClFE,iBjB4C6B;EiB3C7B,gBjBR4B;EiBS5B,iBjB6C6B;EiB5C7B,mBjBgD6B;CgBiC9B;;AAMD;EACE,eAAe;EACf,YAAY;CACb;;AAGD;EACE,gBAAgB;CACjB;;AAGD;;;EAII,YAAY;CACb;;AG7JH;EACE,WAAW;EpBiLH,iCoBhL+B;CAIxC;;AAND;EAII,WAAW;CACZ;;AAGH;EACE,cAAc;CAKf;;AAND;EAGc,eAAe;CAAI;;AAKjC;EAAoB,mBAAmB;CAAI;;AAE3C;EAAoB,yBAAyB;CAAI;;AAEjD;EACE,mBAAmB;EACnB,UAAU;EACV,iBAAiB;EpB+JT,wCoB9JuC;EpBsKvC,2BoBrKyB;EpByKzB,iCoBxKgC;CACzC;;AC9BD;EACE,sBAAsB;EACtB,SAAS;EACT,UAAU;EACV,iBAAiB;EACjB,uBAAuB;EACvB,uBAAsC;EACtC,yBAAwC;EACxC,oCAAiD;EACjD,mCAAiD;CAClD;;AAGD;;EAEE,mBAAmB;CACpB;;AAGD;EACE,WAAW;CACZ;;AAGD;EACE,mBAAmB;EACnB,UAAU;EACV,QAAQ;EACR,cpBmP6B;EoBlP7B,cAAc;EACd,YAAY;EACZ,iBAAiB;EACjB,eAAe;EACf,gBAAgB;EAChB,iBAAiB;EACjB,gBpBU4B;EoBT5B,iBAAiB;EACjB,uBpBoMmC;EoBnMnC,uBpBuMmC;EoBtMnC,sCpBoMmC;EoBnMnC,mBpB+D6B;EDxCrB,4CqBtB2B;EACnC,6BAA6B;CAyB9B;;AA3CD;EAwBI,SAAS;EACT,WAAW;CACZ;;AA1BH;ECzBE,YAAY;EACZ,cAA2C;EAC3C,iBAAiB;EACjB,0BrB6OsC;CoBxLrC;;AA/BH;EAmCI,eAAe;EACf,kBAAkB;EAClB,YAAY;EACZ,oBAAoB;EACpB,yBpBNiC;EoBOjC,epB1D4B;EoB2D5B,oBAAoB;CACrB;;AAIH;EAGI,sBAAsB;EACtB,epB0KmC;EoBzKnC,0BpB2KoC;CoB1KrC;;AAIH;EAII,YpBwB4B;EoBvB5B,sBAAsB;EACtB,WAAW;EACX,0BpB5E0B;CoB6E3B;;AAOH;EAII,epB3F4B;CoB4F7B;;AALH;EAUI,sBAAsB;EACtB,8BAA8B;EAC9B,uBAAuB;EE3GzB,oEAAmE;EF6GjE,oBpBoHwC;CoBnHzC;;AAIH;EAGI,eAAe;CAChB;;AAJH;EAQI,WAAW;CACZ;;AAOH;EACE,WAAW;EACX,SAAS;CACV;;AAOD;EACE,QAAQ;EACR,YAAY;CACb;;AAGD;EACE,eAAe;EACf,kBAAkB;EAClB,gBpBtG4B;EoBuG5B,yBpB7FmC;EoB8FnC,epB/I8B;EoBgJ9B,oBAAoB;CACrB;;AAGD;EACE,gBAAgB;EAChB,QAAQ;EACR,SAAS;EACT,UAAU;EACV,OAAO;EACP,aAA0B;CAC3B;;AAGD;EACE,SAAS;EACT,WAAW;CACZ;;AAOD;;EAII,cAAc;EACd,0BAAuC;EACvC,4BAAyC;EACzC,YAAY;CACb;;AARH;;EAWI,UAAU;EACV,aAAa;EACb,mBAAmB;CACpB;;AAQH;EACE;IAEI,SAAS;IAAE,WAAW;GACvB;EAHH;IAOI,QAAQ;IAAE,YAAY;GACvB;C7B0gIJ;;AgC1tID;;EAEE,mBAAmB;EACnB,sBAAsB;EACtB,uBAAuB;CAYxB;;AAhBD;;EAMI,mBAAmB;EACnB,YAAY;CAQb;;AAfH;;;;;EAaM,WAAW;CACZ;;AAKL;;;;EAKI,kBAAkB;CACnB;;AAIH;EACE,kBAAkB;CAanB;;AAdD;EjBnBI,aAAa;EACb,eAAe;CAChB;;AiBiBH;EjBfI,YAAY;CACb;;AiBcH;;;EAOI,YAAY;CACb;;AARH;;;EAYI,iBAAiB;CAClB;;AAGH;EACE,iBAAiB;CAClB;;AAGD;EACE,eAAe;CAIhB;;AALD;EChDE,8BDmDgC;EClD7B,2BDkD6B;CAC/B;;AAGH;;EC/CE,6BDiD6B;EChD1B,0BDgD0B;CAC9B;;AAGD;EACE,YAAY;CACb;;AACD;EACE,iBAAiB;CAClB;;AACD;;ECnEE,8BDsEgC;ECrE7B,2BDqE6B;CAC/B;;AAEH;ECjEE,6BDkE6B;ECjE1B,0BDiE0B;CAC9B;;AAGD;;EAEE,WAAW;CACZ;;AAgBD;EACE,kBAAkB;EAClB,mBAAmB;CACpB;;AACD;EACE,mBAAmB;EACnB,oBAAoB;CACrB;;AAID;ExB9CU,iDwB+CgC;CAMzC;;AAPD;ExB9CU,iBwBmDkB;CACzB;;AAKH;EACE,eAAe;CAChB;;AAED;EACE,wBAAqD;EACrD,uBAAuB;CACxB;;AAED;EACE,wBvBf6B;CuBgB9B;;AAMD;;;EAII,eAAe;EACf,YAAY;EACZ,YAAY;EACZ,gBAAgB;CACjB;;AARH;EjBhII,aAAa;EACb,eAAe;CAChB;;AiB8HH;EjB5HI,YAAY;CACb;;AiB2HH;EAcM,YAAY;CACb;;AAfL;;;;EAsBI,iBAAiB;EACjB,eAAe;CAChB;;AAGH;EAEI,iBAAiB;CAClB;;AAHH;ECvKE,6BxB0G6B;EwBzG5B,4BxByG4B;EwBlG7B,8BDqKiC;ECpKhC,6BDoKgC;CAChC;;AAPH;ECvKE,2BDgL8B;EC/K7B,0BD+K6B;ECxK9B,gCxBkG6B;EwBjG5B,+BxBiG4B;CuBwE5B;;AAEH;EACE,iBAAiB;CAClB;;AACD;;EC/KE,8BDkLiC;ECjLhC,6BDiLgC;CAChC;;AAEH;EC7LE,2BD8L4B;EC7L3B,0BD6L2B;CAC7B;;AAMD;EACE,eAAe;EACf,YAAY;EACZ,oBAAoB;EACpB,0BAA0B;CAc3B;;AAlBD;;EAOI,YAAY;EACZ,oBAAoB;EACpB,UAAU;CACX;;AAVH;EAYI,YAAY;CACb;;AAbH;EAgBI,WAAW;CACZ;;AhC8tIH;;;;EgCzsIM,mBAAmB;EACnB,uBAAU;EACV,qBAAqB;CACtB;;AE3OL;EACE,mBAAmB;EACnB,eAAe;EACf,0BAA0B;CA2B3B;;AA9BD;EAOI,YAAY;EACZ,gBAAgB;EAChB,iBAAiB;CAClB;;AAVH;EAeI,mBAAmB;EACnB,WAAW;EAKX,YAAY;EAEZ,YAAY;EACZ,iBAAiB;CAKlB;;AA7BH;EA2BM,WAAW;CACZ;;AAuBL;;;EAGE,oBAAoB;CAKrB;;AARD;;;EAMI,iBAAiB;CAClB;;AAGH;;EAEE,UAAU;EACV,oBAAoB;EACpB,uBAAuB;CACxB;;AAID;EACE,kBzBkB8B;EyBjB9B,gBzB3B4B;EyB4B5B,oBAAoB;EACpB,eAAe;EACf,ezBpE8B;EyBqE9B,mBAAmB;EACnB,0BzBpE8B;EyBqE9B,uBzB+GmC;EyB9GnC,mBzBwB6B;CyBL9B;;AA5BD;;;EAaI,kBzBY4B;EyBX5B,gBzBrC0B;EyBsC1B,mBzBoB2B;CyBnB5B;;AAhBH;;;EAkBI,mBzBI4B;EyBH5B,gBzB3C0B;EyB4C1B,mBzBc2B;CyBb5B;;AArBH;;EA0BI,cAAc;CACf;;AAIH;;;;;;;EDpGE,8BC2G8B;ED1G3B,2BC0G2B;CAC/B;;AACD;EACE,gBAAgB;CACjB;;AACD;;;;;;;EDxGE,6BC+G6B;ED9G1B,0BC8G0B;CAC9B;;AACD;EACE,eAAe;CAChB;;AAID;EACE,mBAAmB;EAGnB,aAAa;EACb,oBAAoB;CA+BrB;;AApCD;EAUI,mBAAmB;CAUpB;;AApBH;EAYM,kBAAkB;CACnB;;AAbL;EAkBM,WAAW;CACZ;;AAnBL;;EA0BM,mBAAmB;CACpB;;AA3BL;;EAgCM,WAAW;EACX,kBAAkB;CACnB;;AChKL;EACE,iBAAiB;EACjB,gBAAgB;EAChB,iBAAiB;CAyDlB;;AA5DD;EpBOI,aAAa;EACb,eAAe;CAChB;;AoBTH;EpBWI,YAAY;CACb;;AoBZH;EAOI,mBAAmB;EACnB,eAAe;CAyBhB;;AAjCH;EAWM,mBAAmB;EACnB,eAAe;EACf,mB1BqZ+C;C0B/YhD;;AAnBL;EAgBQ,sBAAsB;EACtB,0B1BVwB;C0BWzB;;AAlBP;EAuBM,e1BjB0B;C0B0B3B;;AAhCL;EA2BQ,e1BrBwB;E0BsBxB,sBAAsB;EACtB,8BAA8B;EAC9B,oB1BiMoC;C0BhMrC;;AA/BP;EAwCM,0B1BjC0B;E0BkC1B,sB1BhCwB;C0BiCzB;;AA1CL;ELHE,YAAY;EACZ,cAA2C;EAC3C,iBAAiB;EACjB,0BAJgC;CKwD/B;;AApDH;EA0DI,gBAAgB;CACjB;;AAQH;EACE,8B1BqW8C;C0BlU/C;;AApCD;EAGI,YAAY;EAEZ,oBAAoB;CAyBrB;;AA9BH;EASM,kBAAkB;EAClB,yB1BtB+B;E0BuB/B,8BAA8B;EAC9B,2BAA0D;CAI3D;;AAhBL;EAcQ,mC1BwVwC;C0BvVzC;;AAfP;EAuBQ,e1BrFwB;E0BsFxB,uB1BtEoB;E0BuEpB,uB1BmVwC;E0BlVxC,iCAAiC;EACjC,gBAAgB;CACjB;;AAaP;EAEI,YAAY;CAmBb;;AArBH;EAMM,mB1BbyB;C0Bc1B;;AAPL;EASM,iBAAiB;CAClB;;AAVL;EAiBQ,Y1BnBwB;E0BoBxB,0B1BrHsB;C0BsHvB;;AAOP;EAEI,YAAY;CAKb;;AAPH;EAIM,gBAAgB;EAChB,eAAe;CAChB;;AAWL;EACE,YAAY;CAwBb;;AAzBD;EAII,YAAY;CAKb;;AATH;EAMM,mBAAmB;EACnB,mBAAmB;CACpB;;AARL;EAYI,UAAU;EACV,WAAW;CACZ;;AAED;EAhBF;IAkBM,oBAAoB;IACpB,UAAU;GAIX;EAvBL;IAqBQ,iBAAiB;GAClB;CnC4hJN;;AmCphJD;EACE,iBAAiB;CAyBlB;;AA1BD;EAKI,gBAAgB;EAChB,mB1BtF2B;C0BuF5B;;AAPH;;;EAYI,uB1BgPkD;C0B/OnD;;AAED;EAfF;IAiBM,8B1B2OgD;I0B1OhD,2BAA0D;GAC3D;EAnBL;;;IAuBM,0B1BvLsB;G0BwLvB;CnCuhJJ;;AmC9gJD;EAEI,cAAc;CACf;;AAHH;EAKI,eAAe;CAChB;;AAQH;EAEE,iBAAiB;EF3OjB,2BE6O4B;EF5O3B,0BE4O2B;CAC7B;;ACvOD;EACE,mBAAmB;EACnB,iB3BgWqC;E2B/VrC,oB3BoD6B;E2BnD7B,8BAA8B;CAQ/B;;AAZD;ErBKI,aAAa;EACb,eAAe;CAChB;;AqBPH;ErBSI,YAAY;CACb;;AqBDD;EATF;IAUI,mB3ByF2B;G2BvF9B;CpC0vJA;;AoClvJD;ErBfI,aAAa;EACb,eAAe;CAChB;;AqBaH;ErBXI,YAAY;CACb;;AqBaD;EAHF;IAII,YAAY;GAEf;CpC2vJA;;AoC9uJD;EACE,oBAAoB;EACpB,oB3B4TsC;E2B3TtC,mB3B2TsC;E2B1TtC,kCAAkC;EAClC,mDAA8B;EAE9B,kCAAkC;CA+BnC;;AAtCD;ErBlCI,aAAa;EACb,eAAe;CAChB;;AqBgCH;ErB9BI,YAAY;CACb;;AqB6BH;EAUI,iBAAiB;CAClB;;AAED;EAbF;IAcI,YAAY;IACZ,cAAc;IACd,iBAAiB;GAsBpB;EAtCD;IAmBM,0BAA0B;IAC1B,wBAAwB;IACxB,kBAAkB;IAClB,6BAA6B;GAC9B;EAvBL;IA0BM,oBAAoB;GACrB;EA3BL;;;IAkCM,gBAAgB;IAChB,iBAAiB;GAClB;CpCuvJJ;;AoCnvJD;;EAGI,kB3BqRoC;C2BhRrC;;AAHC;EALJ;;IAMM,kBAAkB;GAErB;CpCuvJF;;AoC/uJD;;;;EAII,oB3BkQoC;E2BjQpC,mB3BiQoC;C2B3PrC;;AAJC;EAPJ;;;;IAQM,gBAAgB;IAChB,eAAgB;GAEnB;CpCsvJF;;AoC3uJD;EACE,c3BoJ6B;E2BnJ7B,sBAAsB;CAKvB;;AAHC;EAJF;IAKI,iBAAiB;GAEpB;CpC+uJA;;AoC5uJD;;EAEE,gBAAgB;EAChB,SAAS;EACT,QAAQ;EACR,c3B0I6B;C2BpI9B;;AAHC;EARF;;IASI,iBAAiB;GAEpB;CpCgvJA;;AoC/uJD;EACE,OAAO;EACP,sBAAsB;CACvB;;AACD;EACE,UAAU;EACV,iBAAiB;EACjB,sBAAsB;CACvB;;AAKD;EACE,YAAY;EACZ,mB3B2MsC;E2B1MtC,gB3BjH4B;E2BkH5B,kB3BrG6B;E2BsG7B,a3BqMqC;C2BpLtC;;AAtBD;EASI,sBAAsB;CACvB;;AAVH;EAaI,eAAe;CAChB;;AAED;EAhBF;;IAmBM,mB3B0LkC;G2BzLnC;CpCgvJJ;;AoCtuJD;EACE,mBAAmB;EACnB,aAAa;EACb,mB3B4KsC;E2B3KtC,kBAAkB;EC9LlB,gBAA4B;EAC5B,mBAA+B;ED+L/B,8BAA8B;EAC9B,uBAAuB;EACvB,8BAA8B;EAC9B,mB3B5F6B;C2BkH9B;;AA/BD;EAcI,WAAW;CACZ;;AAfH;EAmBI,eAAe;EACf,YAAY;EACZ,YAAY;EACZ,mBAAmB;CACpB;;AAvBH;EAyBI,gBAAgB;CACjB;;AAED;EA5BF;IA6BI,cAAc;GAEjB;CpCyuJA;;AoCjuJD;EACE,oB3BuIsC;C2B1FvC;;AA9CD;EAII,kBAAqB;EACrB,qBAAqB;EACrB,kB3B5K2B;C2B6K5B;;AAED;EATF;IAYM,iBAAiB;IACjB,YAAY;IACZ,YAAY;IACZ,cAAc;IACd,8BAA8B;IAC9B,UAAU;IACV,iBAAiB;GAYlB;EA9BL;;IAqBQ,2BAA2B;GAC5B;EAtBP;IAwBQ,kB3B9LuB;G2BmMxB;EA7BP;IA2BU,uBAAuB;GACxB;CpCquJR;;AoC/tJC;EAlCF;IAmCI,YAAY;IACZ,UAAU;GAUb;EA9CD;IAuCM,YAAY;GAKb;EA5CL;IAyCQ,kB3BgG2C;I2B/F3C,qB3B+F2C;G2B9F5C;CpCouJN;;AoCztJD;EACE,mB3BiFsC;E2BhFtC,oB3BgFsC;E2B/EtC,mB3B+EsC;E2B9EtC,kCAAkC;EAClC,qCAAqC;E5B7N7B,qF4B8NiD;EC7RzD,gBAA4B;EAC5B,mBAA+B;CDyThC;;Ab2JC;EAEE;IACE,sBAAsB;IACtB,iBAAiB;IACjB,uBAAuB;GACxB;EAGD;IACE,sBAAsB;IACtB,YAAY;IACZ,uBAAuB;GACxB;EAGD;IACE,sBAAsB;GACvB;EAED;IACE,sBAAsB;IACtB,uBAAuB;GAOxB;EALC;;;IAGE,YAAY;GACb;EAIY;IACb,YAAY;GACb;EAED;IACE,iBAAiB;IACjB,uBAAuB;GACxB;EAID;;IAEE,sBAAsB;IACtB,cAAc;IACd,iBAAiB;IACjB,uBAAuB;GAKxB;EAHC;;IACE,gBAAgB;GACjB;EAEsB;;IAEvB,mBAAmB;IACnB,eAAe;GAChB;EAGa;IACZ,OAAO;GACR;CvB2hJJ;;AoC3wJG;EAbJ;IAcM,mBAAmB;GAMtB;EApBH;IAiBQ,iBAAiB;GAClB;CpC+wJN;;AoCvwJC;EA1BF;IA2BI,YAAY;IACZ,UAAU;IACV,eAAe;IACf,gBAAgB;IAChB,eAAe;IACf,kBAAkB;I5BxPZ,iB4ByPkB;GAE3B;CpC2wJA;;AoCrwJD;EACE,cAAc;EHpUd,2BGqU4B;EHpU3B,0BGoU2B;CAC7B;;AAED;EACE,iBAAiB;EHzUjB,6BxB0G6B;EwBzG5B,4BxByG4B;EwBlG7B,8BGmU+B;EHlU9B,6BGkU8B;CAChC;;AAOD;EChVE,gBAA4B;EAC5B,mBAA+B;CDwVhC;;AATD;EChVE,iBAA4B;EAC5B,oBAA+B;CDoV9B;;AALH;EChVE,iBAA4B;EAC5B,oBAA+B;CDuV9B;;AAQH;EChWE,iBAA4B;EAC5B,oBAA+B;CDuWhC;;AALC;EAHF;IAII,YAAY;IACZ,kB3BIoC;I2BHpC,mB3BGoC;G2BDvC;CpCuwJA;;AoC5vJD;EACE;IACE,uBAAuB;GACxB;EACD;IACE,wBAAwB;IAC1B,oB3BhBsC;G2BqBrC;EAPD;IAKI,gBAAgB;GACjB;CpC+vJJ;;AoCtvJD;EACE,0B3BzBwC;E2B0BxC,sB3BzBuC;C2ByJxC;;AAlID;EAKI,Y3BzB2C;C2B+B5C;;AAXH;EAQM,e3BlB2C;E2BmB3C,8B3BlBgD;C2BmBjD;;AAVL;EAcI,Y3BvCmC;C2BwCpC;;AAfH;EAmBM,Y3BvCyC;C2B8C1C;;AA1BL;EAuBQ,Y3B1CuC;E2B2CvC,8B3B1C8C;C2B2C/C;;AAzBP;EA+BQ,Y3BhDuC;E2BiDvC,0B3BhDyC;C2BiD1C;;AAjCP;EAuCQ,Y3BtDuC;E2BuDvC,8B3BtD8C;C2BuD/C;;AAzCP;EA8CI,mB3BlD2C;C2B0D5C;;AAtDH;EAiDM,uB3BvDyC;C2BwD1C;;AAlDL;EAoDM,uB3BzDyC;C2B0D1C;;AArDL;;EA0DI,sB3BjFqC;C2BkFtC;;AA3DH;EAoEQ,0B3BpFyC;E2BqFzC,Y3BtFuC;C2BuFxC;;AAGH;EAzEJ;IA6EU,Y3BjGqC;G2BuGtC;EAnFT;IAgFY,Y3BnGmC;I2BoGnC,8B3BnG0C;G2BoG3C;EAlFX;IAwFY,Y3BzGmC;I2B0GnC,0B3BzGqC;G2B0GtC;EA1FX;IAgGY,Y3B/GmC;I2BgHnC,8B3B/G0C;G2BgH3C;CpCiuJV;;AoCn0JD;EA8GI,Y3BlI2C;C2BsI5C;;AAlHH;EAgHM,Y3BnIyC;C2BoI1C;;AAjHL;EAqHI,Y3BzI2C;C2BqJ5C;;AAjIH;EAwHM,Y3B3IyC;C2B4I1C;;AAzHL;;;EA8HQ,Y3B7IuC;C2B8IxC;;AAOP;EACE,uB3BrI8C;E2BsI9C,sB3BrIgD;C2BsQjD;;AAnID;EAKI,e3BrI+C;C2B2IhD;;AAXH;EAQM,Y3B9H0C;E2B+H1C,8B3B9HiD;C2B+HlD;;AAVL;EAcI,e3BnJ+C;C2BoJhD;;AAfH;EAmBM,e3BnJ6C;C2B0J9C;;AA1BL;EAuBQ,Y3BtJwC;E2BuJxC,8B3BtJ+C;C2BuJhD;;AAzBP;EA+BQ,Y3B9JwC;E2B+JxC,0B3B5J0C;C2B6J3C;;AAjCP;EAuCQ,Y3BlKwC;E2BmKxC,8B3BlK+C;C2BmKhD;;AAzCP;EA+CI,mB3B/J4C;C2BuK7C;;AAvDH;EAkDM,uB3BpK0C;C2BqK3C;;AAnDL;EAqDM,uB3BtK0C;C2BuK3C;;AAtDL;;EA2DI,sBAAoB;CACrB;;AA5DH;EAoEQ,0B3BhM0C;E2BiM1C,Y3BpMwC;C2BqMzC;;AAGH;EAzEJ;IA6EU,sB3BhNwC;G2BiNzC;EA9ET;IAgFU,0B3BnNwC;G2BoNzC;EAjFT;IAmFU,e3BnNyC;G2ByN1C;EAzFT;IAsFY,Y3BrNoC;I2BsNpC,8B3BrN2C;G2BsN5C;EAxFX;IA8FY,Y3B7NoC;I2B8NpC,0B3B3NsC;G2B4NvC;EAhGX;IAsGY,Y3BjOoC;I2BkOpC,8B3BjO2C;G2BkO5C;CpC8rJV;;AoCtyJD;EA+GI,e3B/O+C;C2BmPhD;;AAnHH;EAiHM,Y3BhP0C;C2BiP3C;;AAlHL;EAsHI,e3BtP+C;C2BkQhD;;AAlIH;EAyHM,Y3BxP0C;C2ByP3C;;AA1HL;;;EA+HQ,Y3B1PwC;C2B2PzC;;AE7oBP;EACE,kB7BqxBkC;E6BpxBlC,oB7B0D6B;E6BzD7B,iBAAiB;EACjB,0B7BoxBqC;E6BnxBrC,mB7BmG6B;C6BlF9B;;AAtBD;EAQI,sBAAsB;CASvB;;AAjBH;EAaM,cAA2C;EAC3C,eAAe;EACf,Y7B2wB8B;C6B1wB/B;;AAhBL;EAoBI,e7BX4B;C6BY7B;;ACvBH;EACE,sBAAsB;EACtB,gBAAgB;EAChB,eAA+B;EAC/B,mB9BsG6B;C8BlC9B;;AAxED;EAOI,gBAAgB;CA0BjB;;AAjCH;;EAUM,mBAAmB;EACnB,YAAY;EACZ,kB9BgF0B;E8B/E1B,yB9B+C+B;E8B9C/B,sBAAsB;EACtB,e9BDwB;E8BExB,uB9BobqC;E8BnbrC,uB9BobqC;E8BnbrC,kBAAkB;CACnB;;AAnBL;;EAuBQ,eAAe;ENXrB,+BxB8F6B;EwB7F1B,4BxB6F0B;C8BjFxB;;AAzBP;;ENIE,gCxBsG6B;EwBrG1B,6BxBqG0B;C8B3ExB;;AA/BP;;;EAuCM,WAAW;EACX,e9BPwB;E8BQxB,0B9B7B0B;E8B8B1B,mB9B+ZqC;C8B9ZtC;;AA3CL;;;;EAmDM,WAAW;EACX,Y9BuZqC;E8BtZrC,0B9BvCwB;E8BwCxB,sB9BxCwB;E8ByCxB,gBAAgB;CACjB;;AAxDL;;;;;;EAkEM,e9BvD0B;E8BwD1B,uB9B6YqC;E8B5YrC,mB9B6YqC;E8B5YrC,oB9B+JsC;C8B9JvC;;ACrEC;;EAEA,mB/B4F0B;E+B3F1B,gB/B6CwB;E+B5CxB,uB/BkG+B;C+BjGhC;;AAEG;;EPIN,+BxB+F6B;EwB9F1B,4BxB8F0B;C+BhGxB;;AAGC;;EPVN,gCxBuG6B;EwBtG1B,6BxBsG0B;C+B1FxB;;AAhBD;;EAEA,kB/B+F0B;E+B9F1B,gB/B8CwB;E+B7CxB,iB/BmGyB;C+BlG1B;;AAEG;;EPIN,+BxBgG6B;EwB/F1B,4BxB+F0B;C+BjGxB;;AAGC;;EPVN,gCxBwG6B;EwBvG1B,6BxBuG0B;C+B3FxB;;ACfP;EACE,gBAAgB;EAChB,eAA+B;EAC/B,iBAAiB;EACjB,mBAAmB;CA4CpB;;AAhDD;E1BUI,aAAa;EACb,eAAe;CAChB;;A0BZH;E1BcI,YAAY;CACb;;A0BfH;EAOI,gBAAgB;CAejB;;AAtBH;;EAUM,sBAAsB;EACtB,kBAAkB;EAClB,uBhCsbqC;EgCrbrC,uBhCsbqC;EgCrbrC,oBhC0cqC;CgCzctC;;AAfL;;EAmBM,sBAAsB;EACtB,0BhCV0B;CgCW3B;;AArBL;;EA2BM,aAAa;CACd;;AA5BL;;EAkCM,YAAY;CACb;;AAnCL;;;;EA2CM,ehClC0B;EgCmC1B,uBhCsZqC;EgCrZrC,oBhCqLsC;CgCpLvC;;AC/CL;EACE,gBAAgB;EAChB,wBAAwB;EACxB,eAAe;EACf,kBAAkB;EAClB,eAAe;EACf,YjC+jBgC;EiC9jBhC,mBAAmB;EACnB,oBAAoB;EACpB,yBAAyB;EACzB,qBAAqB;CActB;;AAxBD;EAgBI,cAAc;CACf;;AAjBH;EAqBI,mBAAmB;EACnB,UAAU;CACX;;AAIH;EAGI,YjCyiB8B;EiCxiB9B,sBAAsB;EACtB,gBAAgB;CACjB;;AAMH;ECxCE,0BlCW8B;CiC+B/B;;AAFD;ECnCM,0BAAwB;CACzB;;ADsCL;EC5CE,0BlCc4B;CiCgC7B;;AAFD;ECvCM,0BAAwB;CACzB;;AD0CL;EChDE,0BlCe6B;CiCmC9B;;AAFD;EC3CM,0BAAwB;CACzB;;AD8CL;ECpDE,0BlCgB6B;CiCsC9B;;AAFD;EC/CM,0BAAwB;CACzB;;ADkDL;ECxDE,0BlCiB6B;CiCyC9B;;AAFD;ECnDM,0BAAwB;CACzB;;ADsDL;EC5DE,0BlCkB6B;CiC4C9B;;AAFD;ECvDM,0BAAwB;CACzB;;ACHL;EACE,sBAAsB;EACtB,gBAAgB;EAChB,iBAAiB;EACjB,gBnC2C4B;EmC1C5B,kBnCswBgC;EmCrwBhC,YnC2vBgC;EmC1vBhC,enCqwB6B;EmCpwB7B,uBAAuB;EACvB,oBAAoB;EACpB,mBAAmB;EACnB,0BnCH8B;EmCI9B,oBnCiwBgC;CmC1tBjC;;AAnDD;EAgBI,cAAc;CACf;;AAjBH;EAqBI,mBAAmB;EACnB,UAAU;CACX;;AAvBH;;EA2BI,OAAO;EACP,iBAAiB;CAClB;;AA7BH;;EAoCI,enCzB0B;EmC0B1B,uBnCouB8B;CmCnuB/B;;AAtCH;EAyCI,aAAa;CACd;;AA1CH;EA6CI,kBAAkB;CACnB;;AA9CH;EAiDI,iBAAiB;CAClB;;AAIH;EAGI,YnC0sB8B;EmCzsB9B,sBAAsB;EACtB,gBAAgB;CACjB;;AC7DH;EACE,kBpCqemC;EoCpenC,qBpCoemC;EoCnenC,oBpCmemC;EoClenC,epCmesC;EoCletC,0BpCK8B;CoCsC/B;;AAhDD;;EASI,epCgeoC;CoC/drC;;AAVH;EAaI,oBAAkC;EAClC,gBpC4diC;EoC3djC,iBAAiB;CAClB;;AAhBH;EAmBI,0BAAwB;CACzB;;AApBH;;EAwBI,mBpCiF2B;EoChF3B,mBAAkC;EAClC,oBAAkC;CACnC;;AA3BH;EA8BI,gBAAgB;CACjB;;AAED;EAjCF;IAkCI,kBAAmC;IACnC,qBAAmC;GAatC;EAhDD;;IAuCM,mBAAkC;IAClC,oBAAkC;GACnC;EAzCL;;IA6CM,gBpC8b+B;GoC7bhC;C7CsoLJ;;A8CnrLD;EACE,eAAe;EACf,arCquB+B;EqCpuB/B,oBrCwD6B;EqCvD7B,yBrCqDmC;EqCpDnC,uBrCkB0B;EqCjB1B,uBrCquBgC;EqCpuBhC,mBrCgG6B;ED8ErB,oCsC7KkC;CAgB3C;;AAxBD;;EnCGE,eADmC;EAEnC,gBAAgB;EAChB,aAAa;EmCQX,kBAAkB;EAClB,mBAAmB;CACpB;;AAfH;EAqBI,arC6tB6B;EqC5tB7B,erChB4B;CqCiB7B;;AAIH;;;EAGE,sBrCnB4B;CqCoB7B;;AC7BD;EACE,ctC0mBgC;EsCzmBhC,oBtCuD6B;EsCtD7B,8BAA8B;EAC9B,mBtCiG6B;CsC1E9B;;AA3BD;EAQI,cAAc;EAEd,eAAe;CAChB;;AAXH;EAeI,kBtC8lB8B;CsC7lB/B;;AAhBH;;EAqBI,iBAAiB;CAClB;;AAtBH;EAyBI,gBAAgB;CACjB;;AAOH;;EAEE,oBAA8B;CAS/B;;AAXD;;EAMI,mBAAmB;EACnB,UAAU;EACV,aAAa;EACb,eAAe;CAChB;;AAOH;ECvDE,0BvCqfsC;EuCpftC,sBvCqfqC;EuCpfrC,evCkfsC;CsC3bvC;;ACrDC;EACE,0BAAwB;CACzB;;AACD;EACE,eAAa;CACd;;ADkDH;EC3DE,0BvCyfsC;EuCxftC,sBvCyfqC;EuCxfrC,evCsfsC;CsC3bvC;;ACzDC;EACE,0BAAwB;CACzB;;AACD;EACE,eAAa;CACd;;ADsDH;EC/DE,0BvC6fsC;EuC5ftC,sBvC6fqC;EuC5frC,evC0fsC;CsC3bvC;;AC7DC;EACE,0BAAwB;CACzB;;AACD;EACE,eAAa;CACd;;AD0DH;ECnEE,0BvCigBsC;EuChgBtC,sBvCigBqC;EuChgBrC,evC8fsC;CsC3bvC;;ACjEC;EACE,0BAAwB;CACzB;;AACD;EACE,eAAa;CACd;;ACHH;EACE;IAAQ,4BAA4B;GjDmzLnC;EiDlzLD;IAAQ,yBAAyB;GjDqzLhC;CACF;;AiDlzLD;EACE;IAAQ,4BAA4B;GjDszLnC;EiDrzLD;IAAQ,yBAAyB;GjDwzLhC;CACF;;AiDjzLD;EACE,iBAAiB;EACjB,axCsC6B;EwCrC7B,oBxCqC6B;EwCpC7B,0BxCgnBmC;EwC/mBnC,mBxC+E6B;EDxCrB,+CyCtCgC;CACzC;;AAGD;EACE,YAAY;EACZ,UAAU;EACV,aAAa;EACb,gBxCc4B;EwCb5B,kBxCyB6B;EwCxB7B,YxCsmBgC;EwCrmBhC,mBAAmB;EACnB,0BxC1B4B;EDoDpB,+CyCzB+B;EzC+I/B,4ByC9I0B;CACnC;;AAOD;;ECGE,sMAAiC;EDAjC,2BAA2B;CAC5B;;AAMD;;EzC5CE,2DyC8C0D;EzC5ClD,mDyC4CkD;CAC3D;;AAMD;EErEE,0B1Ce6B;CwCwD9B;;AEpEC;EDkDA,sMAAiC;CChDhC;;AFoEH;EEzEE,0B1CgB6B;CwC2D9B;;AExEC;EDkDA,sMAAiC;CChDhC;;AFwEH;EE7EE,0B1CiB6B;CwC8D9B;;AE5EC;EDkDA,sMAAiC;CChDhC;;AF4EH;EEjFE,0B1CkB6B;CwCiE9B;;AEhFC;EDkDA,sMAAiC;CChDhC;;ACRH;EAEE,iBAAiB;CAKlB;;AAPD;EAKI,cAAc;CACf;;AAGH;;EAEE,QAAQ;EACR,iBAAiB;CAClB;;AAED;EACE,eAAe;CAChB;;AAED;EACE,eAAe;CAMhB;;AAPD;EAKI,gBAAgB;CACjB;;AAGH;;EAEE,mBAAmB;CACpB;;AAED;;EAEE,oBAAoB;CACrB;;AAED;;;EAGE,oBAAoB;EACpB,oBAAoB;CACrB;;AAED;EACE,uBAAuB;CACxB;;AAED;EACE,uBAAuB;CACxB;;AAGD;EACE,cAAc;EACd,mBAAmB;CACpB;;AAKD;EACE,gBAAgB;EAChB,iBAAiB;CAClB;;ACxDD;EAEE,oBAAoB;EACpB,gBAAgB;CACjB;;AAOD;EACE,mBAAmB;EACnB,eAAe;EACf,mBAAmB;EAEnB,oBAAoB;EACpB,uB5C0oBkC;E4CzoBlC,uB5C2oBkC;C4CjoBnC;;AAjBD;EpBjBE,6BxB0G6B;EwBzG5B,4BxByG4B;C4C7E5B;;AAZH;EAcI,iBAAiB;EpBvBnB,gCxBkG6B;EwBjG5B,+BxBiG4B;C4CzE5B;;AASH;;EAEE,Y5C6oBkC;C4ChoBnC;;AAfD;;EAKI,Y5C4oBgC;C4C3oBjC;;AANH;;;EAWI,sBAAsB;EACtB,Y5CmoBgC;E4CloBhC,0B5CinBmC;C4ChnBpC;;AAGH;EACE,YAAY;EACZ,iBAAiB;CAClB;;AAED;EAKI,0B5CzD4B;E4C0D5B,e5C3D4B;E4C4D5B,oB5C6JwC;C4CpJzC;;AAhBH;EAWM,eAAe;CAChB;;AAZL;EAcM,e5CnE0B;C4CoE3B;;AAfL;EAsBI,WAAW;EACX,Y5CwB4B;E4CvB5B,0B5C1E0B;E4C2E1B,sB5C3E0B;C4CsF3B;;AApCH;;;;;;;EA+BM,eAAe;CAChB;;AAhCL;EAkCM,e5C8kBiC;C4C7kBlC;;ACnGH;EACE,e7CmfoC;E6ClfpC,0B7CmfoC;C6ChfrC;;AAED;;EACE,e7C4eoC;C6C1drC;;AAnBD;;EAII,eAAe;CAChB;;AALH;;;EASI,e7CoekC;E6CnelC,0BAAwB;CACzB;;AAXH;;;;EAeI,YAAY;EACZ,0B7C6dkC;E6C5dlC,sB7C4dkC;C6C3dnC;;AAzBH;EACE,e7CufoC;E6CtfpC,0B7CufoC;C6CpfrC;;AAED;;EACE,e7CgfoC;C6C9drC;;AAnBD;;EAII,eAAe;CAChB;;AALH;;;EASI,e7CwekC;E6CvelC,0BAAwB;CACzB;;AAXH;;;;EAeI,YAAY;EACZ,0B7CiekC;E6ChelC,sB7CgekC;C6C/dnC;;AAzBH;EACE,e7C2foC;E6C1fpC,0B7C2foC;C6CxfrC;;AAED;;EACE,e7CofoC;C6ClerC;;AAnBD;;EAII,eAAe;CAChB;;AALH;;;EASI,e7C4ekC;E6C3elC,0BAAwB;CACzB;;AAXH;;;;EAeI,YAAY;EACZ,0B7CqekC;E6CpelC,sB7CoekC;C6CnenC;;AAzBH;EACE,e7C+foC;E6C9fpC,0B7C+foC;C6C5frC;;AAED;;EACE,e7CwfoC;C6CterC;;AAnBD;;EAII,eAAe;CAChB;;AALH;;;EASI,e7CgfkC;E6C/elC,0BAAwB;CACzB;;AAXH;;;;EAeI,YAAY;EACZ,0B7CyekC;E6CxelC,sB7CwekC;C6CvenC;;AD8FL;EACE,cAAc;EACd,mBAAmB;CACpB;;AACD;EACE,iBAAiB;EACjB,iBAAiB;CAClB;;AE3HD;EACE,oB9C0D6B;E8CzD7B,uB9C6rBgC;E8C5rBhC,8BAA8B;EAC9B,mB9CmG6B;EDxCrB,0C+C1D0B;CACnC;;AAGD;EACE,c9CsrBgC;C8CprBjC;;AAHD;ExCAI,aAAa;EACb,eAAe;CAChB;;AwCFH;ExCII,YAAY;CACb;;AwCCH;EACE,mB9CirBqC;E8ChrBrC,qCAAqC;EtBpBrC,6BsBqBgD;EtBpB/C,4BsBoB+C;CAKjD;;AARD;EAMI,eAAe;CAChB;;AAIH;EACE,cAAc;EACd,iBAAiB;EACjB,gBAAe;EACf,eAAe;CAShB;;AAbD;;;;;EAWI,eAAe;CAChB;;AAIH;EACE,mB9CspBqC;E8CrpBrC,0B9C2pBmC;E8C1pBnC,2B9CypBgC;EwBjsBhC,gCsByCmD;EtBxClD,+BsBwCkD;CACpD;;AAQD;;EAGI,iBAAiB;CAsBlB;;AAzBH;;EAMM,oBAAoB;EACpB,iBAAiB;CAClB;;AARL;;EAaQ,cAAc;EtBvEpB,6BsBwEsD;EtBvErD,4BsBuEqD;CACjD;;AAfP;;EAqBQ,iBAAiB;EtBvEvB,gCsBwEyD;EtBvExD,+BsBuEwD;CACpD;;AAvBP;EtB1DE,2BsBsFgC;EtBrF/B,0BsBqF+B;CAC7B;;AAIL;EAEI,oBAAoB;CACrB;;AAEH;EACE,oBAAoB;CACrB;;AAOD;;;EAII,iBAAiB;CAMlB;;AAVH;;;EAOM,mB9CmlB4B;E8CllB5B,oB9CklB4B;C8CjlB7B;;AATL;;EtBzGE,6BsBuHkD;EtBtHjD,4BsBsHiD;CAkBjD;;AAhCH;;;;EAmBQ,4BAA6C;EAC7C,6BAA8C;CAU/C;;AA9BP;;;;;;;;EAwBU,4BAA6C;CAC9C;;AAzBT;;;;;;;;EA4BU,6BAA8C;CAC/C;;AA7BT;;EtBjGE,gCsBqIqD;EtBpIpD,+BsBoIoD;CAkBpD;;AAtDH;;;;EAyCQ,+BAAgD;EAChD,gCAAiD;CAUlD;;AApDP;;;;;;;;EA8CU,+BAAgD;CACjD;;AA/CT;;;;;;;;EAkDU,gCAAiD;CAClD;;AAnDT;;;;EA2DI,2B9CzBgC;C8C0BjC;;AA5DH;;EA+DI,cAAc;CACf;;AAhEH;;EAmEI,UAAU;CAiCX;;AApGH;;;;;;;;;;;;EA0EU,eAAe;CAChB;;AA3ET;;;;;;;;;;;;EA8EU,gBAAgB;CACjB;;AA/ET;;;;;;;;EAuFU,iBAAiB;CAClB;;AAxFT;;;;;;;;EAgGU,iBAAiB;CAClB;;AAjGT;EAsGI,UAAU;EACV,iBAAiB;CAClB;;AASH;EACE,oB9C7J6B;C8CwL9B;;AA5BD;EAKI,iBAAiB;EACjB,mB9CtH2B;C8C2H5B;;AAXH;EASM,gBAAgB;CACjB;;AAVL;EAcI,iBAAiB;CAMlB;;AApBH;;EAkBM,2B9C6d4B;C8C5d7B;;AAnBL;EAuBI,cAAc;CAIf;;AA3BH;EAyBM,8B9Csd4B;C8Crd7B;;AAML;EC1PE,mB/C6sBgC;C8CjdjC;;AC1PK;EACF,e/CM4B;E+CL5B,0B/C0sBiC;E+CzsBjC,mB/CwsB8B;C+C/rB/B;;AAPqB;EAClB,uB/CqsB4B;C+CpsB7B;;AACD;EACE,e/CmsB+B;E+ClsB/B,0B/CH0B;C+CI3B;;AAGmB;EAClB,0B/C4rB4B;C+C3rB7B;;AD2OL;EC7PE,sB/Cc4B;C8CiP7B;;AC7PK;EACF,Y/C6sB8B;E+C5sB9B,0B/CU0B;E+CT1B,sB/CS0B;C+CA3B;;AAPqB;EAClB,0B/CMwB;C+CLzB;;AACD;EACE,e/CGwB;E+CFxB,uB/CosB4B;C+CnsB7B;;AAGmB;EAClB,6B/CHwB;C+CIzB;;AD8OL;EChQE,sB/CsfqC;C8CpPtC;;AChQK;EACF,e/CifoC;E+ChfpC,0B/CifoC;E+ChfpC,sB/CifmC;C+CxepC;;AAPqB;EAClB,0B/C8eiC;C+C7elC;;AACD;EACE,e/C0ekC;E+CzelC,0B/CwekC;C+CvenC;;AAGmB;EAClB,6B/CqeiC;C+CpelC;;ADiPL;ECnQE,sB/C0fqC;C8CrPtC;;ACnQK;EACF,e/CqfoC;E+CpfpC,0B/CqfoC;E+CpfpC,sB/CqfmC;C+C5epC;;AAPqB;EAClB,0B/CkfiC;C+CjflC;;AACD;EACE,e/C8ekC;E+C7elC,0B/C4ekC;C+C3enC;;AAGmB;EAClB,6B/CyeiC;C+CxelC;;ADoPL;ECtQE,sB/C8fqC;C8CtPtC;;ACtQK;EACF,e/CyfoC;E+CxfpC,0B/CyfoC;E+CxfpC,sB/CyfmC;C+ChfpC;;AAPqB;EAClB,0B/CsfiC;C+CrflC;;AACD;EACE,e/CkfkC;E+CjflC,0B/CgfkC;C+C/enC;;AAGmB;EAClB,6B/C6eiC;C+C5elC;;ADuPL;ECzQE,sB/CkgBqC;C8CvPtC;;ACzQK;EACF,e/C6foC;E+C5fpC,0B/C6foC;E+C5fpC,sB/C6fmC;C+CpfpC;;AAPqB;EAClB,0B/C0fiC;C+CzflC;;AACD;EACE,e/CsfkC;E+CrflC,0B/CofkC;C+CnfnC;;AAGmB;EAClB,6B/CifiC;C+ChflC;;ACjBL;EACE,mBAAmB;EACnB,eAAe;EACf,UAAU;EACV,WAAW;EACX,iBAAiB;CAelB;;AApBD;;;;;EAYI,mBAAmB;EACnB,OAAO;EACP,QAAQ;EACR,UAAU;EACV,aAAa;EACb,YAAY;EACZ,UAAU;CACX;;AAIH;EACE,uBAAuB;CACxB;;AAGD;EACE,oBAAoB;CACrB;;AC5BD;EACE,iBAAiB;EACjB,cAAc;EACd,oBAAoB;EACpB,0BjDqvBmC;EiDpvBnC,0BjDqvBkC;EiDpvBlC,mBjDiG6B;EDxCrB,gDkDxDgC;CAKzC;;AAZD;EASI,mBAAmB;EACnB,kCAAkB;CACnB;;AAIH;EACE,cAAc;EACd,mBjDuF6B;CiDtF9B;;AACD;EACE,aAAa;EACb,mBjDoF6B;CiDnF9B;;ACvBD;EACE,aAAa;EACb,gBAA2B;EAC3B,kBlDmzBgC;EkDlzBhC,eAAe;EACf,YlDkzBgC;EkDjzBhC,0BlDkzBwC;EkB1zBxC,agCSmB;EhCNnB,0BAAa;CgCiBd;;AAlBD;EAWI,YlD4yB8B;EkD3yB9B,sBAAsB;EACtB,gBAAgB;EhCflB,agCgBqB;EhCbrB,0BAAa;CgCcZ;;AASH;EACE,WAAW;EACX,gBAAgB;EAChB,wBAAwB;EACxB,UAAU;EACV,yBAAyB;CAC1B;;ACzBD;EACE,iBAAiB;CAClB;;AAGD;EACE,cAAc;EACd,iBAAiB;EACjB,gBAAgB;EAChB,OAAO;EACP,SAAS;EACT,UAAU;EACV,QAAQ;EACR,cnDmQ6B;EmDlQ7B,kCAAkC;EAIlC,WAAW;CAQZ;;AArBD;EpD0HE,sCAA4B;EAGpB,8BAAoB;EAqEpB,4CoDhLqC;EpDgLrC,oCoDhLqC;EpDgLrC,qEoDhLqC;CAC5C;;AAnBH;EpD0HE,mCAA4B;EAGpB,2BAAoB;CoDzGoB;;AAElD;EACE,mBAAmB;EACnB,iBAAiB;CAClB;;AAGD;EACE,mBAAmB;EACnB,YAAY;EACZ,aAAa;CACd;;AAGD;EACE,mBAAmB;EACnB,uBnDuiBiD;EmDtiBjD,uBnD0iBiD;EmDziBjD,qCnDuiBiD;EmDtiBjD,mBnDuD6B;EDzCrB,yCoDb0B;EAClC,6BAA6B;EAE7B,WAAW;CACZ;;AAGD;EACE,gBAAgB;EAChB,OAAO;EACP,SAAS;EACT,UAAU;EACV,QAAQ;EACR,cnDoN6B;EmDnN7B,uBnD4hBgC;CmDxhBjC;;AAXD;EjC5DE,WiCqE2B;EjClE3B,yBAAa;CiCkEmB;;AATlC;EjC5DE,alBimB8B;EkB9lB9B,0BAAa;CiCmEuC;;AAKtD;EACE,cnDugBgC;EmDtgBhC,iCnDshBmC;CmDphBpC;;AAJD;E7C/DI,aAAa;EACb,eAAe;CAChB;;A6C6DH;E7C3DI,YAAY;CACb;;A6CgEH;EACE,iBAAiB;CAClB;;AAGD;EACE,UAAU;EACV,yBnD5BmC;CmD6BpC;;AAID;EACE,mBAAmB;EACnB,cnDifgC;CmDhfjC;;AAGD;EACE,cnD4egC;EmD3ehC,kBAAkB;EAClB,8BnD6fmC;CmD7epC;;AAnBD;E7CvFI,aAAa;EACb,eAAe;CAChB;;A6CqFH;E7CnFI,YAAY;CACb;;A6CkFH;EAQI,iBAAiB;EACjB,iBAAiB;CAClB;;AAVH;EAaI,kBAAkB;CACnB;;AAdH;EAiBI,eAAe;CAChB;;AAIH;EACE,mBAAmB;EACnB,aAAa;EACb,YAAY;EACZ,aAAa;EACb,iBAAiB;CAClB;;AAGD;EAEE;IACE,anDme+B;ImDle/B,kBAAkB;GACnB;EACD;IpDtEQ,0CoDuE6B;GACpC;EAGD;IAAY,anD4dqB;GmD5dD;C5DqrNjC;;A4DlrND;EACE;IAAY,anDsdqB;GmDtdD;C5DurNjC;;A6Dr0ND;EACE,mBAAmB;EACnB,cpD+Q6B;EoD9Q7B,eAAe;ECRf,4DrD4CsE;EqD1CtE,mBAAmB;EACnB,oBAAoB;EACpB,uBAAuB;EACvB,iBAAiB;EACjB,yBrDwDmC;EqDvDnC,iBAAiB;EACjB,kBAAkB;EAClB,sBAAsB;EACtB,kBAAkB;EAClB,qBAAqB;EACrB,oBAAoB;EACpB,mBAAmB;EACnB,qBAAqB;EACrB,kBAAkB;EDHlB,gBpDwC4B;EkBlD5B,WkCYkB;ElCTlB,yBAAa;CkCgBd;;AAhBD;ElCHE,alB+gB8B;EkB5gB9B,0BAAa;CkCWoC;;AAXnD;EAYa,iBAAkB;EAAE,eAA+B;CAAI;;AAZpE;EAaa,iBAAkB;EAAE,epDkgBA;CoDlgBmC;;AAbpE;EAca,gBAAkB;EAAE,eAA+B;CAAI;;AAdpE;EAea,kBAAkB;EAAE,epDggBA;CoDhgBmC;;AAIpE;EACE,iBpDmfiC;EoDlfjC,iBAAiB;EACjB,YpDmfgC;EoDlfhC,mBAAmB;EACnB,uBpDmfgC;EoDlfhC,mBpD8E6B;CoD7E9B;;AAGD;EACE,mBAAmB;EACnB,SAAS;EACT,UAAU;EACV,0BAA0B;EAC1B,oBAAoB;CACrB;;AAED;EAEI,UAAU;EACV,UAAU;EACV,kBpDse6B;EoDre7B,wBAAyD;EACzD,uBpDge8B;CoD/d/B;;AAPH;EASI,UAAU;EACV,WpDge6B;EoD/d7B,oBpD+d6B;EoD9d7B,wBAAyD;EACzD,uBpDyd8B;CoDxd/B;;AAdH;EAgBI,UAAU;EACV,UpDyd6B;EoDxd7B,oBpDwd6B;EoDvd7B,wBAAyD;EACzD,uBpDkd8B;CoDjd/B;;AArBH;EAuBI,SAAS;EACT,QAAQ;EACR,iBpDid6B;EoDhd7B,4BAA8E;EAC9E,yBpD2c8B;CoD1c/B;;AA5BH;EA8BI,SAAS;EACT,SAAS;EACT,iBpD0c6B;EoDzc7B,4BpDyc6B;EoDxc7B,wBpDoc8B;CoDnc/B;;AAnCH;EAqCI,OAAO;EACP,UAAU;EACV,kBpDmc6B;EoDlc7B,wBpDkc6B;EoDjc7B,0BpD6b8B;CoD5b/B;;AA1CH;EA4CI,OAAO;EACP,WpD6b6B;EoD5b7B,iBpD4b6B;EoD3b7B,wBpD2b6B;EoD1b7B,0BpDsb8B;CoDrb/B;;AAjDH;EAmDI,OAAO;EACP,UpDsb6B;EoDrb7B,iBpDqb6B;EoDpb7B,wBpDob6B;EoDnb7B,0BpD+a8B;CoD9a/B;;AE9FH;EACE,mBAAmB;EACnB,OAAO;EACP,QAAQ;EACR,ctD6Q6B;EsD5Q7B,cAAc;EACd,iBtDshByC;EsDrhBzC,aAAa;EDXb,4DrD4CsE;EqD1CtE,mBAAmB;EACnB,oBAAoB;EACpB,uBAAuB;EACvB,iBAAiB;EACjB,yBrDwDmC;EqDvDnC,iBAAiB;EACjB,kBAAkB;EAClB,sBAAsB;EACtB,kBAAkB;EAClB,qBAAqB;EACrB,oBAAoB;EACpB,mBAAmB;EACnB,qBAAqB;EACrB,kBAAkB;ECAlB,gBtDmC4B;EsDjC5B,uBtD6gBwC;EsD5gBxC,6BAA6B;EAC7B,uBtDihBwC;EsDhhBxC,qCtD8gBwC;EsD7gBxC,mBtDwF6B;EDzCrB,0CuD9C2B;CAOpC;;AAzBD;EAqBc,kBtDihB4B;CsDjhBS;;AArBnD;EAsBc,kBtDghB4B;CsDhhBS;;AAtBnD;EAuBc,iBtD+gB4B;CsD/gBQ;;AAvBlD;EAwBc,mBtD8gB4B;CsD9gBU;;AAGpD;EACE,UAAU;EACV,kBAAkB;EAClB,gBtDgB4B;EsDf5B,0BtDogB0C;EsDngB1C,iCAA+B;EAC/B,2BAAwE;CACzE;;AAED;EACE,kBAAkB;CACnB;;AAMD;EAGI,mBAAmB;EACnB,eAAe;EACf,SAAS;EACT,UAAU;EACV,0BAA0B;EAC1B,oBAAoB;CACrB;;AAEH;EACE,mBtDmfyD;CsDlf1D;;AACD;EACE,mBtD2ewC;EsD1exC,YAAY;CACb;;AAED;EAEI,UAAU;EACV,mBtDyeuD;EsDxevD,uBAAuB;EACvB,0BtD2ewC;EsD1exC,sCtDweyC;EsDvezC,ctDqeuD;CsD7dxD;;AAfH;EASM,aAAa;EACb,YAAY;EACZ,mBtD4doC;EsD3dpC,uBAAuB;EACvB,uBtD8coC;CsD7crC;;AAdL;EAiBI,SAAS;EACT,YtD0duD;EsDzdvD,kBtDyduD;EsDxdvD,qBAAqB;EACrB,4BtD2dwC;EsD1dxC,wCtDwdyC;CsDhd1C;;AA9BH;EAwBM,aAAa;EACb,UAAU;EACV,ctD6coC;EsD5cpC,qBAAqB;EACrB,yBtD+boC;CsD9brC;;AA7BL;EAgCI,UAAU;EACV,mBtD2cuD;EsD1cvD,oBAAoB;EACpB,6BtD6cwC;EsD5cxC,yCtD0cyC;EsDzczC,WtDucuD;CsD/bxD;;AA7CH;EAuCM,aAAa;EACb,SAAS;EACT,mBtD8boC;EsD7bpC,oBAAoB;EACpB,0BtDgboC;CsD/arC;;AA5CL;EAgDI,SAAS;EACT,atD2buD;EsD1bvD,kBtD0buD;EsDzbvD,sBAAsB;EACtB,2BtD4bwC;EsD3bxC,uCtDybyC;CsDjb1C;;AA7DH;EAuDM,aAAa;EACb,WAAW;EACX,sBAAsB;EACtB,wBtDiaoC;EsDhapC,ctD4aoC;CsD3arC;;AC1HL;EACE,mBAAmB;CACpB;;AAED;EACE,mBAAmB;EACnB,iBAAiB;EACjB,YAAY;CA0Eb;;AA7ED;EAMI,cAAc;EACd,mBAAmB;ExD0Kb,kCwDzKkC;CAgCzC;;AAxCH;;ErDDE,eADmC;EAEnC,gBAAgB;EAChB,aAAa;EqDaT,eAAe;CAChB;;AAGD;EAlBJ;IxDuMU,+CwDpL0C;IxDoL1C,uCwDpL0C;IxDoL1C,2EwDpL0C;IxD4BlD,oCwD3BuC;IxD6B/B,4BwD7B+B;IxDuIvC,4BwDtI+B;IxDwIvB,oBwDxIuB;GAmB9B;EAxCH;IxDqIE,2CAA8B;IACtB,mCAAsB;IwD5GxB,QAAQ;GACT;EA3BP;IxDqIE,4CAA8B;IACtB,oCAAsB;IwDvGxB,QAAQ;GACT;EAhCP;IxDqIE,wCAA8B;IACtB,gCAAsB;IwDjGxB,QAAQ;GACT;ChEymON;;AgE/oOD;;;EA6CI,eAAe;CAChB;;AA9CH;EAiDI,QAAQ;CACT;;AAlDH;;EAsDI,mBAAmB;EACnB,OAAO;EACP,YAAY;CACb;;AAzDH;EA4DI,WAAW;CACZ;;AA7DH;EA+DI,YAAY;CACb;;AAhEH;;EAmEI,QAAQ;CACT;;AApEH;EAuEI,YAAY;CACb;;AAxEH;EA0EI,WAAW;CACZ;;AAOH;EACE,mBAAmB;EACnB,OAAO;EACP,QAAQ;EACR,UAAU;EACV,WvD4sB+C;EkB1yB/C,alB2yB8C;EkBxyB9C,0BAAa;EqC6Fb,gBvD4sBgD;EuD3sBhD,YvDwsBgD;EuDvsBhD,mBAAmB;EACnB,0CvDosB0D;EuDnsB1D,8BAAsB;CA+DvB;;AA1ED;EdjFE,+FAAiC;EACjC,4BAA4B;EAC5B,uHAAwJ;CciGvJ;;AAlBH;EAoBI,WAAW;EACX,SAAS;EdtGX,+FAAiC;EACjC,4BAA4B;EAC5B,uHAAwJ;CcsGvJ;;AAvBH;EA4BI,WAAW;EACX,YvDmrB8C;EuDlrB9C,sBAAsB;ErCvHxB,aqCwHqB;ErCrHrB,0BAAa;CqCsHZ;;AAhCH;;;;EAuCI,mBAAmB;EACnB,SAAS;EACT,kBAAkB;EAClB,WAAW;EACX,sBAAsB;CACvB;;AA5CH;;EA+CI,UAAU;EACV,mBAAmB;CACpB;;AAjDH;;EAoDI,WAAW;EACX,oBAAoB;CACrB;;AAtDH;;EAyDI,YAAa;EACb,aAAa;EACb,eAAe;EACf,mBAAmB;CACpB;;AA7DH;EAkEM,iBAAiB;CAClB;;AAnEL;EAuEM,iBAAiB;CAClB;;AASL;EACE,mBAAmB;EACnB,aAAa;EACb,UAAU;EACV,YAAY;EACZ,WAAW;EACX,kBAAkB;EAClB,gBAAgB;EAChB,iBAAiB;EACjB,mBAAmB;CA8BpB;;AAvCD;EAYI,sBAAsB;EACtB,YAAa;EACb,aAAa;EACb,YAAY;EACZ,oBAAoB;EACpB,uBvDonB8C;EuDnnB9C,oBAAoB;EACpB,gBAAgB;EAWhB,0BAA0B;EAC1B,8BAAsB;CACvB;;AAhCH;EAkCI,UAAU;EACV,YAAa;EACb,aAAa;EACb,uBvD+lB8C;CuD9lB/C;;AAMH;EACE,mBAAmB;EACnB,UAAU;EACV,WAAW;EACX,aAAa;EACb,YAAY;EACZ,kBAAkB;EAClB,qBAAqB;EACrB,YvDmlBgD;EuDllBhD,mBAAmB;EACnB,0CvDukB0D;CuDnkB3D;;AAdD;EAYI,kBAAkB;CACnB;;AAKH;EAGE;;;;IAKI,YAAmC;IACnC,aAAoC;IACpC,kBAAwC;IACxC,gBAAuC;GACxC;EATH;;IAYI,mBAAyC;GAC1C;EAbH;;IAgBI,oBAA0C;GAC3C;EAIH;IACE,UAAU;IACV,WAAW;IACX,qBAAqB;GACtB;EAGD;IACE,aAAa;GACd;ChEglOF;;AiEp1OD;ElDOI,aAAa;EACb,eAAe;CAChB;;AkDTH;ElDWI,YAAY;CACb;;AkDTH;ECRE,eAAe;EACf,kBAAkB;EAClB,mBAAmB;CDQpB;;AACD;EACE,wBAAwB;CACzB;;AACD;EACE,uBAAuB;CACxB;;AAOD;EACE,yBAAyB;CAC1B;;AACD;EACE,0BAA0B;CAC3B;;AACD;EACE,mBAAmB;CACpB;;AACD;EEzBE,YAAY;EACZ,mBAAmB;EACnB,kBAAkB;EAClB,8BAA8B;EAC9B,UAAU;CFuBX;;AAOD;EACE,yBAAyB;CAC1B;;AAMD;EACE,gBAAgB;CACjB;;AGjCC;EACE,oBAAoB;CpE63OvB;;AqEn4OC;EACE,yBAAyB;CAC1B;;AAFD;EACE,yBAAyB;CAC1B;;AAFD;EACE,yBAAyB;CAC1B;;AAFD;EACE,yBAAyB;CAC1B;;ADiBH;;;;;;;;;;;;EAYE,yBAAyB;CAC1B;;AAED;EC5CE;IACE,0BAA0B;GAC3B;EACD;IAAE,0BAA0B;GAAI;EAChC;IAAE,8BAA8B;GAAI;EACpC;;IAAE,+BAA+B;GAAI;CrEu7OtC;;AoE54OC;EADF;IAEI,0BAA0B;GAE7B;CpE+4OA;;AoE74OC;EADF;IAEI,2BAA2B;GAE9B;CpEg5OA;;AoE94OC;EADF;IAEI,iCAAiC;GAEpC;CpEi5OA;;AoE/4OD;EC/DE;IACE,0BAA0B;GAC3B;EACD;IAAE,0BAA0B;GAAI;EAChC;IAAE,8BAA8B;GAAI;EACpC;;IAAE,+BAA+B;GAAI;CrEy9OtC;;AoE35OC;EADF;IAEI,0BAA0B;GAE7B;CpE85OA;;AoE55OC;EADF;IAEI,2BAA2B;GAE9B;CpE+5OA;;AoE75OC;EADF;IAEI,iCAAiC;GAEpC;CpEg6OA;;AoE95OD;EClFE;IACE,0BAA0B;GAC3B;EACD;IAAE,0BAA0B;GAAI;EAChC;IAAE,8BAA8B;GAAI;EACpC;;IAAE,+BAA+B;GAAI;CrE2/OtC;;AoE16OC;EADF;IAEI,0BAA0B;GAE7B;CpE66OA;;AoE36OC;EADF;IAEI,2BAA2B;GAE9B;CpE86OA;;AoE56OC;EADF;IAEI,iCAAiC;GAEpC;CpE+6OA;;AoE76OD;ECrGE;IACE,0BAA0B;GAC3B;EACD;IAAE,0BAA0B;GAAI;EAChC;IAAE,8BAA8B;GAAI;EACpC;;IAAE,+BAA+B;GAAI;CrE6hPtC;;AoEz7OC;EADF;IAEI,0BAA0B;GAE7B;CpE47OA;;AoE17OC;EADF;IAEI,2BAA2B;GAE9B;CpE67OA;;AoE37OC;EADF;IAEI,iCAAiC;GAEpC;CpE87OA;;AoE57OD;EC9GE;IACE,yBAAyB;GAC1B;CrE8iPF;;AoE97OD;EClHE;IACE,yBAAyB;GAC1B;CrEojPF;;AoEh8OD;ECtHE;IACE,yBAAyB;GAC1B;CrE0jPF;;AoEl8OD;EC1HE;IACE,yBAAyB;GAC1B;CrEgkPF;;AqElkPC;EACE,yBAAyB;CAC1B;;ADqIH;ECjJE;IACE,0BAA0B;GAC3B;EACD;IAAE,0BAA0B;GAAI;EAChC;IAAE,8BAA8B;GAAI;EACpC;;IAAE,+BAA+B;GAAI;CrE2lPtC;;AoE58OD;EACE,yBAAyB;CAK1B;;AAHC;EAHF;IAII,0BAA0B;GAE7B;CpEg9OA;;AoE/8OD;EACE,yBAAyB;CAK1B;;AAHC;EAHF;IAII,2BAA2B;GAE9B;CpEm9OA;;AoEl9OD;EACE,yBAAyB;CAK1B;;AAHC;EAHF;IAII,iCAAiC;GAEpC;CpEs9OA;;AoEp9OD;EChKE;IACE,yBAAyB;GAC1B;CrEwnPF;;AE/nPD;EACI,uBAAuB;EACvB,eAAe;EACf,mCAAmC;EACnC,iBAAiB;EACjB,cAAc;EACd,UAAU;CACb;;AAED;EACI,eoETmB;CpEctB;;AAND;EAIQ,sBAAsB;CACzB;;AAGL;EAEQ,WAAW;CACd;;AAEL;EACI,kBAAkB;CACrB;;AACD;EACI,mBAAmB;CACtB;;AACD;EACI,iBAAiB;CACpB;;AACD;EACI,oBAAoB;CACvB;;AAED;EACI,eAAe;EACf,qBAAqB;CACxB;;AAED;EACI,aAAa;EACb,aAAa;CAMhB;;AARD;EAKQ,aAAY;EACZ,eAAe;CAClB;;AAEL;EACI,oBoEjDmB;EpEkDnB,sBoElDmB;CpEmDtB;;AACD;EACI,eAAe;EACf,kBAAkB;EAClB,mBAAmB;CACtB;;AAED;EACI,kBAAkB;EAClB,gBAAgB;CAWnB;;AAbD;EAKQ,oBoErDY;CpEsDf;;AANL;EAQQ,oBoExDY;CpEyDf;;AATL;EAWQ,oBoE3DY;CpE4Df", "file": "app.css", "sourcesContent": ["@charset \"UTF-8\";\n/*! minireset.css v0.0.2 | MIT License | github.com/jgthms/minireset.css */\n@import url(https://fonts.googleapis.com/css?family=Raleway:200,300,400,600);\nhtml,\nbody,\np,\nol,\nul,\nli,\ndl,\ndt,\ndd,\nblockquote,\nfigure,\nfieldset,\nlegend,\ntextarea,\npre,\niframe,\nhr,\nh1,\nh2,\nh3,\nh4,\nh5,\nh6 {\n  margin: 0;\n  padding: 0;\n}\n\nh1,\nh2,\nh3,\nh4,\nh5,\nh6 {\n  font-size: 100%;\n  font-weight: normal;\n}\n\nul {\n  list-style: none;\n}\n\nbutton,\ninput,\nselect,\ntextarea {\n  margin: 0;\n}\n\nhtml {\n  box-sizing: border-box;\n}\n\n*, *:before, *:after {\n  box-sizing: inherit;\n}\n\nimg,\nembed,\nobject,\naudio,\nvideo {\n  height: auto;\n  max-width: 100%;\n}\n\niframe {\n  border: 0;\n}\n\ntable {\n  border-collapse: collapse;\n  border-spacing: 0;\n}\n\ntd,\nth {\n  padding: 0;\n  text-align: left;\n}\n\n/*!\n * Bootstrap v3.3.7 (http://getbootstrap.com)\n * Copyright 2011-2016 Twitter, Inc.\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/master/LICENSE)\n */\n/*! normalize.css v3.0.3 | MIT License | github.com/necolas/normalize.css */\nhtml {\n  font-family: sans-serif;\n  -ms-text-size-adjust: 100%;\n  -webkit-text-size-adjust: 100%;\n}\n\nbody {\n  margin: 0;\n}\n\narticle,\naside,\ndetails,\nfigcaption,\nfigure,\nfooter,\nheader,\nhgroup,\nmain,\nmenu,\nnav,\nsection,\nsummary {\n  display: block;\n}\n\naudio,\ncanvas,\nprogress,\nvideo {\n  display: inline-block;\n  vertical-align: baseline;\n}\n\naudio:not([controls]) {\n  display: none;\n  height: 0;\n}\n\n[hidden],\ntemplate {\n  display: none;\n}\n\na {\n  background-color: transparent;\n}\n\na:active,\na:hover {\n  outline: 0;\n}\n\nabbr[title] {\n  border-bottom: 1px dotted;\n}\n\nb,\nstrong {\n  font-weight: bold;\n}\n\ndfn {\n  font-style: italic;\n}\n\nh1 {\n  font-size: 2em;\n  margin: 0.67em 0;\n}\n\nmark {\n  background: #ff0;\n  color: #000;\n}\n\nsmall {\n  font-size: 80%;\n}\n\nsub,\nsup {\n  font-size: 75%;\n  line-height: 0;\n  position: relative;\n  vertical-align: baseline;\n}\n\nsup {\n  top: -0.5em;\n}\n\nsub {\n  bottom: -0.25em;\n}\n\nimg {\n  border: 0;\n}\n\nsvg:not(:root) {\n  overflow: hidden;\n}\n\nfigure {\n  margin: 1em 40px;\n}\n\nhr {\n  box-sizing: content-box;\n  height: 0;\n}\n\npre {\n  overflow: auto;\n}\n\ncode,\nkbd,\npre,\nsamp {\n  font-family: monospace, monospace;\n  font-size: 1em;\n}\n\nbutton,\ninput,\noptgroup,\nselect,\ntextarea {\n  color: inherit;\n  font: inherit;\n  margin: 0;\n}\n\nbutton {\n  overflow: visible;\n}\n\nbutton,\nselect {\n  text-transform: none;\n}\n\nbutton,\nhtml input[type=\"button\"],\ninput[type=\"reset\"],\ninput[type=\"submit\"] {\n  -webkit-appearance: button;\n  cursor: pointer;\n}\n\nbutton[disabled],\nhtml input[disabled] {\n  cursor: default;\n}\n\nbutton::-moz-focus-inner,\ninput::-moz-focus-inner {\n  border: 0;\n  padding: 0;\n}\n\ninput {\n  line-height: normal;\n}\n\ninput[type=\"checkbox\"],\ninput[type=\"radio\"] {\n  box-sizing: border-box;\n  padding: 0;\n}\n\ninput[type=\"number\"]::-webkit-inner-spin-button,\ninput[type=\"number\"]::-webkit-outer-spin-button {\n  height: auto;\n}\n\ninput[type=\"search\"] {\n  -webkit-appearance: textfield;\n  box-sizing: content-box;\n}\n\ninput[type=\"search\"]::-webkit-search-cancel-button,\ninput[type=\"search\"]::-webkit-search-decoration {\n  -webkit-appearance: none;\n}\n\nfieldset {\n  border: 1px solid #c0c0c0;\n  margin: 0 2px;\n  padding: 0.35em 0.625em 0.75em;\n}\n\nlegend {\n  border: 0;\n  padding: 0;\n}\n\ntextarea {\n  overflow: auto;\n}\n\noptgroup {\n  font-weight: bold;\n}\n\ntable {\n  border-collapse: collapse;\n  border-spacing: 0;\n}\n\ntd,\nth {\n  padding: 0;\n}\n\n/*! Source: https://github.com/h5bp/html5-boilerplate/blob/master/src/css/main.css */\n@media print {\n  *,\n  *:before,\n  *:after {\n    background: transparent !important;\n    color: #000 !important;\n    box-shadow: none !important;\n    text-shadow: none !important;\n  }\n  a,\n  a:visited {\n    text-decoration: underline;\n  }\n  a[href]:after {\n    content: \" (\" attr(href) \")\";\n  }\n  abbr[title]:after {\n    content: \" (\" attr(title) \")\";\n  }\n  a[href^=\"#\"]:after,\n  a[href^=\"javascript:\"]:after {\n    content: \"\";\n  }\n  pre,\n  blockquote {\n    border: 1px solid #999;\n    page-break-inside: avoid;\n  }\n  thead {\n    display: table-header-group;\n  }\n  tr,\n  img {\n    page-break-inside: avoid;\n  }\n  img {\n    max-width: 100% !important;\n  }\n  p,\n  h2,\n  h3 {\n    orphans: 3;\n    widows: 3;\n  }\n  h2,\n  h3 {\n    page-break-after: avoid;\n  }\n  .navbar {\n    display: none;\n  }\n  .btn > .caret,\n  .dropup > .btn > .caret {\n    border-top-color: #000 !important;\n  }\n  .label {\n    border: 1px solid #000;\n  }\n  .table {\n    border-collapse: collapse !important;\n  }\n  .table td,\n  .table th {\n    background-color: #fff !important;\n  }\n  .table-bordered th,\n  .table-bordered td {\n    border: 1px solid #ddd !important;\n  }\n}\n\n@font-face {\n  font-family: 'Glyphicons Halflings';\n  src: url(\"../fonts/bootstrap/glyphicons-halflings-regular.eot\");\n  src: url(\"../fonts/bootstrap/glyphicons-halflings-regular.eot?#iefix\") format(\"embedded-opentype\"), url(\"../fonts/bootstrap/glyphicons-halflings-regular.woff2\") format(\"woff2\"), url(\"../fonts/bootstrap/glyphicons-halflings-regular.woff\") format(\"woff\"), url(\"../fonts/bootstrap/glyphicons-halflings-regular.ttf\") format(\"truetype\"), url(\"../fonts/bootstrap/glyphicons-halflings-regular.svg#glyphicons_halflingsregular\") format(\"svg\");\n}\n\n.glyphicon {\n  position: relative;\n  top: 1px;\n  display: inline-block;\n  font-family: 'Glyphicons Halflings';\n  font-style: normal;\n  font-weight: normal;\n  line-height: 1;\n  -webkit-font-smoothing: antialiased;\n  -moz-osx-font-smoothing: grayscale;\n}\n\n.glyphicon-asterisk:before {\n  content: \"\\002a\";\n}\n\n.glyphicon-plus:before {\n  content: \"\\002b\";\n}\n\n.glyphicon-euro:before,\n.glyphicon-eur:before {\n  content: \"\\20ac\";\n}\n\n.glyphicon-minus:before {\n  content: \"\\2212\";\n}\n\n.glyphicon-cloud:before {\n  content: \"\\2601\";\n}\n\n.glyphicon-envelope:before {\n  content: \"\\2709\";\n}\n\n.glyphicon-pencil:before {\n  content: \"\\270f\";\n}\n\n.glyphicon-glass:before {\n  content: \"\\e001\";\n}\n\n.glyphicon-music:before {\n  content: \"\\e002\";\n}\n\n.glyphicon-search:before {\n  content: \"\\e003\";\n}\n\n.glyphicon-heart:before {\n  content: \"\\e005\";\n}\n\n.glyphicon-star:before {\n  content: \"\\e006\";\n}\n\n.glyphicon-star-empty:before {\n  content: \"\\e007\";\n}\n\n.glyphicon-user:before {\n  content: \"\\e008\";\n}\n\n.glyphicon-film:before {\n  content: \"\\e009\";\n}\n\n.glyphicon-th-large:before {\n  content: \"\\e010\";\n}\n\n.glyphicon-th:before {\n  content: \"\\e011\";\n}\n\n.glyphicon-th-list:before {\n  content: \"\\e012\";\n}\n\n.glyphicon-ok:before {\n  content: \"\\e013\";\n}\n\n.glyphicon-remove:before {\n  content: \"\\e014\";\n}\n\n.glyphicon-zoom-in:before {\n  content: \"\\e015\";\n}\n\n.glyphicon-zoom-out:before {\n  content: \"\\e016\";\n}\n\n.glyphicon-off:before {\n  content: \"\\e017\";\n}\n\n.glyphicon-signal:before {\n  content: \"\\e018\";\n}\n\n.glyphicon-cog:before {\n  content: \"\\e019\";\n}\n\n.glyphicon-trash:before {\n  content: \"\\e020\";\n}\n\n.glyphicon-home:before {\n  content: \"\\e021\";\n}\n\n.glyphicon-file:before {\n  content: \"\\e022\";\n}\n\n.glyphicon-time:before {\n  content: \"\\e023\";\n}\n\n.glyphicon-road:before {\n  content: \"\\e024\";\n}\n\n.glyphicon-download-alt:before {\n  content: \"\\e025\";\n}\n\n.glyphicon-download:before {\n  content: \"\\e026\";\n}\n\n.glyphicon-upload:before {\n  content: \"\\e027\";\n}\n\n.glyphicon-inbox:before {\n  content: \"\\e028\";\n}\n\n.glyphicon-play-circle:before {\n  content: \"\\e029\";\n}\n\n.glyphicon-repeat:before {\n  content: \"\\e030\";\n}\n\n.glyphicon-refresh:before {\n  content: \"\\e031\";\n}\n\n.glyphicon-list-alt:before {\n  content: \"\\e032\";\n}\n\n.glyphicon-lock:before {\n  content: \"\\e033\";\n}\n\n.glyphicon-flag:before {\n  content: \"\\e034\";\n}\n\n.glyphicon-headphones:before {\n  content: \"\\e035\";\n}\n\n.glyphicon-volume-off:before {\n  content: \"\\e036\";\n}\n\n.glyphicon-volume-down:before {\n  content: \"\\e037\";\n}\n\n.glyphicon-volume-up:before {\n  content: \"\\e038\";\n}\n\n.glyphicon-qrcode:before {\n  content: \"\\e039\";\n}\n\n.glyphicon-barcode:before {\n  content: \"\\e040\";\n}\n\n.glyphicon-tag:before {\n  content: \"\\e041\";\n}\n\n.glyphicon-tags:before {\n  content: \"\\e042\";\n}\n\n.glyphicon-book:before {\n  content: \"\\e043\";\n}\n\n.glyphicon-bookmark:before {\n  content: \"\\e044\";\n}\n\n.glyphicon-print:before {\n  content: \"\\e045\";\n}\n\n.glyphicon-camera:before {\n  content: \"\\e046\";\n}\n\n.glyphicon-font:before {\n  content: \"\\e047\";\n}\n\n.glyphicon-bold:before {\n  content: \"\\e048\";\n}\n\n.glyphicon-italic:before {\n  content: \"\\e049\";\n}\n\n.glyphicon-text-height:before {\n  content: \"\\e050\";\n}\n\n.glyphicon-text-width:before {\n  content: \"\\e051\";\n}\n\n.glyphicon-align-left:before {\n  content: \"\\e052\";\n}\n\n.glyphicon-align-center:before {\n  content: \"\\e053\";\n}\n\n.glyphicon-align-right:before {\n  content: \"\\e054\";\n}\n\n.glyphicon-align-justify:before {\n  content: \"\\e055\";\n}\n\n.glyphicon-list:before {\n  content: \"\\e056\";\n}\n\n.glyphicon-indent-left:before {\n  content: \"\\e057\";\n}\n\n.glyphicon-indent-right:before {\n  content: \"\\e058\";\n}\n\n.glyphicon-facetime-video:before {\n  content: \"\\e059\";\n}\n\n.glyphicon-picture:before {\n  content: \"\\e060\";\n}\n\n.glyphicon-map-marker:before {\n  content: \"\\e062\";\n}\n\n.glyphicon-adjust:before {\n  content: \"\\e063\";\n}\n\n.glyphicon-tint:before {\n  content: \"\\e064\";\n}\n\n.glyphicon-edit:before {\n  content: \"\\e065\";\n}\n\n.glyphicon-share:before {\n  content: \"\\e066\";\n}\n\n.glyphicon-check:before {\n  content: \"\\e067\";\n}\n\n.glyphicon-move:before {\n  content: \"\\e068\";\n}\n\n.glyphicon-step-backward:before {\n  content: \"\\e069\";\n}\n\n.glyphicon-fast-backward:before {\n  content: \"\\e070\";\n}\n\n.glyphicon-backward:before {\n  content: \"\\e071\";\n}\n\n.glyphicon-play:before {\n  content: \"\\e072\";\n}\n\n.glyphicon-pause:before {\n  content: \"\\e073\";\n}\n\n.glyphicon-stop:before {\n  content: \"\\e074\";\n}\n\n.glyphicon-forward:before {\n  content: \"\\e075\";\n}\n\n.glyphicon-fast-forward:before {\n  content: \"\\e076\";\n}\n\n.glyphicon-step-forward:before {\n  content: \"\\e077\";\n}\n\n.glyphicon-eject:before {\n  content: \"\\e078\";\n}\n\n.glyphicon-chevron-left:before {\n  content: \"\\e079\";\n}\n\n.glyphicon-chevron-right:before {\n  content: \"\\e080\";\n}\n\n.glyphicon-plus-sign:before {\n  content: \"\\e081\";\n}\n\n.glyphicon-minus-sign:before {\n  content: \"\\e082\";\n}\n\n.glyphicon-remove-sign:before {\n  content: \"\\e083\";\n}\n\n.glyphicon-ok-sign:before {\n  content: \"\\e084\";\n}\n\n.glyphicon-question-sign:before {\n  content: \"\\e085\";\n}\n\n.glyphicon-info-sign:before {\n  content: \"\\e086\";\n}\n\n.glyphicon-screenshot:before {\n  content: \"\\e087\";\n}\n\n.glyphicon-remove-circle:before {\n  content: \"\\e088\";\n}\n\n.glyphicon-ok-circle:before {\n  content: \"\\e089\";\n}\n\n.glyphicon-ban-circle:before {\n  content: \"\\e090\";\n}\n\n.glyphicon-arrow-left:before {\n  content: \"\\e091\";\n}\n\n.glyphicon-arrow-right:before {\n  content: \"\\e092\";\n}\n\n.glyphicon-arrow-up:before {\n  content: \"\\e093\";\n}\n\n.glyphicon-arrow-down:before {\n  content: \"\\e094\";\n}\n\n.glyphicon-share-alt:before {\n  content: \"\\e095\";\n}\n\n.glyphicon-resize-full:before {\n  content: \"\\e096\";\n}\n\n.glyphicon-resize-small:before {\n  content: \"\\e097\";\n}\n\n.glyphicon-exclamation-sign:before {\n  content: \"\\e101\";\n}\n\n.glyphicon-gift:before {\n  content: \"\\e102\";\n}\n\n.glyphicon-leaf:before {\n  content: \"\\e103\";\n}\n\n.glyphicon-fire:before {\n  content: \"\\e104\";\n}\n\n.glyphicon-eye-open:before {\n  content: \"\\e105\";\n}\n\n.glyphicon-eye-close:before {\n  content: \"\\e106\";\n}\n\n.glyphicon-warning-sign:before {\n  content: \"\\e107\";\n}\n\n.glyphicon-plane:before {\n  content: \"\\e108\";\n}\n\n.glyphicon-calendar:before {\n  content: \"\\e109\";\n}\n\n.glyphicon-random:before {\n  content: \"\\e110\";\n}\n\n.glyphicon-comment:before {\n  content: \"\\e111\";\n}\n\n.glyphicon-magnet:before {\n  content: \"\\e112\";\n}\n\n.glyphicon-chevron-up:before {\n  content: \"\\e113\";\n}\n\n.glyphicon-chevron-down:before {\n  content: \"\\e114\";\n}\n\n.glyphicon-retweet:before {\n  content: \"\\e115\";\n}\n\n.glyphicon-shopping-cart:before {\n  content: \"\\e116\";\n}\n\n.glyphicon-folder-close:before {\n  content: \"\\e117\";\n}\n\n.glyphicon-folder-open:before {\n  content: \"\\e118\";\n}\n\n.glyphicon-resize-vertical:before {\n  content: \"\\e119\";\n}\n\n.glyphicon-resize-horizontal:before {\n  content: \"\\e120\";\n}\n\n.glyphicon-hdd:before {\n  content: \"\\e121\";\n}\n\n.glyphicon-bullhorn:before {\n  content: \"\\e122\";\n}\n\n.glyphicon-bell:before {\n  content: \"\\e123\";\n}\n\n.glyphicon-certificate:before {\n  content: \"\\e124\";\n}\n\n.glyphicon-thumbs-up:before {\n  content: \"\\e125\";\n}\n\n.glyphicon-thumbs-down:before {\n  content: \"\\e126\";\n}\n\n.glyphicon-hand-right:before {\n  content: \"\\e127\";\n}\n\n.glyphicon-hand-left:before {\n  content: \"\\e128\";\n}\n\n.glyphicon-hand-up:before {\n  content: \"\\e129\";\n}\n\n.glyphicon-hand-down:before {\n  content: \"\\e130\";\n}\n\n.glyphicon-circle-arrow-right:before {\n  content: \"\\e131\";\n}\n\n.glyphicon-circle-arrow-left:before {\n  content: \"\\e132\";\n}\n\n.glyphicon-circle-arrow-up:before {\n  content: \"\\e133\";\n}\n\n.glyphicon-circle-arrow-down:before {\n  content: \"\\e134\";\n}\n\n.glyphicon-globe:before {\n  content: \"\\e135\";\n}\n\n.glyphicon-wrench:before {\n  content: \"\\e136\";\n}\n\n.glyphicon-tasks:before {\n  content: \"\\e137\";\n}\n\n.glyphicon-filter:before {\n  content: \"\\e138\";\n}\n\n.glyphicon-briefcase:before {\n  content: \"\\e139\";\n}\n\n.glyphicon-fullscreen:before {\n  content: \"\\e140\";\n}\n\n.glyphicon-dashboard:before {\n  content: \"\\e141\";\n}\n\n.glyphicon-paperclip:before {\n  content: \"\\e142\";\n}\n\n.glyphicon-heart-empty:before {\n  content: \"\\e143\";\n}\n\n.glyphicon-link:before {\n  content: \"\\e144\";\n}\n\n.glyphicon-phone:before {\n  content: \"\\e145\";\n}\n\n.glyphicon-pushpin:before {\n  content: \"\\e146\";\n}\n\n.glyphicon-usd:before {\n  content: \"\\e148\";\n}\n\n.glyphicon-gbp:before {\n  content: \"\\e149\";\n}\n\n.glyphicon-sort:before {\n  content: \"\\e150\";\n}\n\n.glyphicon-sort-by-alphabet:before {\n  content: \"\\e151\";\n}\n\n.glyphicon-sort-by-alphabet-alt:before {\n  content: \"\\e152\";\n}\n\n.glyphicon-sort-by-order:before {\n  content: \"\\e153\";\n}\n\n.glyphicon-sort-by-order-alt:before {\n  content: \"\\e154\";\n}\n\n.glyphicon-sort-by-attributes:before {\n  content: \"\\e155\";\n}\n\n.glyphicon-sort-by-attributes-alt:before {\n  content: \"\\e156\";\n}\n\n.glyphicon-unchecked:before {\n  content: \"\\e157\";\n}\n\n.glyphicon-expand:before {\n  content: \"\\e158\";\n}\n\n.glyphicon-collapse-down:before {\n  content: \"\\e159\";\n}\n\n.glyphicon-collapse-up:before {\n  content: \"\\e160\";\n}\n\n.glyphicon-log-in:before {\n  content: \"\\e161\";\n}\n\n.glyphicon-flash:before {\n  content: \"\\e162\";\n}\n\n.glyphicon-log-out:before {\n  content: \"\\e163\";\n}\n\n.glyphicon-new-window:before {\n  content: \"\\e164\";\n}\n\n.glyphicon-record:before {\n  content: \"\\e165\";\n}\n\n.glyphicon-save:before {\n  content: \"\\e166\";\n}\n\n.glyphicon-open:before {\n  content: \"\\e167\";\n}\n\n.glyphicon-saved:before {\n  content: \"\\e168\";\n}\n\n.glyphicon-import:before {\n  content: \"\\e169\";\n}\n\n.glyphicon-export:before {\n  content: \"\\e170\";\n}\n\n.glyphicon-send:before {\n  content: \"\\e171\";\n}\n\n.glyphicon-floppy-disk:before {\n  content: \"\\e172\";\n}\n\n.glyphicon-floppy-saved:before {\n  content: \"\\e173\";\n}\n\n.glyphicon-floppy-remove:before {\n  content: \"\\e174\";\n}\n\n.glyphicon-floppy-save:before {\n  content: \"\\e175\";\n}\n\n.glyphicon-floppy-open:before {\n  content: \"\\e176\";\n}\n\n.glyphicon-credit-card:before {\n  content: \"\\e177\";\n}\n\n.glyphicon-transfer:before {\n  content: \"\\e178\";\n}\n\n.glyphicon-cutlery:before {\n  content: \"\\e179\";\n}\n\n.glyphicon-header:before {\n  content: \"\\e180\";\n}\n\n.glyphicon-compressed:before {\n  content: \"\\e181\";\n}\n\n.glyphicon-earphone:before {\n  content: \"\\e182\";\n}\n\n.glyphicon-phone-alt:before {\n  content: \"\\e183\";\n}\n\n.glyphicon-tower:before {\n  content: \"\\e184\";\n}\n\n.glyphicon-stats:before {\n  content: \"\\e185\";\n}\n\n.glyphicon-sd-video:before {\n  content: \"\\e186\";\n}\n\n.glyphicon-hd-video:before {\n  content: \"\\e187\";\n}\n\n.glyphicon-subtitles:before {\n  content: \"\\e188\";\n}\n\n.glyphicon-sound-stereo:before {\n  content: \"\\e189\";\n}\n\n.glyphicon-sound-dolby:before {\n  content: \"\\e190\";\n}\n\n.glyphicon-sound-5-1:before {\n  content: \"\\e191\";\n}\n\n.glyphicon-sound-6-1:before {\n  content: \"\\e192\";\n}\n\n.glyphicon-sound-7-1:before {\n  content: \"\\e193\";\n}\n\n.glyphicon-copyright-mark:before {\n  content: \"\\e194\";\n}\n\n.glyphicon-registration-mark:before {\n  content: \"\\e195\";\n}\n\n.glyphicon-cloud-download:before {\n  content: \"\\e197\";\n}\n\n.glyphicon-cloud-upload:before {\n  content: \"\\e198\";\n}\n\n.glyphicon-tree-conifer:before {\n  content: \"\\e199\";\n}\n\n.glyphicon-tree-deciduous:before {\n  content: \"\\e200\";\n}\n\n.glyphicon-cd:before {\n  content: \"\\e201\";\n}\n\n.glyphicon-save-file:before {\n  content: \"\\e202\";\n}\n\n.glyphicon-open-file:before {\n  content: \"\\e203\";\n}\n\n.glyphicon-level-up:before {\n  content: \"\\e204\";\n}\n\n.glyphicon-copy:before {\n  content: \"\\e205\";\n}\n\n.glyphicon-paste:before {\n  content: \"\\e206\";\n}\n\n.glyphicon-alert:before {\n  content: \"\\e209\";\n}\n\n.glyphicon-equalizer:before {\n  content: \"\\e210\";\n}\n\n.glyphicon-king:before {\n  content: \"\\e211\";\n}\n\n.glyphicon-queen:before {\n  content: \"\\e212\";\n}\n\n.glyphicon-pawn:before {\n  content: \"\\e213\";\n}\n\n.glyphicon-bishop:before {\n  content: \"\\e214\";\n}\n\n.glyphicon-knight:before {\n  content: \"\\e215\";\n}\n\n.glyphicon-baby-formula:before {\n  content: \"\\e216\";\n}\n\n.glyphicon-tent:before {\n  content: \"\\26fa\";\n}\n\n.glyphicon-blackboard:before {\n  content: \"\\e218\";\n}\n\n.glyphicon-bed:before {\n  content: \"\\e219\";\n}\n\n.glyphicon-apple:before {\n  content: \"\\f8ff\";\n}\n\n.glyphicon-erase:before {\n  content: \"\\e221\";\n}\n\n.glyphicon-hourglass:before {\n  content: \"\\231b\";\n}\n\n.glyphicon-lamp:before {\n  content: \"\\e223\";\n}\n\n.glyphicon-duplicate:before {\n  content: \"\\e224\";\n}\n\n.glyphicon-piggy-bank:before {\n  content: \"\\e225\";\n}\n\n.glyphicon-scissors:before {\n  content: \"\\e226\";\n}\n\n.glyphicon-bitcoin:before {\n  content: \"\\e227\";\n}\n\n.glyphicon-btc:before {\n  content: \"\\e227\";\n}\n\n.glyphicon-xbt:before {\n  content: \"\\e227\";\n}\n\n.glyphicon-yen:before {\n  content: \"\\00a5\";\n}\n\n.glyphicon-jpy:before {\n  content: \"\\00a5\";\n}\n\n.glyphicon-ruble:before {\n  content: \"\\20bd\";\n}\n\n.glyphicon-rub:before {\n  content: \"\\20bd\";\n}\n\n.glyphicon-scale:before {\n  content: \"\\e230\";\n}\n\n.glyphicon-ice-lolly:before {\n  content: \"\\e231\";\n}\n\n.glyphicon-ice-lolly-tasted:before {\n  content: \"\\e232\";\n}\n\n.glyphicon-education:before {\n  content: \"\\e233\";\n}\n\n.glyphicon-option-horizontal:before {\n  content: \"\\e234\";\n}\n\n.glyphicon-option-vertical:before {\n  content: \"\\e235\";\n}\n\n.glyphicon-menu-hamburger:before {\n  content: \"\\e236\";\n}\n\n.glyphicon-modal-window:before {\n  content: \"\\e237\";\n}\n\n.glyphicon-oil:before {\n  content: \"\\e238\";\n}\n\n.glyphicon-grain:before {\n  content: \"\\e239\";\n}\n\n.glyphicon-sunglasses:before {\n  content: \"\\e240\";\n}\n\n.glyphicon-text-size:before {\n  content: \"\\e241\";\n}\n\n.glyphicon-text-color:before {\n  content: \"\\e242\";\n}\n\n.glyphicon-text-background:before {\n  content: \"\\e243\";\n}\n\n.glyphicon-object-align-top:before {\n  content: \"\\e244\";\n}\n\n.glyphicon-object-align-bottom:before {\n  content: \"\\e245\";\n}\n\n.glyphicon-object-align-horizontal:before {\n  content: \"\\e246\";\n}\n\n.glyphicon-object-align-left:before {\n  content: \"\\e247\";\n}\n\n.glyphicon-object-align-vertical:before {\n  content: \"\\e248\";\n}\n\n.glyphicon-object-align-right:before {\n  content: \"\\e249\";\n}\n\n.glyphicon-triangle-right:before {\n  content: \"\\e250\";\n}\n\n.glyphicon-triangle-left:before {\n  content: \"\\e251\";\n}\n\n.glyphicon-triangle-bottom:before {\n  content: \"\\e252\";\n}\n\n.glyphicon-triangle-top:before {\n  content: \"\\e253\";\n}\n\n.glyphicon-console:before {\n  content: \"\\e254\";\n}\n\n.glyphicon-superscript:before {\n  content: \"\\e255\";\n}\n\n.glyphicon-subscript:before {\n  content: \"\\e256\";\n}\n\n.glyphicon-menu-left:before {\n  content: \"\\e257\";\n}\n\n.glyphicon-menu-right:before {\n  content: \"\\e258\";\n}\n\n.glyphicon-menu-down:before {\n  content: \"\\e259\";\n}\n\n.glyphicon-menu-up:before {\n  content: \"\\e260\";\n}\n\n* {\n  -webkit-box-sizing: border-box;\n  -moz-box-sizing: border-box;\n  box-sizing: border-box;\n}\n\n*:before,\n*:after {\n  -webkit-box-sizing: border-box;\n  -moz-box-sizing: border-box;\n  box-sizing: border-box;\n}\n\nhtml {\n  font-size: 10px;\n  -webkit-tap-highlight-color: transparent;\n}\n\nbody {\n  font-family: \"Helvetica Neue\", Helvetica, Arial, sans-serif;\n  font-size: 14px;\n  line-height: 1.428571429;\n  color: #333333;\n  background-color: #fff;\n}\n\ninput,\nbutton,\nselect,\ntextarea {\n  font-family: inherit;\n  font-size: inherit;\n  line-height: inherit;\n}\n\na {\n  color: #337ab7;\n  text-decoration: none;\n}\n\na:hover, a:focus {\n  color: #23527c;\n  text-decoration: underline;\n}\n\na:focus {\n  outline: 5px auto -webkit-focus-ring-color;\n  outline-offset: -2px;\n}\n\nfigure {\n  margin: 0;\n}\n\nimg {\n  vertical-align: middle;\n}\n\n.img-responsive {\n  display: block;\n  max-width: 100%;\n  height: auto;\n}\n\n.img-rounded {\n  border-radius: 6px;\n}\n\n.img-thumbnail {\n  padding: 4px;\n  line-height: 1.428571429;\n  background-color: #fff;\n  border: 1px solid #ddd;\n  border-radius: 4px;\n  -webkit-transition: all 0.2s ease-in-out;\n  -o-transition: all 0.2s ease-in-out;\n  transition: all 0.2s ease-in-out;\n  display: inline-block;\n  max-width: 100%;\n  height: auto;\n}\n\n.img-circle {\n  border-radius: 50%;\n}\n\nhr {\n  margin-top: 20px;\n  margin-bottom: 20px;\n  border: 0;\n  border-top: 1px solid #eeeeee;\n}\n\n.sr-only {\n  position: absolute;\n  width: 1px;\n  height: 1px;\n  margin: -1px;\n  padding: 0;\n  overflow: hidden;\n  clip: rect(0, 0, 0, 0);\n  border: 0;\n}\n\n.sr-only-focusable:active, .sr-only-focusable:focus {\n  position: static;\n  width: auto;\n  height: auto;\n  margin: 0;\n  overflow: visible;\n  clip: auto;\n}\n\n[role=\"button\"] {\n  cursor: pointer;\n}\n\nh1, h2, h3, h4, h5, h6,\n.h1, .h2, .h3, .h4, .h5, .h6 {\n  font-family: inherit;\n  font-weight: 500;\n  line-height: 1.1;\n  color: inherit;\n}\n\nh1 small,\nh1 .small, h2 small,\nh2 .small, h3 small,\nh3 .small, h4 small,\nh4 .small, h5 small,\nh5 .small, h6 small,\nh6 .small,\n.h1 small,\n.h1 .small, .h2 small,\n.h2 .small, .h3 small,\n.h3 .small, .h4 small,\n.h4 .small, .h5 small,\n.h5 .small, .h6 small,\n.h6 .small {\n  font-weight: normal;\n  line-height: 1;\n  color: #777777;\n}\n\nh1, .h1,\nh2, .h2,\nh3, .h3 {\n  margin-top: 20px;\n  margin-bottom: 10px;\n}\n\nh1 small,\nh1 .small, .h1 small,\n.h1 .small,\nh2 small,\nh2 .small, .h2 small,\n.h2 .small,\nh3 small,\nh3 .small, .h3 small,\n.h3 .small {\n  font-size: 65%;\n}\n\nh4, .h4,\nh5, .h5,\nh6, .h6 {\n  margin-top: 10px;\n  margin-bottom: 10px;\n}\n\nh4 small,\nh4 .small, .h4 small,\n.h4 .small,\nh5 small,\nh5 .small, .h5 small,\n.h5 .small,\nh6 small,\nh6 .small, .h6 small,\n.h6 .small {\n  font-size: 75%;\n}\n\nh1, .h1 {\n  font-size: 36px;\n}\n\nh2, .h2 {\n  font-size: 30px;\n}\n\nh3, .h3 {\n  font-size: 24px;\n}\n\nh4, .h4 {\n  font-size: 18px;\n}\n\nh5, .h5 {\n  font-size: 14px;\n}\n\nh6, .h6 {\n  font-size: 12px;\n}\n\np {\n  margin: 0 0 10px;\n}\n\n.lead {\n  margin-bottom: 20px;\n  font-size: 16px;\n  font-weight: 300;\n  line-height: 1.4;\n}\n\n@media (min-width: 768px) {\n  .lead {\n    font-size: 21px;\n  }\n}\n\nsmall,\n.small {\n  font-size: 85%;\n}\n\nmark,\n.mark {\n  background-color: #fcf8e3;\n  padding: .2em;\n}\n\n.text-left {\n  text-align: left;\n}\n\n.text-right {\n  text-align: right;\n}\n\n.text-center {\n  text-align: center;\n}\n\n.text-justify {\n  text-align: justify;\n}\n\n.text-nowrap {\n  white-space: nowrap;\n}\n\n.text-lowercase {\n  text-transform: lowercase;\n}\n\n.text-uppercase, .initialism {\n  text-transform: uppercase;\n}\n\n.text-capitalize {\n  text-transform: capitalize;\n}\n\n.text-muted {\n  color: #777777;\n}\n\n.text-primary {\n  color: #337ab7;\n}\n\na.text-primary:hover,\na.text-primary:focus {\n  color: #286090;\n}\n\n.text-success {\n  color: #3c763d;\n}\n\na.text-success:hover,\na.text-success:focus {\n  color: #2b542c;\n}\n\n.text-info {\n  color: #31708f;\n}\n\na.text-info:hover,\na.text-info:focus {\n  color: #245269;\n}\n\n.text-warning {\n  color: #8a6d3b;\n}\n\na.text-warning:hover,\na.text-warning:focus {\n  color: #66512c;\n}\n\n.text-danger {\n  color: #a94442;\n}\n\na.text-danger:hover,\na.text-danger:focus {\n  color: #843534;\n}\n\n.bg-primary {\n  color: #fff;\n}\n\n.bg-primary {\n  background-color: #337ab7;\n}\n\na.bg-primary:hover,\na.bg-primary:focus {\n  background-color: #286090;\n}\n\n.bg-success {\n  background-color: #dff0d8;\n}\n\na.bg-success:hover,\na.bg-success:focus {\n  background-color: #c1e2b3;\n}\n\n.bg-info {\n  background-color: #d9edf7;\n}\n\na.bg-info:hover,\na.bg-info:focus {\n  background-color: #afd9ee;\n}\n\n.bg-warning {\n  background-color: #fcf8e3;\n}\n\na.bg-warning:hover,\na.bg-warning:focus {\n  background-color: #f7ecb5;\n}\n\n.bg-danger {\n  background-color: #f2dede;\n}\n\na.bg-danger:hover,\na.bg-danger:focus {\n  background-color: #e4b9b9;\n}\n\n.page-header {\n  padding-bottom: 9px;\n  margin: 40px 0 20px;\n  border-bottom: 1px solid #eeeeee;\n}\n\nul,\nol {\n  margin-top: 0;\n  margin-bottom: 10px;\n}\n\nul ul,\nul ol,\nol ul,\nol ol {\n  margin-bottom: 0;\n}\n\n.list-unstyled {\n  padding-left: 0;\n  list-style: none;\n}\n\n.list-inline {\n  padding-left: 0;\n  list-style: none;\n  margin-left: -5px;\n}\n\n.list-inline > li {\n  display: inline-block;\n  padding-left: 5px;\n  padding-right: 5px;\n}\n\ndl {\n  margin-top: 0;\n  margin-bottom: 20px;\n}\n\ndt,\ndd {\n  line-height: 1.428571429;\n}\n\ndt {\n  font-weight: bold;\n}\n\ndd {\n  margin-left: 0;\n}\n\n.dl-horizontal dd:before, .dl-horizontal dd:after {\n  content: \" \";\n  display: table;\n}\n\n.dl-horizontal dd:after {\n  clear: both;\n}\n\n@media (min-width: 768px) {\n  .dl-horizontal dt {\n    float: left;\n    width: 160px;\n    clear: left;\n    text-align: right;\n    overflow: hidden;\n    text-overflow: ellipsis;\n    white-space: nowrap;\n  }\n  .dl-horizontal dd {\n    margin-left: 180px;\n  }\n}\n\nabbr[title],\nabbr[data-original-title] {\n  cursor: help;\n  border-bottom: 1px dotted #777777;\n}\n\n.initialism {\n  font-size: 90%;\n}\n\nblockquote {\n  padding: 10px 20px;\n  margin: 0 0 20px;\n  font-size: 17.5px;\n  border-left: 5px solid #eeeeee;\n}\n\nblockquote p:last-child,\nblockquote ul:last-child,\nblockquote ol:last-child {\n  margin-bottom: 0;\n}\n\nblockquote footer,\nblockquote small,\nblockquote .small {\n  display: block;\n  font-size: 80%;\n  line-height: 1.428571429;\n  color: #777777;\n}\n\nblockquote footer:before,\nblockquote small:before,\nblockquote .small:before {\n  content: '\\2014 \\00A0';\n}\n\n.blockquote-reverse,\nblockquote.pull-right {\n  padding-right: 15px;\n  padding-left: 0;\n  border-right: 5px solid #eeeeee;\n  border-left: 0;\n  text-align: right;\n}\n\n.blockquote-reverse footer:before,\n.blockquote-reverse small:before,\n.blockquote-reverse .small:before,\nblockquote.pull-right footer:before,\nblockquote.pull-right small:before,\nblockquote.pull-right .small:before {\n  content: '';\n}\n\n.blockquote-reverse footer:after,\n.blockquote-reverse small:after,\n.blockquote-reverse .small:after,\nblockquote.pull-right footer:after,\nblockquote.pull-right small:after,\nblockquote.pull-right .small:after {\n  content: '\\00A0 \\2014';\n}\n\naddress {\n  margin-bottom: 20px;\n  font-style: normal;\n  line-height: 1.428571429;\n}\n\ncode,\nkbd,\npre,\nsamp {\n  font-family: Menlo, Monaco, Consolas, \"Courier New\", monospace;\n}\n\ncode {\n  padding: 2px 4px;\n  font-size: 90%;\n  color: #c7254e;\n  background-color: #f9f2f4;\n  border-radius: 4px;\n}\n\nkbd {\n  padding: 2px 4px;\n  font-size: 90%;\n  color: #fff;\n  background-color: #333;\n  border-radius: 3px;\n  box-shadow: inset 0 -1px 0 rgba(0, 0, 0, 0.25);\n}\n\nkbd kbd {\n  padding: 0;\n  font-size: 100%;\n  font-weight: bold;\n  box-shadow: none;\n}\n\npre {\n  display: block;\n  padding: 9.5px;\n  margin: 0 0 10px;\n  font-size: 13px;\n  line-height: 1.428571429;\n  word-break: break-all;\n  word-wrap: break-word;\n  color: #333333;\n  background-color: #f5f5f5;\n  border: 1px solid #ccc;\n  border-radius: 4px;\n}\n\npre code {\n  padding: 0;\n  font-size: inherit;\n  color: inherit;\n  white-space: pre-wrap;\n  background-color: transparent;\n  border-radius: 0;\n}\n\n.pre-scrollable {\n  max-height: 340px;\n  overflow-y: scroll;\n}\n\n.container {\n  margin-right: auto;\n  margin-left: auto;\n  padding-left: 15px;\n  padding-right: 15px;\n}\n\n.container:before, .container:after {\n  content: \" \";\n  display: table;\n}\n\n.container:after {\n  clear: both;\n}\n\n@media (min-width: 768px) {\n  .container {\n    width: 750px;\n  }\n}\n\n@media (min-width: 992px) {\n  .container {\n    width: 970px;\n  }\n}\n\n@media (min-width: 1200px) {\n  .container {\n    width: 1170px;\n  }\n}\n\n.container-fluid {\n  margin-right: auto;\n  margin-left: auto;\n  padding-left: 15px;\n  padding-right: 15px;\n}\n\n.container-fluid:before, .container-fluid:after {\n  content: \" \";\n  display: table;\n}\n\n.container-fluid:after {\n  clear: both;\n}\n\n.row {\n  margin-left: -15px;\n  margin-right: -15px;\n}\n\n.row:before, .row:after {\n  content: \" \";\n  display: table;\n}\n\n.row:after {\n  clear: both;\n}\n\n.col-xs-1, .col-sm-1, .col-md-1, .col-lg-1, .col-xs-2, .col-sm-2, .col-md-2, .col-lg-2, .col-xs-3, .col-sm-3, .col-md-3, .col-lg-3, .col-xs-4, .col-sm-4, .col-md-4, .col-lg-4, .col-xs-5, .col-sm-5, .col-md-5, .col-lg-5, .col-xs-6, .col-sm-6, .col-md-6, .col-lg-6, .col-xs-7, .col-sm-7, .col-md-7, .col-lg-7, .col-xs-8, .col-sm-8, .col-md-8, .col-lg-8, .col-xs-9, .col-sm-9, .col-md-9, .col-lg-9, .col-xs-10, .col-sm-10, .col-md-10, .col-lg-10, .col-xs-11, .col-sm-11, .col-md-11, .col-lg-11, .col-xs-12, .col-sm-12, .col-md-12, .col-lg-12 {\n  position: relative;\n  min-height: 1px;\n  padding-left: 15px;\n  padding-right: 15px;\n}\n\n.col-xs-1, .col-xs-2, .col-xs-3, .col-xs-4, .col-xs-5, .col-xs-6, .col-xs-7, .col-xs-8, .col-xs-9, .col-xs-10, .col-xs-11, .col-xs-12 {\n  float: left;\n}\n\n.col-xs-1 {\n  width: 8.3333333333%;\n}\n\n.col-xs-2 {\n  width: 16.6666666667%;\n}\n\n.col-xs-3 {\n  width: 25%;\n}\n\n.col-xs-4 {\n  width: 33.3333333333%;\n}\n\n.col-xs-5 {\n  width: 41.6666666667%;\n}\n\n.col-xs-6 {\n  width: 50%;\n}\n\n.col-xs-7 {\n  width: 58.3333333333%;\n}\n\n.col-xs-8 {\n  width: 66.6666666667%;\n}\n\n.col-xs-9 {\n  width: 75%;\n}\n\n.col-xs-10 {\n  width: 83.3333333333%;\n}\n\n.col-xs-11 {\n  width: 91.6666666667%;\n}\n\n.col-xs-12 {\n  width: 100%;\n}\n\n.col-xs-pull-0 {\n  right: auto;\n}\n\n.col-xs-pull-1 {\n  right: 8.3333333333%;\n}\n\n.col-xs-pull-2 {\n  right: 16.6666666667%;\n}\n\n.col-xs-pull-3 {\n  right: 25%;\n}\n\n.col-xs-pull-4 {\n  right: 33.3333333333%;\n}\n\n.col-xs-pull-5 {\n  right: 41.6666666667%;\n}\n\n.col-xs-pull-6 {\n  right: 50%;\n}\n\n.col-xs-pull-7 {\n  right: 58.3333333333%;\n}\n\n.col-xs-pull-8 {\n  right: 66.6666666667%;\n}\n\n.col-xs-pull-9 {\n  right: 75%;\n}\n\n.col-xs-pull-10 {\n  right: 83.3333333333%;\n}\n\n.col-xs-pull-11 {\n  right: 91.6666666667%;\n}\n\n.col-xs-pull-12 {\n  right: 100%;\n}\n\n.col-xs-push-0 {\n  left: auto;\n}\n\n.col-xs-push-1 {\n  left: 8.3333333333%;\n}\n\n.col-xs-push-2 {\n  left: 16.6666666667%;\n}\n\n.col-xs-push-3 {\n  left: 25%;\n}\n\n.col-xs-push-4 {\n  left: 33.3333333333%;\n}\n\n.col-xs-push-5 {\n  left: 41.6666666667%;\n}\n\n.col-xs-push-6 {\n  left: 50%;\n}\n\n.col-xs-push-7 {\n  left: 58.3333333333%;\n}\n\n.col-xs-push-8 {\n  left: 66.6666666667%;\n}\n\n.col-xs-push-9 {\n  left: 75%;\n}\n\n.col-xs-push-10 {\n  left: 83.3333333333%;\n}\n\n.col-xs-push-11 {\n  left: 91.6666666667%;\n}\n\n.col-xs-push-12 {\n  left: 100%;\n}\n\n.col-xs-offset-0 {\n  margin-left: 0%;\n}\n\n.col-xs-offset-1 {\n  margin-left: 8.3333333333%;\n}\n\n.col-xs-offset-2 {\n  margin-left: 16.6666666667%;\n}\n\n.col-xs-offset-3 {\n  margin-left: 25%;\n}\n\n.col-xs-offset-4 {\n  margin-left: 33.3333333333%;\n}\n\n.col-xs-offset-5 {\n  margin-left: 41.6666666667%;\n}\n\n.col-xs-offset-6 {\n  margin-left: 50%;\n}\n\n.col-xs-offset-7 {\n  margin-left: 58.3333333333%;\n}\n\n.col-xs-offset-8 {\n  margin-left: 66.6666666667%;\n}\n\n.col-xs-offset-9 {\n  margin-left: 75%;\n}\n\n.col-xs-offset-10 {\n  margin-left: 83.3333333333%;\n}\n\n.col-xs-offset-11 {\n  margin-left: 91.6666666667%;\n}\n\n.col-xs-offset-12 {\n  margin-left: 100%;\n}\n\n@media (min-width: 768px) {\n  .col-sm-1, .col-sm-2, .col-sm-3, .col-sm-4, .col-sm-5, .col-sm-6, .col-sm-7, .col-sm-8, .col-sm-9, .col-sm-10, .col-sm-11, .col-sm-12 {\n    float: left;\n  }\n  .col-sm-1 {\n    width: 8.3333333333%;\n  }\n  .col-sm-2 {\n    width: 16.6666666667%;\n  }\n  .col-sm-3 {\n    width: 25%;\n  }\n  .col-sm-4 {\n    width: 33.3333333333%;\n  }\n  .col-sm-5 {\n    width: 41.6666666667%;\n  }\n  .col-sm-6 {\n    width: 50%;\n  }\n  .col-sm-7 {\n    width: 58.3333333333%;\n  }\n  .col-sm-8 {\n    width: 66.6666666667%;\n  }\n  .col-sm-9 {\n    width: 75%;\n  }\n  .col-sm-10 {\n    width: 83.3333333333%;\n  }\n  .col-sm-11 {\n    width: 91.6666666667%;\n  }\n  .col-sm-12 {\n    width: 100%;\n  }\n  .col-sm-pull-0 {\n    right: auto;\n  }\n  .col-sm-pull-1 {\n    right: 8.3333333333%;\n  }\n  .col-sm-pull-2 {\n    right: 16.6666666667%;\n  }\n  .col-sm-pull-3 {\n    right: 25%;\n  }\n  .col-sm-pull-4 {\n    right: 33.3333333333%;\n  }\n  .col-sm-pull-5 {\n    right: 41.6666666667%;\n  }\n  .col-sm-pull-6 {\n    right: 50%;\n  }\n  .col-sm-pull-7 {\n    right: 58.3333333333%;\n  }\n  .col-sm-pull-8 {\n    right: 66.6666666667%;\n  }\n  .col-sm-pull-9 {\n    right: 75%;\n  }\n  .col-sm-pull-10 {\n    right: 83.3333333333%;\n  }\n  .col-sm-pull-11 {\n    right: 91.6666666667%;\n  }\n  .col-sm-pull-12 {\n    right: 100%;\n  }\n  .col-sm-push-0 {\n    left: auto;\n  }\n  .col-sm-push-1 {\n    left: 8.3333333333%;\n  }\n  .col-sm-push-2 {\n    left: 16.6666666667%;\n  }\n  .col-sm-push-3 {\n    left: 25%;\n  }\n  .col-sm-push-4 {\n    left: 33.3333333333%;\n  }\n  .col-sm-push-5 {\n    left: 41.6666666667%;\n  }\n  .col-sm-push-6 {\n    left: 50%;\n  }\n  .col-sm-push-7 {\n    left: 58.3333333333%;\n  }\n  .col-sm-push-8 {\n    left: 66.6666666667%;\n  }\n  .col-sm-push-9 {\n    left: 75%;\n  }\n  .col-sm-push-10 {\n    left: 83.3333333333%;\n  }\n  .col-sm-push-11 {\n    left: 91.6666666667%;\n  }\n  .col-sm-push-12 {\n    left: 100%;\n  }\n  .col-sm-offset-0 {\n    margin-left: 0%;\n  }\n  .col-sm-offset-1 {\n    margin-left: 8.3333333333%;\n  }\n  .col-sm-offset-2 {\n    margin-left: 16.6666666667%;\n  }\n  .col-sm-offset-3 {\n    margin-left: 25%;\n  }\n  .col-sm-offset-4 {\n    margin-left: 33.3333333333%;\n  }\n  .col-sm-offset-5 {\n    margin-left: 41.6666666667%;\n  }\n  .col-sm-offset-6 {\n    margin-left: 50%;\n  }\n  .col-sm-offset-7 {\n    margin-left: 58.3333333333%;\n  }\n  .col-sm-offset-8 {\n    margin-left: 66.6666666667%;\n  }\n  .col-sm-offset-9 {\n    margin-left: 75%;\n  }\n  .col-sm-offset-10 {\n    margin-left: 83.3333333333%;\n  }\n  .col-sm-offset-11 {\n    margin-left: 91.6666666667%;\n  }\n  .col-sm-offset-12 {\n    margin-left: 100%;\n  }\n}\n\n@media (min-width: 992px) {\n  .col-md-1, .col-md-2, .col-md-3, .col-md-4, .col-md-5, .col-md-6, .col-md-7, .col-md-8, .col-md-9, .col-md-10, .col-md-11, .col-md-12 {\n    float: left;\n  }\n  .col-md-1 {\n    width: 8.3333333333%;\n  }\n  .col-md-2 {\n    width: 16.6666666667%;\n  }\n  .col-md-3 {\n    width: 25%;\n  }\n  .col-md-4 {\n    width: 33.3333333333%;\n  }\n  .col-md-5 {\n    width: 41.6666666667%;\n  }\n  .col-md-6 {\n    width: 50%;\n  }\n  .col-md-7 {\n    width: 58.3333333333%;\n  }\n  .col-md-8 {\n    width: 66.6666666667%;\n  }\n  .col-md-9 {\n    width: 75%;\n  }\n  .col-md-10 {\n    width: 83.3333333333%;\n  }\n  .col-md-11 {\n    width: 91.6666666667%;\n  }\n  .col-md-12 {\n    width: 100%;\n  }\n  .col-md-pull-0 {\n    right: auto;\n  }\n  .col-md-pull-1 {\n    right: 8.3333333333%;\n  }\n  .col-md-pull-2 {\n    right: 16.6666666667%;\n  }\n  .col-md-pull-3 {\n    right: 25%;\n  }\n  .col-md-pull-4 {\n    right: 33.3333333333%;\n  }\n  .col-md-pull-5 {\n    right: 41.6666666667%;\n  }\n  .col-md-pull-6 {\n    right: 50%;\n  }\n  .col-md-pull-7 {\n    right: 58.3333333333%;\n  }\n  .col-md-pull-8 {\n    right: 66.6666666667%;\n  }\n  .col-md-pull-9 {\n    right: 75%;\n  }\n  .col-md-pull-10 {\n    right: 83.3333333333%;\n  }\n  .col-md-pull-11 {\n    right: 91.6666666667%;\n  }\n  .col-md-pull-12 {\n    right: 100%;\n  }\n  .col-md-push-0 {\n    left: auto;\n  }\n  .col-md-push-1 {\n    left: 8.3333333333%;\n  }\n  .col-md-push-2 {\n    left: 16.6666666667%;\n  }\n  .col-md-push-3 {\n    left: 25%;\n  }\n  .col-md-push-4 {\n    left: 33.3333333333%;\n  }\n  .col-md-push-5 {\n    left: 41.6666666667%;\n  }\n  .col-md-push-6 {\n    left: 50%;\n  }\n  .col-md-push-7 {\n    left: 58.3333333333%;\n  }\n  .col-md-push-8 {\n    left: 66.6666666667%;\n  }\n  .col-md-push-9 {\n    left: 75%;\n  }\n  .col-md-push-10 {\n    left: 83.3333333333%;\n  }\n  .col-md-push-11 {\n    left: 91.6666666667%;\n  }\n  .col-md-push-12 {\n    left: 100%;\n  }\n  .col-md-offset-0 {\n    margin-left: 0%;\n  }\n  .col-md-offset-1 {\n    margin-left: 8.3333333333%;\n  }\n  .col-md-offset-2 {\n    margin-left: 16.6666666667%;\n  }\n  .col-md-offset-3 {\n    margin-left: 25%;\n  }\n  .col-md-offset-4 {\n    margin-left: 33.3333333333%;\n  }\n  .col-md-offset-5 {\n    margin-left: 41.6666666667%;\n  }\n  .col-md-offset-6 {\n    margin-left: 50%;\n  }\n  .col-md-offset-7 {\n    margin-left: 58.3333333333%;\n  }\n  .col-md-offset-8 {\n    margin-left: 66.6666666667%;\n  }\n  .col-md-offset-9 {\n    margin-left: 75%;\n  }\n  .col-md-offset-10 {\n    margin-left: 83.3333333333%;\n  }\n  .col-md-offset-11 {\n    margin-left: 91.6666666667%;\n  }\n  .col-md-offset-12 {\n    margin-left: 100%;\n  }\n}\n\n@media (min-width: 1200px) {\n  .col-lg-1, .col-lg-2, .col-lg-3, .col-lg-4, .col-lg-5, .col-lg-6, .col-lg-7, .col-lg-8, .col-lg-9, .col-lg-10, .col-lg-11, .col-lg-12 {\n    float: left;\n  }\n  .col-lg-1 {\n    width: 8.3333333333%;\n  }\n  .col-lg-2 {\n    width: 16.6666666667%;\n  }\n  .col-lg-3 {\n    width: 25%;\n  }\n  .col-lg-4 {\n    width: 33.3333333333%;\n  }\n  .col-lg-5 {\n    width: 41.6666666667%;\n  }\n  .col-lg-6 {\n    width: 50%;\n  }\n  .col-lg-7 {\n    width: 58.3333333333%;\n  }\n  .col-lg-8 {\n    width: 66.6666666667%;\n  }\n  .col-lg-9 {\n    width: 75%;\n  }\n  .col-lg-10 {\n    width: 83.3333333333%;\n  }\n  .col-lg-11 {\n    width: 91.6666666667%;\n  }\n  .col-lg-12 {\n    width: 100%;\n  }\n  .col-lg-pull-0 {\n    right: auto;\n  }\n  .col-lg-pull-1 {\n    right: 8.3333333333%;\n  }\n  .col-lg-pull-2 {\n    right: 16.6666666667%;\n  }\n  .col-lg-pull-3 {\n    right: 25%;\n  }\n  .col-lg-pull-4 {\n    right: 33.3333333333%;\n  }\n  .col-lg-pull-5 {\n    right: 41.6666666667%;\n  }\n  .col-lg-pull-6 {\n    right: 50%;\n  }\n  .col-lg-pull-7 {\n    right: 58.3333333333%;\n  }\n  .col-lg-pull-8 {\n    right: 66.6666666667%;\n  }\n  .col-lg-pull-9 {\n    right: 75%;\n  }\n  .col-lg-pull-10 {\n    right: 83.3333333333%;\n  }\n  .col-lg-pull-11 {\n    right: 91.6666666667%;\n  }\n  .col-lg-pull-12 {\n    right: 100%;\n  }\n  .col-lg-push-0 {\n    left: auto;\n  }\n  .col-lg-push-1 {\n    left: 8.3333333333%;\n  }\n  .col-lg-push-2 {\n    left: 16.6666666667%;\n  }\n  .col-lg-push-3 {\n    left: 25%;\n  }\n  .col-lg-push-4 {\n    left: 33.3333333333%;\n  }\n  .col-lg-push-5 {\n    left: 41.6666666667%;\n  }\n  .col-lg-push-6 {\n    left: 50%;\n  }\n  .col-lg-push-7 {\n    left: 58.3333333333%;\n  }\n  .col-lg-push-8 {\n    left: 66.6666666667%;\n  }\n  .col-lg-push-9 {\n    left: 75%;\n  }\n  .col-lg-push-10 {\n    left: 83.3333333333%;\n  }\n  .col-lg-push-11 {\n    left: 91.6666666667%;\n  }\n  .col-lg-push-12 {\n    left: 100%;\n  }\n  .col-lg-offset-0 {\n    margin-left: 0%;\n  }\n  .col-lg-offset-1 {\n    margin-left: 8.3333333333%;\n  }\n  .col-lg-offset-2 {\n    margin-left: 16.6666666667%;\n  }\n  .col-lg-offset-3 {\n    margin-left: 25%;\n  }\n  .col-lg-offset-4 {\n    margin-left: 33.3333333333%;\n  }\n  .col-lg-offset-5 {\n    margin-left: 41.6666666667%;\n  }\n  .col-lg-offset-6 {\n    margin-left: 50%;\n  }\n  .col-lg-offset-7 {\n    margin-left: 58.3333333333%;\n  }\n  .col-lg-offset-8 {\n    margin-left: 66.6666666667%;\n  }\n  .col-lg-offset-9 {\n    margin-left: 75%;\n  }\n  .col-lg-offset-10 {\n    margin-left: 83.3333333333%;\n  }\n  .col-lg-offset-11 {\n    margin-left: 91.6666666667%;\n  }\n  .col-lg-offset-12 {\n    margin-left: 100%;\n  }\n}\n\ntable {\n  background-color: transparent;\n}\n\ncaption {\n  padding-top: 8px;\n  padding-bottom: 8px;\n  color: #777777;\n  text-align: left;\n}\n\nth {\n  text-align: left;\n}\n\n.table {\n  width: 100%;\n  max-width: 100%;\n  margin-bottom: 20px;\n}\n\n.table > thead > tr > th,\n.table > thead > tr > td,\n.table > tbody > tr > th,\n.table > tbody > tr > td,\n.table > tfoot > tr > th,\n.table > tfoot > tr > td {\n  padding: 8px;\n  line-height: 1.428571429;\n  vertical-align: top;\n  border-top: 1px solid #ddd;\n}\n\n.table > thead > tr > th {\n  vertical-align: bottom;\n  border-bottom: 2px solid #ddd;\n}\n\n.table > caption + thead > tr:first-child > th,\n.table > caption + thead > tr:first-child > td,\n.table > colgroup + thead > tr:first-child > th,\n.table > colgroup + thead > tr:first-child > td,\n.table > thead:first-child > tr:first-child > th,\n.table > thead:first-child > tr:first-child > td {\n  border-top: 0;\n}\n\n.table > tbody + tbody {\n  border-top: 2px solid #ddd;\n}\n\n.table .table {\n  background-color: #fff;\n}\n\n.table-condensed > thead > tr > th,\n.table-condensed > thead > tr > td,\n.table-condensed > tbody > tr > th,\n.table-condensed > tbody > tr > td,\n.table-condensed > tfoot > tr > th,\n.table-condensed > tfoot > tr > td {\n  padding: 5px;\n}\n\n.table-bordered {\n  border: 1px solid #ddd;\n}\n\n.table-bordered > thead > tr > th,\n.table-bordered > thead > tr > td,\n.table-bordered > tbody > tr > th,\n.table-bordered > tbody > tr > td,\n.table-bordered > tfoot > tr > th,\n.table-bordered > tfoot > tr > td {\n  border: 1px solid #ddd;\n}\n\n.table-bordered > thead > tr > th,\n.table-bordered > thead > tr > td {\n  border-bottom-width: 2px;\n}\n\n.table-striped > tbody > tr:nth-of-type(odd) {\n  background-color: #f9f9f9;\n}\n\n.table-hover > tbody > tr:hover {\n  background-color: #f5f5f5;\n}\n\ntable col[class*=\"col-\"] {\n  position: static;\n  float: none;\n  display: table-column;\n}\n\ntable td[class*=\"col-\"],\ntable th[class*=\"col-\"] {\n  position: static;\n  float: none;\n  display: table-cell;\n}\n\n.table > thead > tr > td.active,\n.table > thead > tr > th.active,\n.table > thead > tr.active > td,\n.table > thead > tr.active > th,\n.table > tbody > tr > td.active,\n.table > tbody > tr > th.active,\n.table > tbody > tr.active > td,\n.table > tbody > tr.active > th,\n.table > tfoot > tr > td.active,\n.table > tfoot > tr > th.active,\n.table > tfoot > tr.active > td,\n.table > tfoot > tr.active > th {\n  background-color: #f5f5f5;\n}\n\n.table-hover > tbody > tr > td.active:hover,\n.table-hover > tbody > tr > th.active:hover,\n.table-hover > tbody > tr.active:hover > td,\n.table-hover > tbody > tr:hover > .active,\n.table-hover > tbody > tr.active:hover > th {\n  background-color: #e8e8e8;\n}\n\n.table > thead > tr > td.success,\n.table > thead > tr > th.success,\n.table > thead > tr.success > td,\n.table > thead > tr.success > th,\n.table > tbody > tr > td.success,\n.table > tbody > tr > th.success,\n.table > tbody > tr.success > td,\n.table > tbody > tr.success > th,\n.table > tfoot > tr > td.success,\n.table > tfoot > tr > th.success,\n.table > tfoot > tr.success > td,\n.table > tfoot > tr.success > th {\n  background-color: #dff0d8;\n}\n\n.table-hover > tbody > tr > td.success:hover,\n.table-hover > tbody > tr > th.success:hover,\n.table-hover > tbody > tr.success:hover > td,\n.table-hover > tbody > tr:hover > .success,\n.table-hover > tbody > tr.success:hover > th {\n  background-color: #d0e9c6;\n}\n\n.table > thead > tr > td.info,\n.table > thead > tr > th.info,\n.table > thead > tr.info > td,\n.table > thead > tr.info > th,\n.table > tbody > tr > td.info,\n.table > tbody > tr > th.info,\n.table > tbody > tr.info > td,\n.table > tbody > tr.info > th,\n.table > tfoot > tr > td.info,\n.table > tfoot > tr > th.info,\n.table > tfoot > tr.info > td,\n.table > tfoot > tr.info > th {\n  background-color: #d9edf7;\n}\n\n.table-hover > tbody > tr > td.info:hover,\n.table-hover > tbody > tr > th.info:hover,\n.table-hover > tbody > tr.info:hover > td,\n.table-hover > tbody > tr:hover > .info,\n.table-hover > tbody > tr.info:hover > th {\n  background-color: #c4e3f3;\n}\n\n.table > thead > tr > td.warning,\n.table > thead > tr > th.warning,\n.table > thead > tr.warning > td,\n.table > thead > tr.warning > th,\n.table > tbody > tr > td.warning,\n.table > tbody > tr > th.warning,\n.table > tbody > tr.warning > td,\n.table > tbody > tr.warning > th,\n.table > tfoot > tr > td.warning,\n.table > tfoot > tr > th.warning,\n.table > tfoot > tr.warning > td,\n.table > tfoot > tr.warning > th {\n  background-color: #fcf8e3;\n}\n\n.table-hover > tbody > tr > td.warning:hover,\n.table-hover > tbody > tr > th.warning:hover,\n.table-hover > tbody > tr.warning:hover > td,\n.table-hover > tbody > tr:hover > .warning,\n.table-hover > tbody > tr.warning:hover > th {\n  background-color: #faf2cc;\n}\n\n.table > thead > tr > td.danger,\n.table > thead > tr > th.danger,\n.table > thead > tr.danger > td,\n.table > thead > tr.danger > th,\n.table > tbody > tr > td.danger,\n.table > tbody > tr > th.danger,\n.table > tbody > tr.danger > td,\n.table > tbody > tr.danger > th,\n.table > tfoot > tr > td.danger,\n.table > tfoot > tr > th.danger,\n.table > tfoot > tr.danger > td,\n.table > tfoot > tr.danger > th {\n  background-color: #f2dede;\n}\n\n.table-hover > tbody > tr > td.danger:hover,\n.table-hover > tbody > tr > th.danger:hover,\n.table-hover > tbody > tr.danger:hover > td,\n.table-hover > tbody > tr:hover > .danger,\n.table-hover > tbody > tr.danger:hover > th {\n  background-color: #ebcccc;\n}\n\n.table-responsive {\n  overflow-x: auto;\n  min-height: 0.01%;\n}\n\n@media screen and (max-width: 767px) {\n  .table-responsive {\n    width: 100%;\n    margin-bottom: 15px;\n    overflow-y: hidden;\n    -ms-overflow-style: -ms-autohiding-scrollbar;\n    border: 1px solid #ddd;\n  }\n  .table-responsive > .table {\n    margin-bottom: 0;\n  }\n  .table-responsive > .table > thead > tr > th,\n  .table-responsive > .table > thead > tr > td,\n  .table-responsive > .table > tbody > tr > th,\n  .table-responsive > .table > tbody > tr > td,\n  .table-responsive > .table > tfoot > tr > th,\n  .table-responsive > .table > tfoot > tr > td {\n    white-space: nowrap;\n  }\n  .table-responsive > .table-bordered {\n    border: 0;\n  }\n  .table-responsive > .table-bordered > thead > tr > th:first-child,\n  .table-responsive > .table-bordered > thead > tr > td:first-child,\n  .table-responsive > .table-bordered > tbody > tr > th:first-child,\n  .table-responsive > .table-bordered > tbody > tr > td:first-child,\n  .table-responsive > .table-bordered > tfoot > tr > th:first-child,\n  .table-responsive > .table-bordered > tfoot > tr > td:first-child {\n    border-left: 0;\n  }\n  .table-responsive > .table-bordered > thead > tr > th:last-child,\n  .table-responsive > .table-bordered > thead > tr > td:last-child,\n  .table-responsive > .table-bordered > tbody > tr > th:last-child,\n  .table-responsive > .table-bordered > tbody > tr > td:last-child,\n  .table-responsive > .table-bordered > tfoot > tr > th:last-child,\n  .table-responsive > .table-bordered > tfoot > tr > td:last-child {\n    border-right: 0;\n  }\n  .table-responsive > .table-bordered > tbody > tr:last-child > th,\n  .table-responsive > .table-bordered > tbody > tr:last-child > td,\n  .table-responsive > .table-bordered > tfoot > tr:last-child > th,\n  .table-responsive > .table-bordered > tfoot > tr:last-child > td {\n    border-bottom: 0;\n  }\n}\n\nfieldset {\n  padding: 0;\n  margin: 0;\n  border: 0;\n  min-width: 0;\n}\n\nlegend {\n  display: block;\n  width: 100%;\n  padding: 0;\n  margin-bottom: 20px;\n  font-size: 21px;\n  line-height: inherit;\n  color: #333333;\n  border: 0;\n  border-bottom: 1px solid #e5e5e5;\n}\n\nlabel {\n  display: inline-block;\n  max-width: 100%;\n  margin-bottom: 5px;\n  font-weight: bold;\n}\n\ninput[type=\"search\"] {\n  -webkit-box-sizing: border-box;\n  -moz-box-sizing: border-box;\n  box-sizing: border-box;\n}\n\ninput[type=\"radio\"],\ninput[type=\"checkbox\"] {\n  margin: 4px 0 0;\n  margin-top: 1px \\9;\n  line-height: normal;\n}\n\ninput[type=\"file\"] {\n  display: block;\n}\n\ninput[type=\"range\"] {\n  display: block;\n  width: 100%;\n}\n\nselect[multiple],\nselect[size] {\n  height: auto;\n}\n\ninput[type=\"file\"]:focus,\ninput[type=\"radio\"]:focus,\ninput[type=\"checkbox\"]:focus {\n  outline: 5px auto -webkit-focus-ring-color;\n  outline-offset: -2px;\n}\n\noutput {\n  display: block;\n  padding-top: 7px;\n  font-size: 14px;\n  line-height: 1.428571429;\n  color: #555555;\n}\n\n.form-control {\n  display: block;\n  width: 100%;\n  height: 34px;\n  padding: 6px 12px;\n  font-size: 14px;\n  line-height: 1.428571429;\n  color: #555555;\n  background-color: #fff;\n  background-image: none;\n  border: 1px solid #ccc;\n  border-radius: 4px;\n  -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075);\n  box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075);\n  -webkit-transition: border-color ease-in-out 0.15s, box-shadow ease-in-out 0.15s;\n  -o-transition: border-color ease-in-out 0.15s, box-shadow ease-in-out 0.15s;\n  transition: border-color ease-in-out 0.15s, box-shadow ease-in-out 0.15s;\n}\n\n.form-control:focus {\n  border-color: #66afe9;\n  outline: 0;\n  -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 8px rgba(102, 175, 233, 0.6);\n  box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 8px rgba(102, 175, 233, 0.6);\n}\n\n.form-control::-moz-placeholder {\n  color: #999;\n  opacity: 1;\n}\n\n.form-control:-ms-input-placeholder {\n  color: #999;\n}\n\n.form-control::-webkit-input-placeholder {\n  color: #999;\n}\n\n.form-control::-ms-expand {\n  border: 0;\n  background-color: transparent;\n}\n\n.form-control[disabled], .form-control[readonly],\nfieldset[disabled] .form-control {\n  background-color: #eeeeee;\n  opacity: 1;\n}\n\n.form-control[disabled],\nfieldset[disabled] .form-control {\n  cursor: not-allowed;\n}\n\ntextarea.form-control {\n  height: auto;\n}\n\ninput[type=\"search\"] {\n  -webkit-appearance: none;\n}\n\n@media screen and (-webkit-min-device-pixel-ratio: 0) {\n  input[type=\"date\"].form-control,\n  input[type=\"time\"].form-control,\n  input[type=\"datetime-local\"].form-control,\n  input[type=\"month\"].form-control {\n    line-height: 34px;\n  }\n  input[type=\"date\"].input-sm, .input-group-sm > input[type=\"date\"].form-control,\n  .input-group-sm > input[type=\"date\"].input-group-addon,\n  .input-group-sm > .input-group-btn > input[type=\"date\"].btn,\n  .input-group-sm input[type=\"date\"],\n  input[type=\"time\"].input-sm,\n  .input-group-sm > input[type=\"time\"].form-control,\n  .input-group-sm > input[type=\"time\"].input-group-addon,\n  .input-group-sm > .input-group-btn > input[type=\"time\"].btn,\n  .input-group-sm\n  input[type=\"time\"],\n  input[type=\"datetime-local\"].input-sm,\n  .input-group-sm > input[type=\"datetime-local\"].form-control,\n  .input-group-sm > input[type=\"datetime-local\"].input-group-addon,\n  .input-group-sm > .input-group-btn > input[type=\"datetime-local\"].btn,\n  .input-group-sm\n  input[type=\"datetime-local\"],\n  input[type=\"month\"].input-sm,\n  .input-group-sm > input[type=\"month\"].form-control,\n  .input-group-sm > input[type=\"month\"].input-group-addon,\n  .input-group-sm > .input-group-btn > input[type=\"month\"].btn,\n  .input-group-sm\n  input[type=\"month\"] {\n    line-height: 30px;\n  }\n  input[type=\"date\"].input-lg, .input-group-lg > input[type=\"date\"].form-control,\n  .input-group-lg > input[type=\"date\"].input-group-addon,\n  .input-group-lg > .input-group-btn > input[type=\"date\"].btn,\n  .input-group-lg input[type=\"date\"],\n  input[type=\"time\"].input-lg,\n  .input-group-lg > input[type=\"time\"].form-control,\n  .input-group-lg > input[type=\"time\"].input-group-addon,\n  .input-group-lg > .input-group-btn > input[type=\"time\"].btn,\n  .input-group-lg\n  input[type=\"time\"],\n  input[type=\"datetime-local\"].input-lg,\n  .input-group-lg > input[type=\"datetime-local\"].form-control,\n  .input-group-lg > input[type=\"datetime-local\"].input-group-addon,\n  .input-group-lg > .input-group-btn > input[type=\"datetime-local\"].btn,\n  .input-group-lg\n  input[type=\"datetime-local\"],\n  input[type=\"month\"].input-lg,\n  .input-group-lg > input[type=\"month\"].form-control,\n  .input-group-lg > input[type=\"month\"].input-group-addon,\n  .input-group-lg > .input-group-btn > input[type=\"month\"].btn,\n  .input-group-lg\n  input[type=\"month\"] {\n    line-height: 46px;\n  }\n}\n\n.form-group {\n  margin-bottom: 15px;\n}\n\n.radio,\n.checkbox {\n  position: relative;\n  display: block;\n  margin-top: 10px;\n  margin-bottom: 10px;\n}\n\n.radio label,\n.checkbox label {\n  min-height: 20px;\n  padding-left: 20px;\n  margin-bottom: 0;\n  font-weight: normal;\n  cursor: pointer;\n}\n\n.radio input[type=\"radio\"],\n.radio-inline input[type=\"radio\"],\n.checkbox input[type=\"checkbox\"],\n.checkbox-inline input[type=\"checkbox\"] {\n  position: absolute;\n  margin-left: -20px;\n  margin-top: 4px \\9;\n}\n\n.radio + .radio,\n.checkbox + .checkbox {\n  margin-top: -5px;\n}\n\n.radio-inline,\n.checkbox-inline {\n  position: relative;\n  display: inline-block;\n  padding-left: 20px;\n  margin-bottom: 0;\n  vertical-align: middle;\n  font-weight: normal;\n  cursor: pointer;\n}\n\n.radio-inline + .radio-inline,\n.checkbox-inline + .checkbox-inline {\n  margin-top: 0;\n  margin-left: 10px;\n}\n\ninput[type=\"radio\"][disabled], input[type=\"radio\"].disabled,\nfieldset[disabled] input[type=\"radio\"],\ninput[type=\"checkbox\"][disabled],\ninput[type=\"checkbox\"].disabled,\nfieldset[disabled]\ninput[type=\"checkbox\"] {\n  cursor: not-allowed;\n}\n\n.radio-inline.disabled,\nfieldset[disabled] .radio-inline,\n.checkbox-inline.disabled,\nfieldset[disabled]\n.checkbox-inline {\n  cursor: not-allowed;\n}\n\n.radio.disabled label,\nfieldset[disabled] .radio label,\n.checkbox.disabled label,\nfieldset[disabled]\n.checkbox label {\n  cursor: not-allowed;\n}\n\n.form-control-static {\n  padding-top: 7px;\n  padding-bottom: 7px;\n  margin-bottom: 0;\n  min-height: 34px;\n}\n\n.form-control-static.input-lg, .input-group-lg > .form-control-static.form-control,\n.input-group-lg > .form-control-static.input-group-addon,\n.input-group-lg > .input-group-btn > .form-control-static.btn, .form-control-static.input-sm, .input-group-sm > .form-control-static.form-control,\n.input-group-sm > .form-control-static.input-group-addon,\n.input-group-sm > .input-group-btn > .form-control-static.btn {\n  padding-left: 0;\n  padding-right: 0;\n}\n\n.input-sm, .input-group-sm > .form-control,\n.input-group-sm > .input-group-addon,\n.input-group-sm > .input-group-btn > .btn {\n  height: 30px;\n  padding: 5px 10px;\n  font-size: 12px;\n  line-height: 1.5;\n  border-radius: 3px;\n}\n\nselect.input-sm, .input-group-sm > select.form-control,\n.input-group-sm > select.input-group-addon,\n.input-group-sm > .input-group-btn > select.btn {\n  height: 30px;\n  line-height: 30px;\n}\n\ntextarea.input-sm, .input-group-sm > textarea.form-control,\n.input-group-sm > textarea.input-group-addon,\n.input-group-sm > .input-group-btn > textarea.btn,\nselect[multiple].input-sm,\n.input-group-sm > select[multiple].form-control,\n.input-group-sm > select[multiple].input-group-addon,\n.input-group-sm > .input-group-btn > select[multiple].btn {\n  height: auto;\n}\n\n.form-group-sm .form-control {\n  height: 30px;\n  padding: 5px 10px;\n  font-size: 12px;\n  line-height: 1.5;\n  border-radius: 3px;\n}\n\n.form-group-sm select.form-control {\n  height: 30px;\n  line-height: 30px;\n}\n\n.form-group-sm textarea.form-control,\n.form-group-sm select[multiple].form-control {\n  height: auto;\n}\n\n.form-group-sm .form-control-static {\n  height: 30px;\n  min-height: 32px;\n  padding: 6px 10px;\n  font-size: 12px;\n  line-height: 1.5;\n}\n\n.input-lg, .input-group-lg > .form-control,\n.input-group-lg > .input-group-addon,\n.input-group-lg > .input-group-btn > .btn {\n  height: 46px;\n  padding: 10px 16px;\n  font-size: 18px;\n  line-height: 1.3333333;\n  border-radius: 6px;\n}\n\nselect.input-lg, .input-group-lg > select.form-control,\n.input-group-lg > select.input-group-addon,\n.input-group-lg > .input-group-btn > select.btn {\n  height: 46px;\n  line-height: 46px;\n}\n\ntextarea.input-lg, .input-group-lg > textarea.form-control,\n.input-group-lg > textarea.input-group-addon,\n.input-group-lg > .input-group-btn > textarea.btn,\nselect[multiple].input-lg,\n.input-group-lg > select[multiple].form-control,\n.input-group-lg > select[multiple].input-group-addon,\n.input-group-lg > .input-group-btn > select[multiple].btn {\n  height: auto;\n}\n\n.form-group-lg .form-control {\n  height: 46px;\n  padding: 10px 16px;\n  font-size: 18px;\n  line-height: 1.3333333;\n  border-radius: 6px;\n}\n\n.form-group-lg select.form-control {\n  height: 46px;\n  line-height: 46px;\n}\n\n.form-group-lg textarea.form-control,\n.form-group-lg select[multiple].form-control {\n  height: auto;\n}\n\n.form-group-lg .form-control-static {\n  height: 46px;\n  min-height: 38px;\n  padding: 11px 16px;\n  font-size: 18px;\n  line-height: 1.3333333;\n}\n\n.has-feedback {\n  position: relative;\n}\n\n.has-feedback .form-control {\n  padding-right: 42.5px;\n}\n\n.form-control-feedback {\n  position: absolute;\n  top: 0;\n  right: 0;\n  z-index: 2;\n  display: block;\n  width: 34px;\n  height: 34px;\n  line-height: 34px;\n  text-align: center;\n  pointer-events: none;\n}\n\n.input-lg + .form-control-feedback, .input-group-lg > .form-control + .form-control-feedback,\n.input-group-lg > .input-group-addon + .form-control-feedback,\n.input-group-lg > .input-group-btn > .btn + .form-control-feedback,\n.input-group-lg + .form-control-feedback,\n.form-group-lg .form-control + .form-control-feedback {\n  width: 46px;\n  height: 46px;\n  line-height: 46px;\n}\n\n.input-sm + .form-control-feedback, .input-group-sm > .form-control + .form-control-feedback,\n.input-group-sm > .input-group-addon + .form-control-feedback,\n.input-group-sm > .input-group-btn > .btn + .form-control-feedback,\n.input-group-sm + .form-control-feedback,\n.form-group-sm .form-control + .form-control-feedback {\n  width: 30px;\n  height: 30px;\n  line-height: 30px;\n}\n\n.has-success .help-block,\n.has-success .control-label,\n.has-success .radio,\n.has-success .checkbox,\n.has-success .radio-inline,\n.has-success .checkbox-inline,\n.has-success.radio label,\n.has-success.checkbox label,\n.has-success.radio-inline label,\n.has-success.checkbox-inline label {\n  color: #3c763d;\n}\n\n.has-success .form-control {\n  border-color: #3c763d;\n  -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075);\n  box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075);\n}\n\n.has-success .form-control:focus {\n  border-color: #2b542c;\n  -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 6px #67b168;\n  box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 6px #67b168;\n}\n\n.has-success .input-group-addon {\n  color: #3c763d;\n  border-color: #3c763d;\n  background-color: #dff0d8;\n}\n\n.has-success .form-control-feedback {\n  color: #3c763d;\n}\n\n.has-warning .help-block,\n.has-warning .control-label,\n.has-warning .radio,\n.has-warning .checkbox,\n.has-warning .radio-inline,\n.has-warning .checkbox-inline,\n.has-warning.radio label,\n.has-warning.checkbox label,\n.has-warning.radio-inline label,\n.has-warning.checkbox-inline label {\n  color: #8a6d3b;\n}\n\n.has-warning .form-control {\n  border-color: #8a6d3b;\n  -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075);\n  box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075);\n}\n\n.has-warning .form-control:focus {\n  border-color: #66512c;\n  -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 6px #c0a16b;\n  box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 6px #c0a16b;\n}\n\n.has-warning .input-group-addon {\n  color: #8a6d3b;\n  border-color: #8a6d3b;\n  background-color: #fcf8e3;\n}\n\n.has-warning .form-control-feedback {\n  color: #8a6d3b;\n}\n\n.has-error .help-block,\n.has-error .control-label,\n.has-error .radio,\n.has-error .checkbox,\n.has-error .radio-inline,\n.has-error .checkbox-inline,\n.has-error.radio label,\n.has-error.checkbox label,\n.has-error.radio-inline label,\n.has-error.checkbox-inline label {\n  color: #a94442;\n}\n\n.has-error .form-control {\n  border-color: #a94442;\n  -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075);\n  box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075);\n}\n\n.has-error .form-control:focus {\n  border-color: #843534;\n  -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 6px #ce8483;\n  box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 6px #ce8483;\n}\n\n.has-error .input-group-addon {\n  color: #a94442;\n  border-color: #a94442;\n  background-color: #f2dede;\n}\n\n.has-error .form-control-feedback {\n  color: #a94442;\n}\n\n.has-feedback label ~ .form-control-feedback {\n  top: 25px;\n}\n\n.has-feedback label.sr-only ~ .form-control-feedback {\n  top: 0;\n}\n\n.help-block {\n  display: block;\n  margin-top: 5px;\n  margin-bottom: 10px;\n  color: #737373;\n}\n\n@media (min-width: 768px) {\n  .form-inline .form-group {\n    display: inline-block;\n    margin-bottom: 0;\n    vertical-align: middle;\n  }\n  .form-inline .form-control {\n    display: inline-block;\n    width: auto;\n    vertical-align: middle;\n  }\n  .form-inline .form-control-static {\n    display: inline-block;\n  }\n  .form-inline .input-group {\n    display: inline-table;\n    vertical-align: middle;\n  }\n  .form-inline .input-group .input-group-addon,\n  .form-inline .input-group .input-group-btn,\n  .form-inline .input-group .form-control {\n    width: auto;\n  }\n  .form-inline .input-group > .form-control {\n    width: 100%;\n  }\n  .form-inline .control-label {\n    margin-bottom: 0;\n    vertical-align: middle;\n  }\n  .form-inline .radio,\n  .form-inline .checkbox {\n    display: inline-block;\n    margin-top: 0;\n    margin-bottom: 0;\n    vertical-align: middle;\n  }\n  .form-inline .radio label,\n  .form-inline .checkbox label {\n    padding-left: 0;\n  }\n  .form-inline .radio input[type=\"radio\"],\n  .form-inline .checkbox input[type=\"checkbox\"] {\n    position: relative;\n    margin-left: 0;\n  }\n  .form-inline .has-feedback .form-control-feedback {\n    top: 0;\n  }\n}\n\n.form-horizontal .radio,\n.form-horizontal .checkbox,\n.form-horizontal .radio-inline,\n.form-horizontal .checkbox-inline {\n  margin-top: 0;\n  margin-bottom: 0;\n  padding-top: 7px;\n}\n\n.form-horizontal .radio,\n.form-horizontal .checkbox {\n  min-height: 27px;\n}\n\n.form-horizontal .form-group {\n  margin-left: -15px;\n  margin-right: -15px;\n}\n\n.form-horizontal .form-group:before, .form-horizontal .form-group:after {\n  content: \" \";\n  display: table;\n}\n\n.form-horizontal .form-group:after {\n  clear: both;\n}\n\n@media (min-width: 768px) {\n  .form-horizontal .control-label {\n    text-align: right;\n    margin-bottom: 0;\n    padding-top: 7px;\n  }\n}\n\n.form-horizontal .has-feedback .form-control-feedback {\n  right: 15px;\n}\n\n@media (min-width: 768px) {\n  .form-horizontal .form-group-lg .control-label {\n    padding-top: 11px;\n    font-size: 18px;\n  }\n}\n\n@media (min-width: 768px) {\n  .form-horizontal .form-group-sm .control-label {\n    padding-top: 6px;\n    font-size: 12px;\n  }\n}\n\n.btn {\n  display: inline-block;\n  margin-bottom: 0;\n  font-weight: normal;\n  text-align: center;\n  vertical-align: middle;\n  touch-action: manipulation;\n  cursor: pointer;\n  background-image: none;\n  border: 1px solid transparent;\n  white-space: nowrap;\n  padding: 6px 12px;\n  font-size: 14px;\n  line-height: 1.428571429;\n  border-radius: 4px;\n  -webkit-user-select: none;\n  -moz-user-select: none;\n  -ms-user-select: none;\n  user-select: none;\n}\n\n.btn:focus, .btn.focus, .btn:active:focus, .btn:active.focus, .btn.active:focus, .btn.active.focus {\n  outline: 5px auto -webkit-focus-ring-color;\n  outline-offset: -2px;\n}\n\n.btn:hover, .btn:focus, .btn.focus {\n  color: #333;\n  text-decoration: none;\n}\n\n.btn:active, .btn.active {\n  outline: 0;\n  background-image: none;\n  -webkit-box-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);\n  box-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);\n}\n\n.btn.disabled, .btn[disabled],\nfieldset[disabled] .btn {\n  cursor: not-allowed;\n  opacity: 0.65;\n  filter: alpha(opacity=65);\n  -webkit-box-shadow: none;\n  box-shadow: none;\n}\n\na.btn.disabled,\nfieldset[disabled] a.btn {\n  pointer-events: none;\n}\n\n.btn-default {\n  color: #333;\n  background-color: #fff;\n  border-color: #ccc;\n}\n\n.btn-default:focus, .btn-default.focus {\n  color: #333;\n  background-color: #e6e6e6;\n  border-color: #8c8c8c;\n}\n\n.btn-default:hover {\n  color: #333;\n  background-color: #e6e6e6;\n  border-color: #adadad;\n}\n\n.btn-default:active, .btn-default.active,\n.open > .btn-default.dropdown-toggle {\n  color: #333;\n  background-color: #e6e6e6;\n  border-color: #adadad;\n}\n\n.btn-default:active:hover, .btn-default:active:focus, .btn-default:active.focus, .btn-default.active:hover, .btn-default.active:focus, .btn-default.active.focus,\n.open > .btn-default.dropdown-toggle:hover,\n.open > .btn-default.dropdown-toggle:focus,\n.open > .btn-default.dropdown-toggle.focus {\n  color: #333;\n  background-color: #d4d4d4;\n  border-color: #8c8c8c;\n}\n\n.btn-default:active, .btn-default.active,\n.open > .btn-default.dropdown-toggle {\n  background-image: none;\n}\n\n.btn-default.disabled:hover, .btn-default.disabled:focus, .btn-default.disabled.focus, .btn-default[disabled]:hover, .btn-default[disabled]:focus, .btn-default[disabled].focus,\nfieldset[disabled] .btn-default:hover,\nfieldset[disabled] .btn-default:focus,\nfieldset[disabled] .btn-default.focus {\n  background-color: #fff;\n  border-color: #ccc;\n}\n\n.btn-default .badge {\n  color: #fff;\n  background-color: #333;\n}\n\n.btn-primary {\n  color: #fff;\n  background-color: #337ab7;\n  border-color: #2e6da4;\n}\n\n.btn-primary:focus, .btn-primary.focus {\n  color: #fff;\n  background-color: #286090;\n  border-color: #122b40;\n}\n\n.btn-primary:hover {\n  color: #fff;\n  background-color: #286090;\n  border-color: #204d74;\n}\n\n.btn-primary:active, .btn-primary.active,\n.open > .btn-primary.dropdown-toggle {\n  color: #fff;\n  background-color: #286090;\n  border-color: #204d74;\n}\n\n.btn-primary:active:hover, .btn-primary:active:focus, .btn-primary:active.focus, .btn-primary.active:hover, .btn-primary.active:focus, .btn-primary.active.focus,\n.open > .btn-primary.dropdown-toggle:hover,\n.open > .btn-primary.dropdown-toggle:focus,\n.open > .btn-primary.dropdown-toggle.focus {\n  color: #fff;\n  background-color: #204d74;\n  border-color: #122b40;\n}\n\n.btn-primary:active, .btn-primary.active,\n.open > .btn-primary.dropdown-toggle {\n  background-image: none;\n}\n\n.btn-primary.disabled:hover, .btn-primary.disabled:focus, .btn-primary.disabled.focus, .btn-primary[disabled]:hover, .btn-primary[disabled]:focus, .btn-primary[disabled].focus,\nfieldset[disabled] .btn-primary:hover,\nfieldset[disabled] .btn-primary:focus,\nfieldset[disabled] .btn-primary.focus {\n  background-color: #337ab7;\n  border-color: #2e6da4;\n}\n\n.btn-primary .badge {\n  color: #337ab7;\n  background-color: #fff;\n}\n\n.btn-success {\n  color: #fff;\n  background-color: #5cb85c;\n  border-color: #4cae4c;\n}\n\n.btn-success:focus, .btn-success.focus {\n  color: #fff;\n  background-color: #449d44;\n  border-color: #255625;\n}\n\n.btn-success:hover {\n  color: #fff;\n  background-color: #449d44;\n  border-color: #398439;\n}\n\n.btn-success:active, .btn-success.active,\n.open > .btn-success.dropdown-toggle {\n  color: #fff;\n  background-color: #449d44;\n  border-color: #398439;\n}\n\n.btn-success:active:hover, .btn-success:active:focus, .btn-success:active.focus, .btn-success.active:hover, .btn-success.active:focus, .btn-success.active.focus,\n.open > .btn-success.dropdown-toggle:hover,\n.open > .btn-success.dropdown-toggle:focus,\n.open > .btn-success.dropdown-toggle.focus {\n  color: #fff;\n  background-color: #398439;\n  border-color: #255625;\n}\n\n.btn-success:active, .btn-success.active,\n.open > .btn-success.dropdown-toggle {\n  background-image: none;\n}\n\n.btn-success.disabled:hover, .btn-success.disabled:focus, .btn-success.disabled.focus, .btn-success[disabled]:hover, .btn-success[disabled]:focus, .btn-success[disabled].focus,\nfieldset[disabled] .btn-success:hover,\nfieldset[disabled] .btn-success:focus,\nfieldset[disabled] .btn-success.focus {\n  background-color: #5cb85c;\n  border-color: #4cae4c;\n}\n\n.btn-success .badge {\n  color: #5cb85c;\n  background-color: #fff;\n}\n\n.btn-info {\n  color: #fff;\n  background-color: #5bc0de;\n  border-color: #46b8da;\n}\n\n.btn-info:focus, .btn-info.focus {\n  color: #fff;\n  background-color: #31b0d5;\n  border-color: #1b6d85;\n}\n\n.btn-info:hover {\n  color: #fff;\n  background-color: #31b0d5;\n  border-color: #269abc;\n}\n\n.btn-info:active, .btn-info.active,\n.open > .btn-info.dropdown-toggle {\n  color: #fff;\n  background-color: #31b0d5;\n  border-color: #269abc;\n}\n\n.btn-info:active:hover, .btn-info:active:focus, .btn-info:active.focus, .btn-info.active:hover, .btn-info.active:focus, .btn-info.active.focus,\n.open > .btn-info.dropdown-toggle:hover,\n.open > .btn-info.dropdown-toggle:focus,\n.open > .btn-info.dropdown-toggle.focus {\n  color: #fff;\n  background-color: #269abc;\n  border-color: #1b6d85;\n}\n\n.btn-info:active, .btn-info.active,\n.open > .btn-info.dropdown-toggle {\n  background-image: none;\n}\n\n.btn-info.disabled:hover, .btn-info.disabled:focus, .btn-info.disabled.focus, .btn-info[disabled]:hover, .btn-info[disabled]:focus, .btn-info[disabled].focus,\nfieldset[disabled] .btn-info:hover,\nfieldset[disabled] .btn-info:focus,\nfieldset[disabled] .btn-info.focus {\n  background-color: #5bc0de;\n  border-color: #46b8da;\n}\n\n.btn-info .badge {\n  color: #5bc0de;\n  background-color: #fff;\n}\n\n.btn-warning {\n  color: #fff;\n  background-color: #f0ad4e;\n  border-color: #eea236;\n}\n\n.btn-warning:focus, .btn-warning.focus {\n  color: #fff;\n  background-color: #ec971f;\n  border-color: #985f0d;\n}\n\n.btn-warning:hover {\n  color: #fff;\n  background-color: #ec971f;\n  border-color: #d58512;\n}\n\n.btn-warning:active, .btn-warning.active,\n.open > .btn-warning.dropdown-toggle {\n  color: #fff;\n  background-color: #ec971f;\n  border-color: #d58512;\n}\n\n.btn-warning:active:hover, .btn-warning:active:focus, .btn-warning:active.focus, .btn-warning.active:hover, .btn-warning.active:focus, .btn-warning.active.focus,\n.open > .btn-warning.dropdown-toggle:hover,\n.open > .btn-warning.dropdown-toggle:focus,\n.open > .btn-warning.dropdown-toggle.focus {\n  color: #fff;\n  background-color: #d58512;\n  border-color: #985f0d;\n}\n\n.btn-warning:active, .btn-warning.active,\n.open > .btn-warning.dropdown-toggle {\n  background-image: none;\n}\n\n.btn-warning.disabled:hover, .btn-warning.disabled:focus, .btn-warning.disabled.focus, .btn-warning[disabled]:hover, .btn-warning[disabled]:focus, .btn-warning[disabled].focus,\nfieldset[disabled] .btn-warning:hover,\nfieldset[disabled] .btn-warning:focus,\nfieldset[disabled] .btn-warning.focus {\n  background-color: #f0ad4e;\n  border-color: #eea236;\n}\n\n.btn-warning .badge {\n  color: #f0ad4e;\n  background-color: #fff;\n}\n\n.btn-danger {\n  color: #fff;\n  background-color: #d9534f;\n  border-color: #d43f3a;\n}\n\n.btn-danger:focus, .btn-danger.focus {\n  color: #fff;\n  background-color: #c9302c;\n  border-color: #761c19;\n}\n\n.btn-danger:hover {\n  color: #fff;\n  background-color: #c9302c;\n  border-color: #ac2925;\n}\n\n.btn-danger:active, .btn-danger.active,\n.open > .btn-danger.dropdown-toggle {\n  color: #fff;\n  background-color: #c9302c;\n  border-color: #ac2925;\n}\n\n.btn-danger:active:hover, .btn-danger:active:focus, .btn-danger:active.focus, .btn-danger.active:hover, .btn-danger.active:focus, .btn-danger.active.focus,\n.open > .btn-danger.dropdown-toggle:hover,\n.open > .btn-danger.dropdown-toggle:focus,\n.open > .btn-danger.dropdown-toggle.focus {\n  color: #fff;\n  background-color: #ac2925;\n  border-color: #761c19;\n}\n\n.btn-danger:active, .btn-danger.active,\n.open > .btn-danger.dropdown-toggle {\n  background-image: none;\n}\n\n.btn-danger.disabled:hover, .btn-danger.disabled:focus, .btn-danger.disabled.focus, .btn-danger[disabled]:hover, .btn-danger[disabled]:focus, .btn-danger[disabled].focus,\nfieldset[disabled] .btn-danger:hover,\nfieldset[disabled] .btn-danger:focus,\nfieldset[disabled] .btn-danger.focus {\n  background-color: #d9534f;\n  border-color: #d43f3a;\n}\n\n.btn-danger .badge {\n  color: #d9534f;\n  background-color: #fff;\n}\n\n.btn-link {\n  color: #337ab7;\n  font-weight: normal;\n  border-radius: 0;\n}\n\n.btn-link, .btn-link:active, .btn-link.active, .btn-link[disabled],\nfieldset[disabled] .btn-link {\n  background-color: transparent;\n  -webkit-box-shadow: none;\n  box-shadow: none;\n}\n\n.btn-link, .btn-link:hover, .btn-link:focus, .btn-link:active {\n  border-color: transparent;\n}\n\n.btn-link:hover, .btn-link:focus {\n  color: #23527c;\n  text-decoration: underline;\n  background-color: transparent;\n}\n\n.btn-link[disabled]:hover, .btn-link[disabled]:focus,\nfieldset[disabled] .btn-link:hover,\nfieldset[disabled] .btn-link:focus {\n  color: #777777;\n  text-decoration: none;\n}\n\n.btn-lg, .btn-group-lg > .btn {\n  padding: 10px 16px;\n  font-size: 18px;\n  line-height: 1.3333333;\n  border-radius: 6px;\n}\n\n.btn-sm, .btn-group-sm > .btn {\n  padding: 5px 10px;\n  font-size: 12px;\n  line-height: 1.5;\n  border-radius: 3px;\n}\n\n.btn-xs, .btn-group-xs > .btn {\n  padding: 1px 5px;\n  font-size: 12px;\n  line-height: 1.5;\n  border-radius: 3px;\n}\n\n.btn-block {\n  display: block;\n  width: 100%;\n}\n\n.btn-block + .btn-block {\n  margin-top: 5px;\n}\n\ninput[type=\"submit\"].btn-block,\ninput[type=\"reset\"].btn-block,\ninput[type=\"button\"].btn-block {\n  width: 100%;\n}\n\n.fade {\n  opacity: 0;\n  -webkit-transition: opacity 0.15s linear;\n  -o-transition: opacity 0.15s linear;\n  transition: opacity 0.15s linear;\n}\n\n.fade.in {\n  opacity: 1;\n}\n\n.collapse {\n  display: none;\n}\n\n.collapse.in {\n  display: block;\n}\n\ntr.collapse.in {\n  display: table-row;\n}\n\ntbody.collapse.in {\n  display: table-row-group;\n}\n\n.collapsing {\n  position: relative;\n  height: 0;\n  overflow: hidden;\n  -webkit-transition-property: height, visibility;\n  transition-property: height, visibility;\n  -webkit-transition-duration: 0.35s;\n  transition-duration: 0.35s;\n  -webkit-transition-timing-function: ease;\n  transition-timing-function: ease;\n}\n\n.caret {\n  display: inline-block;\n  width: 0;\n  height: 0;\n  margin-left: 2px;\n  vertical-align: middle;\n  border-top: 4px dashed;\n  border-top: 4px solid \\9;\n  border-right: 4px solid transparent;\n  border-left: 4px solid transparent;\n}\n\n.dropup,\n.dropdown {\n  position: relative;\n}\n\n.dropdown-toggle:focus {\n  outline: 0;\n}\n\n.dropdown-menu {\n  position: absolute;\n  top: 100%;\n  left: 0;\n  z-index: 1000;\n  display: none;\n  float: left;\n  min-width: 160px;\n  padding: 5px 0;\n  margin: 2px 0 0;\n  list-style: none;\n  font-size: 14px;\n  text-align: left;\n  background-color: #fff;\n  border: 1px solid #ccc;\n  border: 1px solid rgba(0, 0, 0, 0.15);\n  border-radius: 4px;\n  -webkit-box-shadow: 0 6px 12px rgba(0, 0, 0, 0.175);\n  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.175);\n  background-clip: padding-box;\n}\n\n.dropdown-menu.pull-right {\n  right: 0;\n  left: auto;\n}\n\n.dropdown-menu .divider {\n  height: 1px;\n  margin: 9px 0;\n  overflow: hidden;\n  background-color: #e5e5e5;\n}\n\n.dropdown-menu > li > a {\n  display: block;\n  padding: 3px 20px;\n  clear: both;\n  font-weight: normal;\n  line-height: 1.428571429;\n  color: #333333;\n  white-space: nowrap;\n}\n\n.dropdown-menu > li > a:hover, .dropdown-menu > li > a:focus {\n  text-decoration: none;\n  color: #262626;\n  background-color: #f5f5f5;\n}\n\n.dropdown-menu > .active > a, .dropdown-menu > .active > a:hover, .dropdown-menu > .active > a:focus {\n  color: #fff;\n  text-decoration: none;\n  outline: 0;\n  background-color: #337ab7;\n}\n\n.dropdown-menu > .disabled > a, .dropdown-menu > .disabled > a:hover, .dropdown-menu > .disabled > a:focus {\n  color: #777777;\n}\n\n.dropdown-menu > .disabled > a:hover, .dropdown-menu > .disabled > a:focus {\n  text-decoration: none;\n  background-color: transparent;\n  background-image: none;\n  filter: progid:DXImageTransform.Microsoft.gradient(enabled = false);\n  cursor: not-allowed;\n}\n\n.open > .dropdown-menu {\n  display: block;\n}\n\n.open > a {\n  outline: 0;\n}\n\n.dropdown-menu-right {\n  left: auto;\n  right: 0;\n}\n\n.dropdown-menu-left {\n  left: 0;\n  right: auto;\n}\n\n.dropdown-header {\n  display: block;\n  padding: 3px 20px;\n  font-size: 12px;\n  line-height: 1.428571429;\n  color: #777777;\n  white-space: nowrap;\n}\n\n.dropdown-backdrop {\n  position: fixed;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  top: 0;\n  z-index: 990;\n}\n\n.pull-right > .dropdown-menu {\n  right: 0;\n  left: auto;\n}\n\n.dropup .caret,\n.navbar-fixed-bottom .dropdown .caret {\n  border-top: 0;\n  border-bottom: 4px dashed;\n  border-bottom: 4px solid \\9;\n  content: \"\";\n}\n\n.dropup .dropdown-menu,\n.navbar-fixed-bottom .dropdown .dropdown-menu {\n  top: auto;\n  bottom: 100%;\n  margin-bottom: 2px;\n}\n\n@media (min-width: 768px) {\n  .navbar-right .dropdown-menu {\n    right: 0;\n    left: auto;\n  }\n  .navbar-right .dropdown-menu-left {\n    left: 0;\n    right: auto;\n  }\n}\n\n.btn-group,\n.btn-group-vertical {\n  position: relative;\n  display: inline-block;\n  vertical-align: middle;\n}\n\n.btn-group > .btn,\n.btn-group-vertical > .btn {\n  position: relative;\n  float: left;\n}\n\n.btn-group > .btn:hover, .btn-group > .btn:focus, .btn-group > .btn:active, .btn-group > .btn.active,\n.btn-group-vertical > .btn:hover,\n.btn-group-vertical > .btn:focus,\n.btn-group-vertical > .btn:active,\n.btn-group-vertical > .btn.active {\n  z-index: 2;\n}\n\n.btn-group .btn + .btn,\n.btn-group .btn + .btn-group,\n.btn-group .btn-group + .btn,\n.btn-group .btn-group + .btn-group {\n  margin-left: -1px;\n}\n\n.btn-toolbar {\n  margin-left: -5px;\n}\n\n.btn-toolbar:before, .btn-toolbar:after {\n  content: \" \";\n  display: table;\n}\n\n.btn-toolbar:after {\n  clear: both;\n}\n\n.btn-toolbar .btn,\n.btn-toolbar .btn-group,\n.btn-toolbar .input-group {\n  float: left;\n}\n\n.btn-toolbar > .btn,\n.btn-toolbar > .btn-group,\n.btn-toolbar > .input-group {\n  margin-left: 5px;\n}\n\n.btn-group > .btn:not(:first-child):not(:last-child):not(.dropdown-toggle) {\n  border-radius: 0;\n}\n\n.btn-group > .btn:first-child {\n  margin-left: 0;\n}\n\n.btn-group > .btn:first-child:not(:last-child):not(.dropdown-toggle) {\n  border-bottom-right-radius: 0;\n  border-top-right-radius: 0;\n}\n\n.btn-group > .btn:last-child:not(:first-child),\n.btn-group > .dropdown-toggle:not(:first-child) {\n  border-bottom-left-radius: 0;\n  border-top-left-radius: 0;\n}\n\n.btn-group > .btn-group {\n  float: left;\n}\n\n.btn-group > .btn-group:not(:first-child):not(:last-child) > .btn {\n  border-radius: 0;\n}\n\n.btn-group > .btn-group:first-child:not(:last-child) > .btn:last-child,\n.btn-group > .btn-group:first-child:not(:last-child) > .dropdown-toggle {\n  border-bottom-right-radius: 0;\n  border-top-right-radius: 0;\n}\n\n.btn-group > .btn-group:last-child:not(:first-child) > .btn:first-child {\n  border-bottom-left-radius: 0;\n  border-top-left-radius: 0;\n}\n\n.btn-group .dropdown-toggle:active,\n.btn-group.open .dropdown-toggle {\n  outline: 0;\n}\n\n.btn-group > .btn + .dropdown-toggle {\n  padding-left: 8px;\n  padding-right: 8px;\n}\n\n.btn-group > .btn-lg + .dropdown-toggle, .btn-group-lg.btn-group > .btn + .dropdown-toggle {\n  padding-left: 12px;\n  padding-right: 12px;\n}\n\n.btn-group.open .dropdown-toggle {\n  -webkit-box-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);\n  box-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);\n}\n\n.btn-group.open .dropdown-toggle.btn-link {\n  -webkit-box-shadow: none;\n  box-shadow: none;\n}\n\n.btn .caret {\n  margin-left: 0;\n}\n\n.btn-lg .caret, .btn-group-lg > .btn .caret {\n  border-width: 5px 5px 0;\n  border-bottom-width: 0;\n}\n\n.dropup .btn-lg .caret, .dropup .btn-group-lg > .btn .caret {\n  border-width: 0 5px 5px;\n}\n\n.btn-group-vertical > .btn,\n.btn-group-vertical > .btn-group,\n.btn-group-vertical > .btn-group > .btn {\n  display: block;\n  float: none;\n  width: 100%;\n  max-width: 100%;\n}\n\n.btn-group-vertical > .btn-group:before, .btn-group-vertical > .btn-group:after {\n  content: \" \";\n  display: table;\n}\n\n.btn-group-vertical > .btn-group:after {\n  clear: both;\n}\n\n.btn-group-vertical > .btn-group > .btn {\n  float: none;\n}\n\n.btn-group-vertical > .btn + .btn,\n.btn-group-vertical > .btn + .btn-group,\n.btn-group-vertical > .btn-group + .btn,\n.btn-group-vertical > .btn-group + .btn-group {\n  margin-top: -1px;\n  margin-left: 0;\n}\n\n.btn-group-vertical > .btn:not(:first-child):not(:last-child) {\n  border-radius: 0;\n}\n\n.btn-group-vertical > .btn:first-child:not(:last-child) {\n  border-top-right-radius: 4px;\n  border-top-left-radius: 4px;\n  border-bottom-right-radius: 0;\n  border-bottom-left-radius: 0;\n}\n\n.btn-group-vertical > .btn:last-child:not(:first-child) {\n  border-top-right-radius: 0;\n  border-top-left-radius: 0;\n  border-bottom-right-radius: 4px;\n  border-bottom-left-radius: 4px;\n}\n\n.btn-group-vertical > .btn-group:not(:first-child):not(:last-child) > .btn {\n  border-radius: 0;\n}\n\n.btn-group-vertical > .btn-group:first-child:not(:last-child) > .btn:last-child,\n.btn-group-vertical > .btn-group:first-child:not(:last-child) > .dropdown-toggle {\n  border-bottom-right-radius: 0;\n  border-bottom-left-radius: 0;\n}\n\n.btn-group-vertical > .btn-group:last-child:not(:first-child) > .btn:first-child {\n  border-top-right-radius: 0;\n  border-top-left-radius: 0;\n}\n\n.btn-group-justified {\n  display: table;\n  width: 100%;\n  table-layout: fixed;\n  border-collapse: separate;\n}\n\n.btn-group-justified > .btn,\n.btn-group-justified > .btn-group {\n  float: none;\n  display: table-cell;\n  width: 1%;\n}\n\n.btn-group-justified > .btn-group .btn {\n  width: 100%;\n}\n\n.btn-group-justified > .btn-group .dropdown-menu {\n  left: auto;\n}\n\n[data-toggle=\"buttons\"] > .btn input[type=\"radio\"],\n[data-toggle=\"buttons\"] > .btn input[type=\"checkbox\"],\n[data-toggle=\"buttons\"] > .btn-group > .btn input[type=\"radio\"],\n[data-toggle=\"buttons\"] > .btn-group > .btn input[type=\"checkbox\"] {\n  position: absolute;\n  clip: rect(0, 0, 0, 0);\n  pointer-events: none;\n}\n\n.input-group {\n  position: relative;\n  display: table;\n  border-collapse: separate;\n}\n\n.input-group[class*=\"col-\"] {\n  float: none;\n  padding-left: 0;\n  padding-right: 0;\n}\n\n.input-group .form-control {\n  position: relative;\n  z-index: 2;\n  float: left;\n  width: 100%;\n  margin-bottom: 0;\n}\n\n.input-group .form-control:focus {\n  z-index: 3;\n}\n\n.input-group-addon,\n.input-group-btn,\n.input-group .form-control {\n  display: table-cell;\n}\n\n.input-group-addon:not(:first-child):not(:last-child),\n.input-group-btn:not(:first-child):not(:last-child),\n.input-group .form-control:not(:first-child):not(:last-child) {\n  border-radius: 0;\n}\n\n.input-group-addon,\n.input-group-btn {\n  width: 1%;\n  white-space: nowrap;\n  vertical-align: middle;\n}\n\n.input-group-addon {\n  padding: 6px 12px;\n  font-size: 14px;\n  font-weight: normal;\n  line-height: 1;\n  color: #555555;\n  text-align: center;\n  background-color: #eeeeee;\n  border: 1px solid #ccc;\n  border-radius: 4px;\n}\n\n.input-group-addon.input-sm,\n.input-group-sm > .input-group-addon,\n.input-group-sm > .input-group-btn > .input-group-addon.btn {\n  padding: 5px 10px;\n  font-size: 12px;\n  border-radius: 3px;\n}\n\n.input-group-addon.input-lg,\n.input-group-lg > .input-group-addon,\n.input-group-lg > .input-group-btn > .input-group-addon.btn {\n  padding: 10px 16px;\n  font-size: 18px;\n  border-radius: 6px;\n}\n\n.input-group-addon input[type=\"radio\"],\n.input-group-addon input[type=\"checkbox\"] {\n  margin-top: 0;\n}\n\n.input-group .form-control:first-child,\n.input-group-addon:first-child,\n.input-group-btn:first-child > .btn,\n.input-group-btn:first-child > .btn-group > .btn,\n.input-group-btn:first-child > .dropdown-toggle,\n.input-group-btn:last-child > .btn:not(:last-child):not(.dropdown-toggle),\n.input-group-btn:last-child > .btn-group:not(:last-child) > .btn {\n  border-bottom-right-radius: 0;\n  border-top-right-radius: 0;\n}\n\n.input-group-addon:first-child {\n  border-right: 0;\n}\n\n.input-group .form-control:last-child,\n.input-group-addon:last-child,\n.input-group-btn:last-child > .btn,\n.input-group-btn:last-child > .btn-group > .btn,\n.input-group-btn:last-child > .dropdown-toggle,\n.input-group-btn:first-child > .btn:not(:first-child),\n.input-group-btn:first-child > .btn-group:not(:first-child) > .btn {\n  border-bottom-left-radius: 0;\n  border-top-left-radius: 0;\n}\n\n.input-group-addon:last-child {\n  border-left: 0;\n}\n\n.input-group-btn {\n  position: relative;\n  font-size: 0;\n  white-space: nowrap;\n}\n\n.input-group-btn > .btn {\n  position: relative;\n}\n\n.input-group-btn > .btn + .btn {\n  margin-left: -1px;\n}\n\n.input-group-btn > .btn:hover, .input-group-btn > .btn:focus, .input-group-btn > .btn:active {\n  z-index: 2;\n}\n\n.input-group-btn:first-child > .btn,\n.input-group-btn:first-child > .btn-group {\n  margin-right: -1px;\n}\n\n.input-group-btn:last-child > .btn,\n.input-group-btn:last-child > .btn-group {\n  z-index: 2;\n  margin-left: -1px;\n}\n\n.nav {\n  margin-bottom: 0;\n  padding-left: 0;\n  list-style: none;\n}\n\n.nav:before, .nav:after {\n  content: \" \";\n  display: table;\n}\n\n.nav:after {\n  clear: both;\n}\n\n.nav > li {\n  position: relative;\n  display: block;\n}\n\n.nav > li > a {\n  position: relative;\n  display: block;\n  padding: 10px 15px;\n}\n\n.nav > li > a:hover, .nav > li > a:focus {\n  text-decoration: none;\n  background-color: #eeeeee;\n}\n\n.nav > li.disabled > a {\n  color: #777777;\n}\n\n.nav > li.disabled > a:hover, .nav > li.disabled > a:focus {\n  color: #777777;\n  text-decoration: none;\n  background-color: transparent;\n  cursor: not-allowed;\n}\n\n.nav .open > a, .nav .open > a:hover, .nav .open > a:focus {\n  background-color: #eeeeee;\n  border-color: #337ab7;\n}\n\n.nav .nav-divider {\n  height: 1px;\n  margin: 9px 0;\n  overflow: hidden;\n  background-color: #e5e5e5;\n}\n\n.nav > li > a > img {\n  max-width: none;\n}\n\n.nav-tabs {\n  border-bottom: 1px solid #ddd;\n}\n\n.nav-tabs > li {\n  float: left;\n  margin-bottom: -1px;\n}\n\n.nav-tabs > li > a {\n  margin-right: 2px;\n  line-height: 1.428571429;\n  border: 1px solid transparent;\n  border-radius: 4px 4px 0 0;\n}\n\n.nav-tabs > li > a:hover {\n  border-color: #eeeeee #eeeeee #ddd;\n}\n\n.nav-tabs > li.active > a, .nav-tabs > li.active > a:hover, .nav-tabs > li.active > a:focus {\n  color: #555555;\n  background-color: #fff;\n  border: 1px solid #ddd;\n  border-bottom-color: transparent;\n  cursor: default;\n}\n\n.nav-pills > li {\n  float: left;\n}\n\n.nav-pills > li > a {\n  border-radius: 4px;\n}\n\n.nav-pills > li + li {\n  margin-left: 2px;\n}\n\n.nav-pills > li.active > a, .nav-pills > li.active > a:hover, .nav-pills > li.active > a:focus {\n  color: #fff;\n  background-color: #337ab7;\n}\n\n.nav-stacked > li {\n  float: none;\n}\n\n.nav-stacked > li + li {\n  margin-top: 2px;\n  margin-left: 0;\n}\n\n.nav-justified, .nav-tabs.nav-justified {\n  width: 100%;\n}\n\n.nav-justified > li, .nav-tabs.nav-justified > li {\n  float: none;\n}\n\n.nav-justified > li > a, .nav-tabs.nav-justified > li > a {\n  text-align: center;\n  margin-bottom: 5px;\n}\n\n.nav-justified > .dropdown .dropdown-menu {\n  top: auto;\n  left: auto;\n}\n\n@media (min-width: 768px) {\n  .nav-justified > li, .nav-tabs.nav-justified > li {\n    display: table-cell;\n    width: 1%;\n  }\n  .nav-justified > li > a, .nav-tabs.nav-justified > li > a {\n    margin-bottom: 0;\n  }\n}\n\n.nav-tabs-justified, .nav-tabs.nav-justified {\n  border-bottom: 0;\n}\n\n.nav-tabs-justified > li > a, .nav-tabs.nav-justified > li > a {\n  margin-right: 0;\n  border-radius: 4px;\n}\n\n.nav-tabs-justified > .active > a, .nav-tabs.nav-justified > .active > a,\n.nav-tabs-justified > .active > a:hover, .nav-tabs.nav-justified > .active > a:hover,\n.nav-tabs-justified > .active > a:focus, .nav-tabs.nav-justified > .active > a:focus {\n  border: 1px solid #ddd;\n}\n\n@media (min-width: 768px) {\n  .nav-tabs-justified > li > a, .nav-tabs.nav-justified > li > a {\n    border-bottom: 1px solid #ddd;\n    border-radius: 4px 4px 0 0;\n  }\n  .nav-tabs-justified > .active > a, .nav-tabs.nav-justified > .active > a,\n  .nav-tabs-justified > .active > a:hover, .nav-tabs.nav-justified > .active > a:hover,\n  .nav-tabs-justified > .active > a:focus, .nav-tabs.nav-justified > .active > a:focus {\n    border-bottom-color: #fff;\n  }\n}\n\n.tab-content > .tab-pane {\n  display: none;\n}\n\n.tab-content > .active {\n  display: block;\n}\n\n.nav-tabs .dropdown-menu {\n  margin-top: -1px;\n  border-top-right-radius: 0;\n  border-top-left-radius: 0;\n}\n\n.navbar {\n  position: relative;\n  min-height: 50px;\n  margin-bottom: 20px;\n  border: 1px solid transparent;\n}\n\n.navbar:before, .navbar:after {\n  content: \" \";\n  display: table;\n}\n\n.navbar:after {\n  clear: both;\n}\n\n@media (min-width: 768px) {\n  .navbar {\n    border-radius: 4px;\n  }\n}\n\n.navbar-header:before, .navbar-header:after {\n  content: \" \";\n  display: table;\n}\n\n.navbar-header:after {\n  clear: both;\n}\n\n@media (min-width: 768px) {\n  .navbar-header {\n    float: left;\n  }\n}\n\n.navbar-collapse {\n  overflow-x: visible;\n  padding-right: 15px;\n  padding-left: 15px;\n  border-top: 1px solid transparent;\n  box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.1);\n  -webkit-overflow-scrolling: touch;\n}\n\n.navbar-collapse:before, .navbar-collapse:after {\n  content: \" \";\n  display: table;\n}\n\n.navbar-collapse:after {\n  clear: both;\n}\n\n.navbar-collapse.in {\n  overflow-y: auto;\n}\n\n@media (min-width: 768px) {\n  .navbar-collapse {\n    width: auto;\n    border-top: 0;\n    box-shadow: none;\n  }\n  .navbar-collapse.collapse {\n    display: block !important;\n    height: auto !important;\n    padding-bottom: 0;\n    overflow: visible !important;\n  }\n  .navbar-collapse.in {\n    overflow-y: visible;\n  }\n  .navbar-fixed-top .navbar-collapse,\n  .navbar-static-top .navbar-collapse,\n  .navbar-fixed-bottom .navbar-collapse {\n    padding-left: 0;\n    padding-right: 0;\n  }\n}\n\n.navbar-fixed-top .navbar-collapse,\n.navbar-fixed-bottom .navbar-collapse {\n  max-height: 340px;\n}\n\n@media (max-device-width: 480px) and (orientation: landscape) {\n  .navbar-fixed-top .navbar-collapse,\n  .navbar-fixed-bottom .navbar-collapse {\n    max-height: 200px;\n  }\n}\n\n.container > .navbar-header,\n.container > .navbar-collapse,\n.container-fluid > .navbar-header,\n.container-fluid > .navbar-collapse {\n  margin-right: -15px;\n  margin-left: -15px;\n}\n\n@media (min-width: 768px) {\n  .container > .navbar-header,\n  .container > .navbar-collapse,\n  .container-fluid > .navbar-header,\n  .container-fluid > .navbar-collapse {\n    margin-right: 0;\n    margin-left: 0;\n  }\n}\n\n.navbar-static-top {\n  z-index: 1000;\n  border-width: 0 0 1px;\n}\n\n@media (min-width: 768px) {\n  .navbar-static-top {\n    border-radius: 0;\n  }\n}\n\n.navbar-fixed-top,\n.navbar-fixed-bottom {\n  position: fixed;\n  right: 0;\n  left: 0;\n  z-index: 1030;\n}\n\n@media (min-width: 768px) {\n  .navbar-fixed-top,\n  .navbar-fixed-bottom {\n    border-radius: 0;\n  }\n}\n\n.navbar-fixed-top {\n  top: 0;\n  border-width: 0 0 1px;\n}\n\n.navbar-fixed-bottom {\n  bottom: 0;\n  margin-bottom: 0;\n  border-width: 1px 0 0;\n}\n\n.navbar-brand {\n  float: left;\n  padding: 15px 15px;\n  font-size: 18px;\n  line-height: 20px;\n  height: 50px;\n}\n\n.navbar-brand:hover, .navbar-brand:focus {\n  text-decoration: none;\n}\n\n.navbar-brand > img {\n  display: block;\n}\n\n@media (min-width: 768px) {\n  .navbar > .container .navbar-brand,\n  .navbar > .container-fluid .navbar-brand {\n    margin-left: -15px;\n  }\n}\n\n.navbar-toggle {\n  position: relative;\n  float: right;\n  margin-right: 15px;\n  padding: 9px 10px;\n  margin-top: 8px;\n  margin-bottom: 8px;\n  background-color: transparent;\n  background-image: none;\n  border: 1px solid transparent;\n  border-radius: 4px;\n}\n\n.navbar-toggle:focus {\n  outline: 0;\n}\n\n.navbar-toggle .icon-bar {\n  display: block;\n  width: 22px;\n  height: 2px;\n  border-radius: 1px;\n}\n\n.navbar-toggle .icon-bar + .icon-bar {\n  margin-top: 4px;\n}\n\n@media (min-width: 768px) {\n  .navbar-toggle {\n    display: none;\n  }\n}\n\n.navbar-nav {\n  margin: 7.5px -15px;\n}\n\n.navbar-nav > li > a {\n  padding-top: 10px;\n  padding-bottom: 10px;\n  line-height: 20px;\n}\n\n@media (max-width: 767px) {\n  .navbar-nav .open .dropdown-menu {\n    position: static;\n    float: none;\n    width: auto;\n    margin-top: 0;\n    background-color: transparent;\n    border: 0;\n    box-shadow: none;\n  }\n  .navbar-nav .open .dropdown-menu > li > a,\n  .navbar-nav .open .dropdown-menu .dropdown-header {\n    padding: 5px 15px 5px 25px;\n  }\n  .navbar-nav .open .dropdown-menu > li > a {\n    line-height: 20px;\n  }\n  .navbar-nav .open .dropdown-menu > li > a:hover, .navbar-nav .open .dropdown-menu > li > a:focus {\n    background-image: none;\n  }\n}\n\n@media (min-width: 768px) {\n  .navbar-nav {\n    float: left;\n    margin: 0;\n  }\n  .navbar-nav > li {\n    float: left;\n  }\n  .navbar-nav > li > a {\n    padding-top: 15px;\n    padding-bottom: 15px;\n  }\n}\n\n.navbar-form {\n  margin-left: -15px;\n  margin-right: -15px;\n  padding: 10px 15px;\n  border-top: 1px solid transparent;\n  border-bottom: 1px solid transparent;\n  -webkit-box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.1), 0 1px 0 rgba(255, 255, 255, 0.1);\n  box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.1), 0 1px 0 rgba(255, 255, 255, 0.1);\n  margin-top: 8px;\n  margin-bottom: 8px;\n}\n\n@media (min-width: 768px) {\n  .navbar-form .form-group {\n    display: inline-block;\n    margin-bottom: 0;\n    vertical-align: middle;\n  }\n  .navbar-form .form-control {\n    display: inline-block;\n    width: auto;\n    vertical-align: middle;\n  }\n  .navbar-form .form-control-static {\n    display: inline-block;\n  }\n  .navbar-form .input-group {\n    display: inline-table;\n    vertical-align: middle;\n  }\n  .navbar-form .input-group .input-group-addon,\n  .navbar-form .input-group .input-group-btn,\n  .navbar-form .input-group .form-control {\n    width: auto;\n  }\n  .navbar-form .input-group > .form-control {\n    width: 100%;\n  }\n  .navbar-form .control-label {\n    margin-bottom: 0;\n    vertical-align: middle;\n  }\n  .navbar-form .radio,\n  .navbar-form .checkbox {\n    display: inline-block;\n    margin-top: 0;\n    margin-bottom: 0;\n    vertical-align: middle;\n  }\n  .navbar-form .radio label,\n  .navbar-form .checkbox label {\n    padding-left: 0;\n  }\n  .navbar-form .radio input[type=\"radio\"],\n  .navbar-form .checkbox input[type=\"checkbox\"] {\n    position: relative;\n    margin-left: 0;\n  }\n  .navbar-form .has-feedback .form-control-feedback {\n    top: 0;\n  }\n}\n\n@media (max-width: 767px) {\n  .navbar-form .form-group {\n    margin-bottom: 5px;\n  }\n  .navbar-form .form-group:last-child {\n    margin-bottom: 0;\n  }\n}\n\n@media (min-width: 768px) {\n  .navbar-form {\n    width: auto;\n    border: 0;\n    margin-left: 0;\n    margin-right: 0;\n    padding-top: 0;\n    padding-bottom: 0;\n    -webkit-box-shadow: none;\n    box-shadow: none;\n  }\n}\n\n.navbar-nav > li > .dropdown-menu {\n  margin-top: 0;\n  border-top-right-radius: 0;\n  border-top-left-radius: 0;\n}\n\n.navbar-fixed-bottom .navbar-nav > li > .dropdown-menu {\n  margin-bottom: 0;\n  border-top-right-radius: 4px;\n  border-top-left-radius: 4px;\n  border-bottom-right-radius: 0;\n  border-bottom-left-radius: 0;\n}\n\n.navbar-btn {\n  margin-top: 8px;\n  margin-bottom: 8px;\n}\n\n.navbar-btn.btn-sm, .btn-group-sm > .navbar-btn.btn {\n  margin-top: 10px;\n  margin-bottom: 10px;\n}\n\n.navbar-btn.btn-xs, .btn-group-xs > .navbar-btn.btn {\n  margin-top: 14px;\n  margin-bottom: 14px;\n}\n\n.navbar-text {\n  margin-top: 15px;\n  margin-bottom: 15px;\n}\n\n@media (min-width: 768px) {\n  .navbar-text {\n    float: left;\n    margin-left: 15px;\n    margin-right: 15px;\n  }\n}\n\n@media (min-width: 768px) {\n  .navbar-left {\n    float: left !important;\n  }\n  .navbar-right {\n    float: right !important;\n    margin-right: -15px;\n  }\n  .navbar-right ~ .navbar-right {\n    margin-right: 0;\n  }\n}\n\n.navbar-default {\n  background-color: #f8f8f8;\n  border-color: #e7e7e7;\n}\n\n.navbar-default .navbar-brand {\n  color: #777;\n}\n\n.navbar-default .navbar-brand:hover, .navbar-default .navbar-brand:focus {\n  color: #5e5e5e;\n  background-color: transparent;\n}\n\n.navbar-default .navbar-text {\n  color: #777;\n}\n\n.navbar-default .navbar-nav > li > a {\n  color: #777;\n}\n\n.navbar-default .navbar-nav > li > a:hover, .navbar-default .navbar-nav > li > a:focus {\n  color: #333;\n  background-color: transparent;\n}\n\n.navbar-default .navbar-nav > .active > a, .navbar-default .navbar-nav > .active > a:hover, .navbar-default .navbar-nav > .active > a:focus {\n  color: #555;\n  background-color: #e7e7e7;\n}\n\n.navbar-default .navbar-nav > .disabled > a, .navbar-default .navbar-nav > .disabled > a:hover, .navbar-default .navbar-nav > .disabled > a:focus {\n  color: #ccc;\n  background-color: transparent;\n}\n\n.navbar-default .navbar-toggle {\n  border-color: #ddd;\n}\n\n.navbar-default .navbar-toggle:hover, .navbar-default .navbar-toggle:focus {\n  background-color: #ddd;\n}\n\n.navbar-default .navbar-toggle .icon-bar {\n  background-color: #888;\n}\n\n.navbar-default .navbar-collapse,\n.navbar-default .navbar-form {\n  border-color: #e7e7e7;\n}\n\n.navbar-default .navbar-nav > .open > a, .navbar-default .navbar-nav > .open > a:hover, .navbar-default .navbar-nav > .open > a:focus {\n  background-color: #e7e7e7;\n  color: #555;\n}\n\n@media (max-width: 767px) {\n  .navbar-default .navbar-nav .open .dropdown-menu > li > a {\n    color: #777;\n  }\n  .navbar-default .navbar-nav .open .dropdown-menu > li > a:hover, .navbar-default .navbar-nav .open .dropdown-menu > li > a:focus {\n    color: #333;\n    background-color: transparent;\n  }\n  .navbar-default .navbar-nav .open .dropdown-menu > .active > a, .navbar-default .navbar-nav .open .dropdown-menu > .active > a:hover, .navbar-default .navbar-nav .open .dropdown-menu > .active > a:focus {\n    color: #555;\n    background-color: #e7e7e7;\n  }\n  .navbar-default .navbar-nav .open .dropdown-menu > .disabled > a, .navbar-default .navbar-nav .open .dropdown-menu > .disabled > a:hover, .navbar-default .navbar-nav .open .dropdown-menu > .disabled > a:focus {\n    color: #ccc;\n    background-color: transparent;\n  }\n}\n\n.navbar-default .navbar-link {\n  color: #777;\n}\n\n.navbar-default .navbar-link:hover {\n  color: #333;\n}\n\n.navbar-default .btn-link {\n  color: #777;\n}\n\n.navbar-default .btn-link:hover, .navbar-default .btn-link:focus {\n  color: #333;\n}\n\n.navbar-default .btn-link[disabled]:hover, .navbar-default .btn-link[disabled]:focus,\nfieldset[disabled] .navbar-default .btn-link:hover,\nfieldset[disabled] .navbar-default .btn-link:focus {\n  color: #ccc;\n}\n\n.navbar-inverse {\n  background-color: #222;\n  border-color: #090909;\n}\n\n.navbar-inverse .navbar-brand {\n  color: #9d9d9d;\n}\n\n.navbar-inverse .navbar-brand:hover, .navbar-inverse .navbar-brand:focus {\n  color: #fff;\n  background-color: transparent;\n}\n\n.navbar-inverse .navbar-text {\n  color: #9d9d9d;\n}\n\n.navbar-inverse .navbar-nav > li > a {\n  color: #9d9d9d;\n}\n\n.navbar-inverse .navbar-nav > li > a:hover, .navbar-inverse .navbar-nav > li > a:focus {\n  color: #fff;\n  background-color: transparent;\n}\n\n.navbar-inverse .navbar-nav > .active > a, .navbar-inverse .navbar-nav > .active > a:hover, .navbar-inverse .navbar-nav > .active > a:focus {\n  color: #fff;\n  background-color: #090909;\n}\n\n.navbar-inverse .navbar-nav > .disabled > a, .navbar-inverse .navbar-nav > .disabled > a:hover, .navbar-inverse .navbar-nav > .disabled > a:focus {\n  color: #444;\n  background-color: transparent;\n}\n\n.navbar-inverse .navbar-toggle {\n  border-color: #333;\n}\n\n.navbar-inverse .navbar-toggle:hover, .navbar-inverse .navbar-toggle:focus {\n  background-color: #333;\n}\n\n.navbar-inverse .navbar-toggle .icon-bar {\n  background-color: #fff;\n}\n\n.navbar-inverse .navbar-collapse,\n.navbar-inverse .navbar-form {\n  border-color: #101010;\n}\n\n.navbar-inverse .navbar-nav > .open > a, .navbar-inverse .navbar-nav > .open > a:hover, .navbar-inverse .navbar-nav > .open > a:focus {\n  background-color: #090909;\n  color: #fff;\n}\n\n@media (max-width: 767px) {\n  .navbar-inverse .navbar-nav .open .dropdown-menu > .dropdown-header {\n    border-color: #090909;\n  }\n  .navbar-inverse .navbar-nav .open .dropdown-menu .divider {\n    background-color: #090909;\n  }\n  .navbar-inverse .navbar-nav .open .dropdown-menu > li > a {\n    color: #9d9d9d;\n  }\n  .navbar-inverse .navbar-nav .open .dropdown-menu > li > a:hover, .navbar-inverse .navbar-nav .open .dropdown-menu > li > a:focus {\n    color: #fff;\n    background-color: transparent;\n  }\n  .navbar-inverse .navbar-nav .open .dropdown-menu > .active > a, .navbar-inverse .navbar-nav .open .dropdown-menu > .active > a:hover, .navbar-inverse .navbar-nav .open .dropdown-menu > .active > a:focus {\n    color: #fff;\n    background-color: #090909;\n  }\n  .navbar-inverse .navbar-nav .open .dropdown-menu > .disabled > a, .navbar-inverse .navbar-nav .open .dropdown-menu > .disabled > a:hover, .navbar-inverse .navbar-nav .open .dropdown-menu > .disabled > a:focus {\n    color: #444;\n    background-color: transparent;\n  }\n}\n\n.navbar-inverse .navbar-link {\n  color: #9d9d9d;\n}\n\n.navbar-inverse .navbar-link:hover {\n  color: #fff;\n}\n\n.navbar-inverse .btn-link {\n  color: #9d9d9d;\n}\n\n.navbar-inverse .btn-link:hover, .navbar-inverse .btn-link:focus {\n  color: #fff;\n}\n\n.navbar-inverse .btn-link[disabled]:hover, .navbar-inverse .btn-link[disabled]:focus,\nfieldset[disabled] .navbar-inverse .btn-link:hover,\nfieldset[disabled] .navbar-inverse .btn-link:focus {\n  color: #444;\n}\n\n.breadcrumb {\n  padding: 8px 15px;\n  margin-bottom: 20px;\n  list-style: none;\n  background-color: #f5f5f5;\n  border-radius: 4px;\n}\n\n.breadcrumb > li {\n  display: inline-block;\n}\n\n.breadcrumb > li + li:before {\n  content: \"/ \";\n  padding: 0 5px;\n  color: #ccc;\n}\n\n.breadcrumb > .active {\n  color: #777777;\n}\n\n.pagination {\n  display: inline-block;\n  padding-left: 0;\n  margin: 20px 0;\n  border-radius: 4px;\n}\n\n.pagination > li {\n  display: inline;\n}\n\n.pagination > li > a,\n.pagination > li > span {\n  position: relative;\n  float: left;\n  padding: 6px 12px;\n  line-height: 1.428571429;\n  text-decoration: none;\n  color: #337ab7;\n  background-color: #fff;\n  border: 1px solid #ddd;\n  margin-left: -1px;\n}\n\n.pagination > li:first-child > a,\n.pagination > li:first-child > span {\n  margin-left: 0;\n  border-bottom-left-radius: 4px;\n  border-top-left-radius: 4px;\n}\n\n.pagination > li:last-child > a,\n.pagination > li:last-child > span {\n  border-bottom-right-radius: 4px;\n  border-top-right-radius: 4px;\n}\n\n.pagination > li > a:hover, .pagination > li > a:focus,\n.pagination > li > span:hover,\n.pagination > li > span:focus {\n  z-index: 2;\n  color: #23527c;\n  background-color: #eeeeee;\n  border-color: #ddd;\n}\n\n.pagination > .active > a, .pagination > .active > a:hover, .pagination > .active > a:focus,\n.pagination > .active > span,\n.pagination > .active > span:hover,\n.pagination > .active > span:focus {\n  z-index: 3;\n  color: #fff;\n  background-color: #337ab7;\n  border-color: #337ab7;\n  cursor: default;\n}\n\n.pagination > .disabled > span,\n.pagination > .disabled > span:hover,\n.pagination > .disabled > span:focus,\n.pagination > .disabled > a,\n.pagination > .disabled > a:hover,\n.pagination > .disabled > a:focus {\n  color: #777777;\n  background-color: #fff;\n  border-color: #ddd;\n  cursor: not-allowed;\n}\n\n.pagination-lg > li > a,\n.pagination-lg > li > span {\n  padding: 10px 16px;\n  font-size: 18px;\n  line-height: 1.3333333;\n}\n\n.pagination-lg > li:first-child > a,\n.pagination-lg > li:first-child > span {\n  border-bottom-left-radius: 6px;\n  border-top-left-radius: 6px;\n}\n\n.pagination-lg > li:last-child > a,\n.pagination-lg > li:last-child > span {\n  border-bottom-right-radius: 6px;\n  border-top-right-radius: 6px;\n}\n\n.pagination-sm > li > a,\n.pagination-sm > li > span {\n  padding: 5px 10px;\n  font-size: 12px;\n  line-height: 1.5;\n}\n\n.pagination-sm > li:first-child > a,\n.pagination-sm > li:first-child > span {\n  border-bottom-left-radius: 3px;\n  border-top-left-radius: 3px;\n}\n\n.pagination-sm > li:last-child > a,\n.pagination-sm > li:last-child > span {\n  border-bottom-right-radius: 3px;\n  border-top-right-radius: 3px;\n}\n\n.pager {\n  padding-left: 0;\n  margin: 20px 0;\n  list-style: none;\n  text-align: center;\n}\n\n.pager:before, .pager:after {\n  content: \" \";\n  display: table;\n}\n\n.pager:after {\n  clear: both;\n}\n\n.pager li {\n  display: inline;\n}\n\n.pager li > a,\n.pager li > span {\n  display: inline-block;\n  padding: 5px 14px;\n  background-color: #fff;\n  border: 1px solid #ddd;\n  border-radius: 15px;\n}\n\n.pager li > a:hover,\n.pager li > a:focus {\n  text-decoration: none;\n  background-color: #eeeeee;\n}\n\n.pager .next > a,\n.pager .next > span {\n  float: right;\n}\n\n.pager .previous > a,\n.pager .previous > span {\n  float: left;\n}\n\n.pager .disabled > a,\n.pager .disabled > a:hover,\n.pager .disabled > a:focus,\n.pager .disabled > span {\n  color: #777777;\n  background-color: #fff;\n  cursor: not-allowed;\n}\n\n.label {\n  display: inline;\n  padding: .2em .6em .3em;\n  font-size: 75%;\n  font-weight: bold;\n  line-height: 1;\n  color: #fff;\n  text-align: center;\n  white-space: nowrap;\n  vertical-align: baseline;\n  border-radius: .25em;\n}\n\n.label:empty {\n  display: none;\n}\n\n.btn .label {\n  position: relative;\n  top: -1px;\n}\n\na.label:hover, a.label:focus {\n  color: #fff;\n  text-decoration: none;\n  cursor: pointer;\n}\n\n.label-default {\n  background-color: #777777;\n}\n\n.label-default[href]:hover, .label-default[href]:focus {\n  background-color: #5e5e5e;\n}\n\n.label-primary {\n  background-color: #337ab7;\n}\n\n.label-primary[href]:hover, .label-primary[href]:focus {\n  background-color: #286090;\n}\n\n.label-success {\n  background-color: #5cb85c;\n}\n\n.label-success[href]:hover, .label-success[href]:focus {\n  background-color: #449d44;\n}\n\n.label-info {\n  background-color: #5bc0de;\n}\n\n.label-info[href]:hover, .label-info[href]:focus {\n  background-color: #31b0d5;\n}\n\n.label-warning {\n  background-color: #f0ad4e;\n}\n\n.label-warning[href]:hover, .label-warning[href]:focus {\n  background-color: #ec971f;\n}\n\n.label-danger {\n  background-color: #d9534f;\n}\n\n.label-danger[href]:hover, .label-danger[href]:focus {\n  background-color: #c9302c;\n}\n\n.badge {\n  display: inline-block;\n  min-width: 10px;\n  padding: 3px 7px;\n  font-size: 12px;\n  font-weight: bold;\n  color: #fff;\n  line-height: 1;\n  vertical-align: middle;\n  white-space: nowrap;\n  text-align: center;\n  background-color: #777777;\n  border-radius: 10px;\n}\n\n.badge:empty {\n  display: none;\n}\n\n.btn .badge {\n  position: relative;\n  top: -1px;\n}\n\n.btn-xs .badge, .btn-group-xs > .btn .badge,\n.btn-group-xs > .btn .badge {\n  top: 0;\n  padding: 1px 5px;\n}\n\n.list-group-item.active > .badge,\n.nav-pills > .active > a > .badge {\n  color: #337ab7;\n  background-color: #fff;\n}\n\n.list-group-item > .badge {\n  float: right;\n}\n\n.list-group-item > .badge + .badge {\n  margin-right: 5px;\n}\n\n.nav-pills > li > a > .badge {\n  margin-left: 3px;\n}\n\na.badge:hover, a.badge:focus {\n  color: #fff;\n  text-decoration: none;\n  cursor: pointer;\n}\n\n.jumbotron {\n  padding-top: 30px;\n  padding-bottom: 30px;\n  margin-bottom: 30px;\n  color: inherit;\n  background-color: #eeeeee;\n}\n\n.jumbotron h1,\n.jumbotron .h1 {\n  color: inherit;\n}\n\n.jumbotron p {\n  margin-bottom: 15px;\n  font-size: 21px;\n  font-weight: 200;\n}\n\n.jumbotron > hr {\n  border-top-color: #d5d5d5;\n}\n\n.container .jumbotron,\n.container-fluid .jumbotron {\n  border-radius: 6px;\n  padding-left: 15px;\n  padding-right: 15px;\n}\n\n.jumbotron .container {\n  max-width: 100%;\n}\n\n@media screen and (min-width: 768px) {\n  .jumbotron {\n    padding-top: 48px;\n    padding-bottom: 48px;\n  }\n  .container .jumbotron,\n  .container-fluid .jumbotron {\n    padding-left: 60px;\n    padding-right: 60px;\n  }\n  .jumbotron h1,\n  .jumbotron .h1 {\n    font-size: 63px;\n  }\n}\n\n.thumbnail {\n  display: block;\n  padding: 4px;\n  margin-bottom: 20px;\n  line-height: 1.428571429;\n  background-color: #fff;\n  border: 1px solid #ddd;\n  border-radius: 4px;\n  -webkit-transition: border 0.2s ease-in-out;\n  -o-transition: border 0.2s ease-in-out;\n  transition: border 0.2s ease-in-out;\n}\n\n.thumbnail > img,\n.thumbnail a > img {\n  display: block;\n  max-width: 100%;\n  height: auto;\n  margin-left: auto;\n  margin-right: auto;\n}\n\n.thumbnail .caption {\n  padding: 9px;\n  color: #333333;\n}\n\na.thumbnail:hover,\na.thumbnail:focus,\na.thumbnail.active {\n  border-color: #337ab7;\n}\n\n.alert {\n  padding: 15px;\n  margin-bottom: 20px;\n  border: 1px solid transparent;\n  border-radius: 4px;\n}\n\n.alert h4 {\n  margin-top: 0;\n  color: inherit;\n}\n\n.alert .alert-link {\n  font-weight: bold;\n}\n\n.alert > p,\n.alert > ul {\n  margin-bottom: 0;\n}\n\n.alert > p + p {\n  margin-top: 5px;\n}\n\n.alert-dismissable,\n.alert-dismissible {\n  padding-right: 35px;\n}\n\n.alert-dismissable .close,\n.alert-dismissible .close {\n  position: relative;\n  top: -2px;\n  right: -21px;\n  color: inherit;\n}\n\n.alert-success {\n  background-color: #dff0d8;\n  border-color: #d6e9c6;\n  color: #3c763d;\n}\n\n.alert-success hr {\n  border-top-color: #c9e2b3;\n}\n\n.alert-success .alert-link {\n  color: #2b542c;\n}\n\n.alert-info {\n  background-color: #d9edf7;\n  border-color: #bce8f1;\n  color: #31708f;\n}\n\n.alert-info hr {\n  border-top-color: #a6e1ec;\n}\n\n.alert-info .alert-link {\n  color: #245269;\n}\n\n.alert-warning {\n  background-color: #fcf8e3;\n  border-color: #faebcc;\n  color: #8a6d3b;\n}\n\n.alert-warning hr {\n  border-top-color: #f7e1b5;\n}\n\n.alert-warning .alert-link {\n  color: #66512c;\n}\n\n.alert-danger {\n  background-color: #f2dede;\n  border-color: #ebccd1;\n  color: #a94442;\n}\n\n.alert-danger hr {\n  border-top-color: #e4b9c0;\n}\n\n.alert-danger .alert-link {\n  color: #843534;\n}\n\n@-webkit-keyframes progress-bar-stripes {\n  from {\n    background-position: 40px 0;\n  }\n  to {\n    background-position: 0 0;\n  }\n}\n\n@keyframes progress-bar-stripes {\n  from {\n    background-position: 40px 0;\n  }\n  to {\n    background-position: 0 0;\n  }\n}\n\n.progress {\n  overflow: hidden;\n  height: 20px;\n  margin-bottom: 20px;\n  background-color: #f5f5f5;\n  border-radius: 4px;\n  -webkit-box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.1);\n  box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.1);\n}\n\n.progress-bar {\n  float: left;\n  width: 0%;\n  height: 100%;\n  font-size: 12px;\n  line-height: 20px;\n  color: #fff;\n  text-align: center;\n  background-color: #337ab7;\n  -webkit-box-shadow: inset 0 -1px 0 rgba(0, 0, 0, 0.15);\n  box-shadow: inset 0 -1px 0 rgba(0, 0, 0, 0.15);\n  -webkit-transition: width 0.6s ease;\n  -o-transition: width 0.6s ease;\n  transition: width 0.6s ease;\n}\n\n.progress-striped .progress-bar,\n.progress-bar-striped {\n  background-image: -webkit-linear-gradient(45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);\n  background-image: -o-linear-gradient(45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);\n  background-image: linear-gradient(45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);\n  background-size: 40px 40px;\n}\n\n.progress.active .progress-bar,\n.progress-bar.active {\n  -webkit-animation: progress-bar-stripes 2s linear infinite;\n  -o-animation: progress-bar-stripes 2s linear infinite;\n  animation: progress-bar-stripes 2s linear infinite;\n}\n\n.progress-bar-success {\n  background-color: #5cb85c;\n}\n\n.progress-striped .progress-bar-success {\n  background-image: -webkit-linear-gradient(45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);\n  background-image: -o-linear-gradient(45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);\n  background-image: linear-gradient(45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);\n}\n\n.progress-bar-info {\n  background-color: #5bc0de;\n}\n\n.progress-striped .progress-bar-info {\n  background-image: -webkit-linear-gradient(45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);\n  background-image: -o-linear-gradient(45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);\n  background-image: linear-gradient(45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);\n}\n\n.progress-bar-warning {\n  background-color: #f0ad4e;\n}\n\n.progress-striped .progress-bar-warning {\n  background-image: -webkit-linear-gradient(45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);\n  background-image: -o-linear-gradient(45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);\n  background-image: linear-gradient(45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);\n}\n\n.progress-bar-danger {\n  background-color: #d9534f;\n}\n\n.progress-striped .progress-bar-danger {\n  background-image: -webkit-linear-gradient(45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);\n  background-image: -o-linear-gradient(45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);\n  background-image: linear-gradient(45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);\n}\n\n.media {\n  margin-top: 15px;\n}\n\n.media:first-child {\n  margin-top: 0;\n}\n\n.media,\n.media-body {\n  zoom: 1;\n  overflow: hidden;\n}\n\n.media-body {\n  width: 10000px;\n}\n\n.media-object {\n  display: block;\n}\n\n.media-object.img-thumbnail {\n  max-width: none;\n}\n\n.media-right,\n.media > .pull-right {\n  padding-left: 10px;\n}\n\n.media-left,\n.media > .pull-left {\n  padding-right: 10px;\n}\n\n.media-left,\n.media-right,\n.media-body {\n  display: table-cell;\n  vertical-align: top;\n}\n\n.media-middle {\n  vertical-align: middle;\n}\n\n.media-bottom {\n  vertical-align: bottom;\n}\n\n.media-heading {\n  margin-top: 0;\n  margin-bottom: 5px;\n}\n\n.media-list {\n  padding-left: 0;\n  list-style: none;\n}\n\n.list-group {\n  margin-bottom: 20px;\n  padding-left: 0;\n}\n\n.list-group-item {\n  position: relative;\n  display: block;\n  padding: 10px 15px;\n  margin-bottom: -1px;\n  background-color: #fff;\n  border: 1px solid #ddd;\n}\n\n.list-group-item:first-child {\n  border-top-right-radius: 4px;\n  border-top-left-radius: 4px;\n}\n\n.list-group-item:last-child {\n  margin-bottom: 0;\n  border-bottom-right-radius: 4px;\n  border-bottom-left-radius: 4px;\n}\n\na.list-group-item,\nbutton.list-group-item {\n  color: #555;\n}\n\na.list-group-item .list-group-item-heading,\nbutton.list-group-item .list-group-item-heading {\n  color: #333;\n}\n\na.list-group-item:hover, a.list-group-item:focus,\nbutton.list-group-item:hover,\nbutton.list-group-item:focus {\n  text-decoration: none;\n  color: #555;\n  background-color: #f5f5f5;\n}\n\nbutton.list-group-item {\n  width: 100%;\n  text-align: left;\n}\n\n.list-group-item.disabled, .list-group-item.disabled:hover, .list-group-item.disabled:focus {\n  background-color: #eeeeee;\n  color: #777777;\n  cursor: not-allowed;\n}\n\n.list-group-item.disabled .list-group-item-heading, .list-group-item.disabled:hover .list-group-item-heading, .list-group-item.disabled:focus .list-group-item-heading {\n  color: inherit;\n}\n\n.list-group-item.disabled .list-group-item-text, .list-group-item.disabled:hover .list-group-item-text, .list-group-item.disabled:focus .list-group-item-text {\n  color: #777777;\n}\n\n.list-group-item.active, .list-group-item.active:hover, .list-group-item.active:focus {\n  z-index: 2;\n  color: #fff;\n  background-color: #337ab7;\n  border-color: #337ab7;\n}\n\n.list-group-item.active .list-group-item-heading,\n.list-group-item.active .list-group-item-heading > small,\n.list-group-item.active .list-group-item-heading > .small, .list-group-item.active:hover .list-group-item-heading,\n.list-group-item.active:hover .list-group-item-heading > small,\n.list-group-item.active:hover .list-group-item-heading > .small, .list-group-item.active:focus .list-group-item-heading,\n.list-group-item.active:focus .list-group-item-heading > small,\n.list-group-item.active:focus .list-group-item-heading > .small {\n  color: inherit;\n}\n\n.list-group-item.active .list-group-item-text, .list-group-item.active:hover .list-group-item-text, .list-group-item.active:focus .list-group-item-text {\n  color: #c7ddef;\n}\n\n.list-group-item-success {\n  color: #3c763d;\n  background-color: #dff0d8;\n}\n\na.list-group-item-success,\nbutton.list-group-item-success {\n  color: #3c763d;\n}\n\na.list-group-item-success .list-group-item-heading,\nbutton.list-group-item-success .list-group-item-heading {\n  color: inherit;\n}\n\na.list-group-item-success:hover, a.list-group-item-success:focus,\nbutton.list-group-item-success:hover,\nbutton.list-group-item-success:focus {\n  color: #3c763d;\n  background-color: #d0e9c6;\n}\n\na.list-group-item-success.active, a.list-group-item-success.active:hover, a.list-group-item-success.active:focus,\nbutton.list-group-item-success.active,\nbutton.list-group-item-success.active:hover,\nbutton.list-group-item-success.active:focus {\n  color: #fff;\n  background-color: #3c763d;\n  border-color: #3c763d;\n}\n\n.list-group-item-info {\n  color: #31708f;\n  background-color: #d9edf7;\n}\n\na.list-group-item-info,\nbutton.list-group-item-info {\n  color: #31708f;\n}\n\na.list-group-item-info .list-group-item-heading,\nbutton.list-group-item-info .list-group-item-heading {\n  color: inherit;\n}\n\na.list-group-item-info:hover, a.list-group-item-info:focus,\nbutton.list-group-item-info:hover,\nbutton.list-group-item-info:focus {\n  color: #31708f;\n  background-color: #c4e3f3;\n}\n\na.list-group-item-info.active, a.list-group-item-info.active:hover, a.list-group-item-info.active:focus,\nbutton.list-group-item-info.active,\nbutton.list-group-item-info.active:hover,\nbutton.list-group-item-info.active:focus {\n  color: #fff;\n  background-color: #31708f;\n  border-color: #31708f;\n}\n\n.list-group-item-warning {\n  color: #8a6d3b;\n  background-color: #fcf8e3;\n}\n\na.list-group-item-warning,\nbutton.list-group-item-warning {\n  color: #8a6d3b;\n}\n\na.list-group-item-warning .list-group-item-heading,\nbutton.list-group-item-warning .list-group-item-heading {\n  color: inherit;\n}\n\na.list-group-item-warning:hover, a.list-group-item-warning:focus,\nbutton.list-group-item-warning:hover,\nbutton.list-group-item-warning:focus {\n  color: #8a6d3b;\n  background-color: #faf2cc;\n}\n\na.list-group-item-warning.active, a.list-group-item-warning.active:hover, a.list-group-item-warning.active:focus,\nbutton.list-group-item-warning.active,\nbutton.list-group-item-warning.active:hover,\nbutton.list-group-item-warning.active:focus {\n  color: #fff;\n  background-color: #8a6d3b;\n  border-color: #8a6d3b;\n}\n\n.list-group-item-danger {\n  color: #a94442;\n  background-color: #f2dede;\n}\n\na.list-group-item-danger,\nbutton.list-group-item-danger {\n  color: #a94442;\n}\n\na.list-group-item-danger .list-group-item-heading,\nbutton.list-group-item-danger .list-group-item-heading {\n  color: inherit;\n}\n\na.list-group-item-danger:hover, a.list-group-item-danger:focus,\nbutton.list-group-item-danger:hover,\nbutton.list-group-item-danger:focus {\n  color: #a94442;\n  background-color: #ebcccc;\n}\n\na.list-group-item-danger.active, a.list-group-item-danger.active:hover, a.list-group-item-danger.active:focus,\nbutton.list-group-item-danger.active,\nbutton.list-group-item-danger.active:hover,\nbutton.list-group-item-danger.active:focus {\n  color: #fff;\n  background-color: #a94442;\n  border-color: #a94442;\n}\n\n.list-group-item-heading {\n  margin-top: 0;\n  margin-bottom: 5px;\n}\n\n.list-group-item-text {\n  margin-bottom: 0;\n  line-height: 1.3;\n}\n\n.panel {\n  margin-bottom: 20px;\n  background-color: #fff;\n  border: 1px solid transparent;\n  border-radius: 4px;\n  -webkit-box-shadow: 0 1px 1px rgba(0, 0, 0, 0.05);\n  box-shadow: 0 1px 1px rgba(0, 0, 0, 0.05);\n}\n\n.panel-body {\n  padding: 15px;\n}\n\n.panel-body:before, .panel-body:after {\n  content: \" \";\n  display: table;\n}\n\n.panel-body:after {\n  clear: both;\n}\n\n.panel-heading {\n  padding: 10px 15px;\n  border-bottom: 1px solid transparent;\n  border-top-right-radius: 3px;\n  border-top-left-radius: 3px;\n}\n\n.panel-heading > .dropdown .dropdown-toggle {\n  color: inherit;\n}\n\n.panel-title {\n  margin-top: 0;\n  margin-bottom: 0;\n  font-size: 16px;\n  color: inherit;\n}\n\n.panel-title > a,\n.panel-title > small,\n.panel-title > .small,\n.panel-title > small > a,\n.panel-title > .small > a {\n  color: inherit;\n}\n\n.panel-footer {\n  padding: 10px 15px;\n  background-color: #f5f5f5;\n  border-top: 1px solid #ddd;\n  border-bottom-right-radius: 3px;\n  border-bottom-left-radius: 3px;\n}\n\n.panel > .list-group,\n.panel > .panel-collapse > .list-group {\n  margin-bottom: 0;\n}\n\n.panel > .list-group .list-group-item,\n.panel > .panel-collapse > .list-group .list-group-item {\n  border-width: 1px 0;\n  border-radius: 0;\n}\n\n.panel > .list-group:first-child .list-group-item:first-child,\n.panel > .panel-collapse > .list-group:first-child .list-group-item:first-child {\n  border-top: 0;\n  border-top-right-radius: 3px;\n  border-top-left-radius: 3px;\n}\n\n.panel > .list-group:last-child .list-group-item:last-child,\n.panel > .panel-collapse > .list-group:last-child .list-group-item:last-child {\n  border-bottom: 0;\n  border-bottom-right-radius: 3px;\n  border-bottom-left-radius: 3px;\n}\n\n.panel > .panel-heading + .panel-collapse > .list-group .list-group-item:first-child {\n  border-top-right-radius: 0;\n  border-top-left-radius: 0;\n}\n\n.panel-heading + .list-group .list-group-item:first-child {\n  border-top-width: 0;\n}\n\n.list-group + .panel-footer {\n  border-top-width: 0;\n}\n\n.panel > .table,\n.panel > .table-responsive > .table,\n.panel > .panel-collapse > .table {\n  margin-bottom: 0;\n}\n\n.panel > .table caption,\n.panel > .table-responsive > .table caption,\n.panel > .panel-collapse > .table caption {\n  padding-left: 15px;\n  padding-right: 15px;\n}\n\n.panel > .table:first-child,\n.panel > .table-responsive:first-child > .table:first-child {\n  border-top-right-radius: 3px;\n  border-top-left-radius: 3px;\n}\n\n.panel > .table:first-child > thead:first-child > tr:first-child,\n.panel > .table:first-child > tbody:first-child > tr:first-child,\n.panel > .table-responsive:first-child > .table:first-child > thead:first-child > tr:first-child,\n.panel > .table-responsive:first-child > .table:first-child > tbody:first-child > tr:first-child {\n  border-top-left-radius: 3px;\n  border-top-right-radius: 3px;\n}\n\n.panel > .table:first-child > thead:first-child > tr:first-child td:first-child,\n.panel > .table:first-child > thead:first-child > tr:first-child th:first-child,\n.panel > .table:first-child > tbody:first-child > tr:first-child td:first-child,\n.panel > .table:first-child > tbody:first-child > tr:first-child th:first-child,\n.panel > .table-responsive:first-child > .table:first-child > thead:first-child > tr:first-child td:first-child,\n.panel > .table-responsive:first-child > .table:first-child > thead:first-child > tr:first-child th:first-child,\n.panel > .table-responsive:first-child > .table:first-child > tbody:first-child > tr:first-child td:first-child,\n.panel > .table-responsive:first-child > .table:first-child > tbody:first-child > tr:first-child th:first-child {\n  border-top-left-radius: 3px;\n}\n\n.panel > .table:first-child > thead:first-child > tr:first-child td:last-child,\n.panel > .table:first-child > thead:first-child > tr:first-child th:last-child,\n.panel > .table:first-child > tbody:first-child > tr:first-child td:last-child,\n.panel > .table:first-child > tbody:first-child > tr:first-child th:last-child,\n.panel > .table-responsive:first-child > .table:first-child > thead:first-child > tr:first-child td:last-child,\n.panel > .table-responsive:first-child > .table:first-child > thead:first-child > tr:first-child th:last-child,\n.panel > .table-responsive:first-child > .table:first-child > tbody:first-child > tr:first-child td:last-child,\n.panel > .table-responsive:first-child > .table:first-child > tbody:first-child > tr:first-child th:last-child {\n  border-top-right-radius: 3px;\n}\n\n.panel > .table:last-child,\n.panel > .table-responsive:last-child > .table:last-child {\n  border-bottom-right-radius: 3px;\n  border-bottom-left-radius: 3px;\n}\n\n.panel > .table:last-child > tbody:last-child > tr:last-child,\n.panel > .table:last-child > tfoot:last-child > tr:last-child,\n.panel > .table-responsive:last-child > .table:last-child > tbody:last-child > tr:last-child,\n.panel > .table-responsive:last-child > .table:last-child > tfoot:last-child > tr:last-child {\n  border-bottom-left-radius: 3px;\n  border-bottom-right-radius: 3px;\n}\n\n.panel > .table:last-child > tbody:last-child > tr:last-child td:first-child,\n.panel > .table:last-child > tbody:last-child > tr:last-child th:first-child,\n.panel > .table:last-child > tfoot:last-child > tr:last-child td:first-child,\n.panel > .table:last-child > tfoot:last-child > tr:last-child th:first-child,\n.panel > .table-responsive:last-child > .table:last-child > tbody:last-child > tr:last-child td:first-child,\n.panel > .table-responsive:last-child > .table:last-child > tbody:last-child > tr:last-child th:first-child,\n.panel > .table-responsive:last-child > .table:last-child > tfoot:last-child > tr:last-child td:first-child,\n.panel > .table-responsive:last-child > .table:last-child > tfoot:last-child > tr:last-child th:first-child {\n  border-bottom-left-radius: 3px;\n}\n\n.panel > .table:last-child > tbody:last-child > tr:last-child td:last-child,\n.panel > .table:last-child > tbody:last-child > tr:last-child th:last-child,\n.panel > .table:last-child > tfoot:last-child > tr:last-child td:last-child,\n.panel > .table:last-child > tfoot:last-child > tr:last-child th:last-child,\n.panel > .table-responsive:last-child > .table:last-child > tbody:last-child > tr:last-child td:last-child,\n.panel > .table-responsive:last-child > .table:last-child > tbody:last-child > tr:last-child th:last-child,\n.panel > .table-responsive:last-child > .table:last-child > tfoot:last-child > tr:last-child td:last-child,\n.panel > .table-responsive:last-child > .table:last-child > tfoot:last-child > tr:last-child th:last-child {\n  border-bottom-right-radius: 3px;\n}\n\n.panel > .panel-body + .table,\n.panel > .panel-body + .table-responsive,\n.panel > .table + .panel-body,\n.panel > .table-responsive + .panel-body {\n  border-top: 1px solid #ddd;\n}\n\n.panel > .table > tbody:first-child > tr:first-child th,\n.panel > .table > tbody:first-child > tr:first-child td {\n  border-top: 0;\n}\n\n.panel > .table-bordered,\n.panel > .table-responsive > .table-bordered {\n  border: 0;\n}\n\n.panel > .table-bordered > thead > tr > th:first-child,\n.panel > .table-bordered > thead > tr > td:first-child,\n.panel > .table-bordered > tbody > tr > th:first-child,\n.panel > .table-bordered > tbody > tr > td:first-child,\n.panel > .table-bordered > tfoot > tr > th:first-child,\n.panel > .table-bordered > tfoot > tr > td:first-child,\n.panel > .table-responsive > .table-bordered > thead > tr > th:first-child,\n.panel > .table-responsive > .table-bordered > thead > tr > td:first-child,\n.panel > .table-responsive > .table-bordered > tbody > tr > th:first-child,\n.panel > .table-responsive > .table-bordered > tbody > tr > td:first-child,\n.panel > .table-responsive > .table-bordered > tfoot > tr > th:first-child,\n.panel > .table-responsive > .table-bordered > tfoot > tr > td:first-child {\n  border-left: 0;\n}\n\n.panel > .table-bordered > thead > tr > th:last-child,\n.panel > .table-bordered > thead > tr > td:last-child,\n.panel > .table-bordered > tbody > tr > th:last-child,\n.panel > .table-bordered > tbody > tr > td:last-child,\n.panel > .table-bordered > tfoot > tr > th:last-child,\n.panel > .table-bordered > tfoot > tr > td:last-child,\n.panel > .table-responsive > .table-bordered > thead > tr > th:last-child,\n.panel > .table-responsive > .table-bordered > thead > tr > td:last-child,\n.panel > .table-responsive > .table-bordered > tbody > tr > th:last-child,\n.panel > .table-responsive > .table-bordered > tbody > tr > td:last-child,\n.panel > .table-responsive > .table-bordered > tfoot > tr > th:last-child,\n.panel > .table-responsive > .table-bordered > tfoot > tr > td:last-child {\n  border-right: 0;\n}\n\n.panel > .table-bordered > thead > tr:first-child > td,\n.panel > .table-bordered > thead > tr:first-child > th,\n.panel > .table-bordered > tbody > tr:first-child > td,\n.panel > .table-bordered > tbody > tr:first-child > th,\n.panel > .table-responsive > .table-bordered > thead > tr:first-child > td,\n.panel > .table-responsive > .table-bordered > thead > tr:first-child > th,\n.panel > .table-responsive > .table-bordered > tbody > tr:first-child > td,\n.panel > .table-responsive > .table-bordered > tbody > tr:first-child > th {\n  border-bottom: 0;\n}\n\n.panel > .table-bordered > tbody > tr:last-child > td,\n.panel > .table-bordered > tbody > tr:last-child > th,\n.panel > .table-bordered > tfoot > tr:last-child > td,\n.panel > .table-bordered > tfoot > tr:last-child > th,\n.panel > .table-responsive > .table-bordered > tbody > tr:last-child > td,\n.panel > .table-responsive > .table-bordered > tbody > tr:last-child > th,\n.panel > .table-responsive > .table-bordered > tfoot > tr:last-child > td,\n.panel > .table-responsive > .table-bordered > tfoot > tr:last-child > th {\n  border-bottom: 0;\n}\n\n.panel > .table-responsive {\n  border: 0;\n  margin-bottom: 0;\n}\n\n.panel-group {\n  margin-bottom: 20px;\n}\n\n.panel-group .panel {\n  margin-bottom: 0;\n  border-radius: 4px;\n}\n\n.panel-group .panel + .panel {\n  margin-top: 5px;\n}\n\n.panel-group .panel-heading {\n  border-bottom: 0;\n}\n\n.panel-group .panel-heading + .panel-collapse > .panel-body,\n.panel-group .panel-heading + .panel-collapse > .list-group {\n  border-top: 1px solid #ddd;\n}\n\n.panel-group .panel-footer {\n  border-top: 0;\n}\n\n.panel-group .panel-footer + .panel-collapse .panel-body {\n  border-bottom: 1px solid #ddd;\n}\n\n.panel-default {\n  border-color: #ddd;\n}\n\n.panel-default > .panel-heading {\n  color: #333333;\n  background-color: #f5f5f5;\n  border-color: #ddd;\n}\n\n.panel-default > .panel-heading + .panel-collapse > .panel-body {\n  border-top-color: #ddd;\n}\n\n.panel-default > .panel-heading .badge {\n  color: #f5f5f5;\n  background-color: #333333;\n}\n\n.panel-default > .panel-footer + .panel-collapse > .panel-body {\n  border-bottom-color: #ddd;\n}\n\n.panel-primary {\n  border-color: #337ab7;\n}\n\n.panel-primary > .panel-heading {\n  color: #fff;\n  background-color: #337ab7;\n  border-color: #337ab7;\n}\n\n.panel-primary > .panel-heading + .panel-collapse > .panel-body {\n  border-top-color: #337ab7;\n}\n\n.panel-primary > .panel-heading .badge {\n  color: #337ab7;\n  background-color: #fff;\n}\n\n.panel-primary > .panel-footer + .panel-collapse > .panel-body {\n  border-bottom-color: #337ab7;\n}\n\n.panel-success {\n  border-color: #d6e9c6;\n}\n\n.panel-success > .panel-heading {\n  color: #3c763d;\n  background-color: #dff0d8;\n  border-color: #d6e9c6;\n}\n\n.panel-success > .panel-heading + .panel-collapse > .panel-body {\n  border-top-color: #d6e9c6;\n}\n\n.panel-success > .panel-heading .badge {\n  color: #dff0d8;\n  background-color: #3c763d;\n}\n\n.panel-success > .panel-footer + .panel-collapse > .panel-body {\n  border-bottom-color: #d6e9c6;\n}\n\n.panel-info {\n  border-color: #bce8f1;\n}\n\n.panel-info > .panel-heading {\n  color: #31708f;\n  background-color: #d9edf7;\n  border-color: #bce8f1;\n}\n\n.panel-info > .panel-heading + .panel-collapse > .panel-body {\n  border-top-color: #bce8f1;\n}\n\n.panel-info > .panel-heading .badge {\n  color: #d9edf7;\n  background-color: #31708f;\n}\n\n.panel-info > .panel-footer + .panel-collapse > .panel-body {\n  border-bottom-color: #bce8f1;\n}\n\n.panel-warning {\n  border-color: #faebcc;\n}\n\n.panel-warning > .panel-heading {\n  color: #8a6d3b;\n  background-color: #fcf8e3;\n  border-color: #faebcc;\n}\n\n.panel-warning > .panel-heading + .panel-collapse > .panel-body {\n  border-top-color: #faebcc;\n}\n\n.panel-warning > .panel-heading .badge {\n  color: #fcf8e3;\n  background-color: #8a6d3b;\n}\n\n.panel-warning > .panel-footer + .panel-collapse > .panel-body {\n  border-bottom-color: #faebcc;\n}\n\n.panel-danger {\n  border-color: #ebccd1;\n}\n\n.panel-danger > .panel-heading {\n  color: #a94442;\n  background-color: #f2dede;\n  border-color: #ebccd1;\n}\n\n.panel-danger > .panel-heading + .panel-collapse > .panel-body {\n  border-top-color: #ebccd1;\n}\n\n.panel-danger > .panel-heading .badge {\n  color: #f2dede;\n  background-color: #a94442;\n}\n\n.panel-danger > .panel-footer + .panel-collapse > .panel-body {\n  border-bottom-color: #ebccd1;\n}\n\n.embed-responsive {\n  position: relative;\n  display: block;\n  height: 0;\n  padding: 0;\n  overflow: hidden;\n}\n\n.embed-responsive .embed-responsive-item,\n.embed-responsive iframe,\n.embed-responsive embed,\n.embed-responsive object,\n.embed-responsive video {\n  position: absolute;\n  top: 0;\n  left: 0;\n  bottom: 0;\n  height: 100%;\n  width: 100%;\n  border: 0;\n}\n\n.embed-responsive-16by9 {\n  padding-bottom: 56.25%;\n}\n\n.embed-responsive-4by3 {\n  padding-bottom: 75%;\n}\n\n.well {\n  min-height: 20px;\n  padding: 19px;\n  margin-bottom: 20px;\n  background-color: #f5f5f5;\n  border: 1px solid #e3e3e3;\n  border-radius: 4px;\n  -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.05);\n  box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.05);\n}\n\n.well blockquote {\n  border-color: #ddd;\n  border-color: rgba(0, 0, 0, 0.15);\n}\n\n.well-lg {\n  padding: 24px;\n  border-radius: 6px;\n}\n\n.well-sm {\n  padding: 9px;\n  border-radius: 3px;\n}\n\n.close {\n  float: right;\n  font-size: 21px;\n  font-weight: bold;\n  line-height: 1;\n  color: #000;\n  text-shadow: 0 1px 0 #fff;\n  opacity: 0.2;\n  filter: alpha(opacity=20);\n}\n\n.close:hover, .close:focus {\n  color: #000;\n  text-decoration: none;\n  cursor: pointer;\n  opacity: 0.5;\n  filter: alpha(opacity=50);\n}\n\nbutton.close {\n  padding: 0;\n  cursor: pointer;\n  background: transparent;\n  border: 0;\n  -webkit-appearance: none;\n}\n\n.modal-open {\n  overflow: hidden;\n}\n\n.modal {\n  display: none;\n  overflow: hidden;\n  position: fixed;\n  top: 0;\n  right: 0;\n  bottom: 0;\n  left: 0;\n  z-index: 1050;\n  -webkit-overflow-scrolling: touch;\n  outline: 0;\n}\n\n.modal.fade .modal-dialog {\n  -webkit-transform: translate(0, -25%);\n  -ms-transform: translate(0, -25%);\n  -o-transform: translate(0, -25%);\n  transform: translate(0, -25%);\n  -webkit-transition: -webkit-transform 0.3s ease-out;\n  -moz-transition: -moz-transform 0.3s ease-out;\n  -o-transition: -o-transform 0.3s ease-out;\n  transition: transform 0.3s ease-out;\n}\n\n.modal.in .modal-dialog {\n  -webkit-transform: translate(0, 0);\n  -ms-transform: translate(0, 0);\n  -o-transform: translate(0, 0);\n  transform: translate(0, 0);\n}\n\n.modal-open .modal {\n  overflow-x: hidden;\n  overflow-y: auto;\n}\n\n.modal-dialog {\n  position: relative;\n  width: auto;\n  margin: 10px;\n}\n\n.modal-content {\n  position: relative;\n  background-color: #fff;\n  border: 1px solid #999;\n  border: 1px solid rgba(0, 0, 0, 0.2);\n  border-radius: 6px;\n  -webkit-box-shadow: 0 3px 9px rgba(0, 0, 0, 0.5);\n  box-shadow: 0 3px 9px rgba(0, 0, 0, 0.5);\n  background-clip: padding-box;\n  outline: 0;\n}\n\n.modal-backdrop {\n  position: fixed;\n  top: 0;\n  right: 0;\n  bottom: 0;\n  left: 0;\n  z-index: 1040;\n  background-color: #000;\n}\n\n.modal-backdrop.fade {\n  opacity: 0;\n  filter: alpha(opacity=0);\n}\n\n.modal-backdrop.in {\n  opacity: 0.5;\n  filter: alpha(opacity=50);\n}\n\n.modal-header {\n  padding: 15px;\n  border-bottom: 1px solid #e5e5e5;\n}\n\n.modal-header:before, .modal-header:after {\n  content: \" \";\n  display: table;\n}\n\n.modal-header:after {\n  clear: both;\n}\n\n.modal-header .close {\n  margin-top: -2px;\n}\n\n.modal-title {\n  margin: 0;\n  line-height: 1.428571429;\n}\n\n.modal-body {\n  position: relative;\n  padding: 15px;\n}\n\n.modal-footer {\n  padding: 15px;\n  text-align: right;\n  border-top: 1px solid #e5e5e5;\n}\n\n.modal-footer:before, .modal-footer:after {\n  content: \" \";\n  display: table;\n}\n\n.modal-footer:after {\n  clear: both;\n}\n\n.modal-footer .btn + .btn {\n  margin-left: 5px;\n  margin-bottom: 0;\n}\n\n.modal-footer .btn-group .btn + .btn {\n  margin-left: -1px;\n}\n\n.modal-footer .btn-block + .btn-block {\n  margin-left: 0;\n}\n\n.modal-scrollbar-measure {\n  position: absolute;\n  top: -9999px;\n  width: 50px;\n  height: 50px;\n  overflow: scroll;\n}\n\n@media (min-width: 768px) {\n  .modal-dialog {\n    width: 600px;\n    margin: 30px auto;\n  }\n  .modal-content {\n    -webkit-box-shadow: 0 5px 15px rgba(0, 0, 0, 0.5);\n    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.5);\n  }\n  .modal-sm {\n    width: 300px;\n  }\n}\n\n@media (min-width: 992px) {\n  .modal-lg {\n    width: 900px;\n  }\n}\n\n.tooltip {\n  position: absolute;\n  z-index: 1070;\n  display: block;\n  font-family: \"Helvetica Neue\", Helvetica, Arial, sans-serif;\n  font-style: normal;\n  font-weight: normal;\n  letter-spacing: normal;\n  line-break: auto;\n  line-height: 1.428571429;\n  text-align: left;\n  text-align: start;\n  text-decoration: none;\n  text-shadow: none;\n  text-transform: none;\n  white-space: normal;\n  word-break: normal;\n  word-spacing: normal;\n  word-wrap: normal;\n  font-size: 12px;\n  opacity: 0;\n  filter: alpha(opacity=0);\n}\n\n.tooltip.in {\n  opacity: 0.9;\n  filter: alpha(opacity=90);\n}\n\n.tooltip.top {\n  margin-top: -3px;\n  padding: 5px 0;\n}\n\n.tooltip.right {\n  margin-left: 3px;\n  padding: 0 5px;\n}\n\n.tooltip.bottom {\n  margin-top: 3px;\n  padding: 5px 0;\n}\n\n.tooltip.left {\n  margin-left: -3px;\n  padding: 0 5px;\n}\n\n.tooltip-inner {\n  max-width: 200px;\n  padding: 3px 8px;\n  color: #fff;\n  text-align: center;\n  background-color: #000;\n  border-radius: 4px;\n}\n\n.tooltip-arrow {\n  position: absolute;\n  width: 0;\n  height: 0;\n  border-color: transparent;\n  border-style: solid;\n}\n\n.tooltip.top .tooltip-arrow {\n  bottom: 0;\n  left: 50%;\n  margin-left: -5px;\n  border-width: 5px 5px 0;\n  border-top-color: #000;\n}\n\n.tooltip.top-left .tooltip-arrow {\n  bottom: 0;\n  right: 5px;\n  margin-bottom: -5px;\n  border-width: 5px 5px 0;\n  border-top-color: #000;\n}\n\n.tooltip.top-right .tooltip-arrow {\n  bottom: 0;\n  left: 5px;\n  margin-bottom: -5px;\n  border-width: 5px 5px 0;\n  border-top-color: #000;\n}\n\n.tooltip.right .tooltip-arrow {\n  top: 50%;\n  left: 0;\n  margin-top: -5px;\n  border-width: 5px 5px 5px 0;\n  border-right-color: #000;\n}\n\n.tooltip.left .tooltip-arrow {\n  top: 50%;\n  right: 0;\n  margin-top: -5px;\n  border-width: 5px 0 5px 5px;\n  border-left-color: #000;\n}\n\n.tooltip.bottom .tooltip-arrow {\n  top: 0;\n  left: 50%;\n  margin-left: -5px;\n  border-width: 0 5px 5px;\n  border-bottom-color: #000;\n}\n\n.tooltip.bottom-left .tooltip-arrow {\n  top: 0;\n  right: 5px;\n  margin-top: -5px;\n  border-width: 0 5px 5px;\n  border-bottom-color: #000;\n}\n\n.tooltip.bottom-right .tooltip-arrow {\n  top: 0;\n  left: 5px;\n  margin-top: -5px;\n  border-width: 0 5px 5px;\n  border-bottom-color: #000;\n}\n\n.popover {\n  position: absolute;\n  top: 0;\n  left: 0;\n  z-index: 1060;\n  display: none;\n  max-width: 276px;\n  padding: 1px;\n  font-family: \"Helvetica Neue\", Helvetica, Arial, sans-serif;\n  font-style: normal;\n  font-weight: normal;\n  letter-spacing: normal;\n  line-break: auto;\n  line-height: 1.428571429;\n  text-align: left;\n  text-align: start;\n  text-decoration: none;\n  text-shadow: none;\n  text-transform: none;\n  white-space: normal;\n  word-break: normal;\n  word-spacing: normal;\n  word-wrap: normal;\n  font-size: 14px;\n  background-color: #fff;\n  background-clip: padding-box;\n  border: 1px solid #ccc;\n  border: 1px solid rgba(0, 0, 0, 0.2);\n  border-radius: 6px;\n  -webkit-box-shadow: 0 5px 10px rgba(0, 0, 0, 0.2);\n  box-shadow: 0 5px 10px rgba(0, 0, 0, 0.2);\n}\n\n.popover.top {\n  margin-top: -10px;\n}\n\n.popover.right {\n  margin-left: 10px;\n}\n\n.popover.bottom {\n  margin-top: 10px;\n}\n\n.popover.left {\n  margin-left: -10px;\n}\n\n.popover-title {\n  margin: 0;\n  padding: 8px 14px;\n  font-size: 14px;\n  background-color: #f7f7f7;\n  border-bottom: 1px solid #ebebeb;\n  border-radius: 5px 5px 0 0;\n}\n\n.popover-content {\n  padding: 9px 14px;\n}\n\n.popover > .arrow, .popover > .arrow:after {\n  position: absolute;\n  display: block;\n  width: 0;\n  height: 0;\n  border-color: transparent;\n  border-style: solid;\n}\n\n.popover > .arrow {\n  border-width: 11px;\n}\n\n.popover > .arrow:after {\n  border-width: 10px;\n  content: \"\";\n}\n\n.popover.top > .arrow {\n  left: 50%;\n  margin-left: -11px;\n  border-bottom-width: 0;\n  border-top-color: #999999;\n  border-top-color: rgba(0, 0, 0, 0.25);\n  bottom: -11px;\n}\n\n.popover.top > .arrow:after {\n  content: \" \";\n  bottom: 1px;\n  margin-left: -10px;\n  border-bottom-width: 0;\n  border-top-color: #fff;\n}\n\n.popover.right > .arrow {\n  top: 50%;\n  left: -11px;\n  margin-top: -11px;\n  border-left-width: 0;\n  border-right-color: #999999;\n  border-right-color: rgba(0, 0, 0, 0.25);\n}\n\n.popover.right > .arrow:after {\n  content: \" \";\n  left: 1px;\n  bottom: -10px;\n  border-left-width: 0;\n  border-right-color: #fff;\n}\n\n.popover.bottom > .arrow {\n  left: 50%;\n  margin-left: -11px;\n  border-top-width: 0;\n  border-bottom-color: #999999;\n  border-bottom-color: rgba(0, 0, 0, 0.25);\n  top: -11px;\n}\n\n.popover.bottom > .arrow:after {\n  content: \" \";\n  top: 1px;\n  margin-left: -10px;\n  border-top-width: 0;\n  border-bottom-color: #fff;\n}\n\n.popover.left > .arrow {\n  top: 50%;\n  right: -11px;\n  margin-top: -11px;\n  border-right-width: 0;\n  border-left-color: #999999;\n  border-left-color: rgba(0, 0, 0, 0.25);\n}\n\n.popover.left > .arrow:after {\n  content: \" \";\n  right: 1px;\n  border-right-width: 0;\n  border-left-color: #fff;\n  bottom: -10px;\n}\n\n.carousel {\n  position: relative;\n}\n\n.carousel-inner {\n  position: relative;\n  overflow: hidden;\n  width: 100%;\n}\n\n.carousel-inner > .item {\n  display: none;\n  position: relative;\n  -webkit-transition: 0.6s ease-in-out left;\n  -o-transition: 0.6s ease-in-out left;\n  transition: 0.6s ease-in-out left;\n}\n\n.carousel-inner > .item > img,\n.carousel-inner > .item > a > img {\n  display: block;\n  max-width: 100%;\n  height: auto;\n  line-height: 1;\n}\n\n@media all and (transform-3d), (-webkit-transform-3d) {\n  .carousel-inner > .item {\n    -webkit-transition: -webkit-transform 0.6s ease-in-out;\n    -moz-transition: -moz-transform 0.6s ease-in-out;\n    -o-transition: -o-transform 0.6s ease-in-out;\n    transition: transform 0.6s ease-in-out;\n    -webkit-backface-visibility: hidden;\n    -moz-backface-visibility: hidden;\n    backface-visibility: hidden;\n    -webkit-perspective: 1000px;\n    -moz-perspective: 1000px;\n    perspective: 1000px;\n  }\n  .carousel-inner > .item.next, .carousel-inner > .item.active.right {\n    -webkit-transform: translate3d(100%, 0, 0);\n    transform: translate3d(100%, 0, 0);\n    left: 0;\n  }\n  .carousel-inner > .item.prev, .carousel-inner > .item.active.left {\n    -webkit-transform: translate3d(-100%, 0, 0);\n    transform: translate3d(-100%, 0, 0);\n    left: 0;\n  }\n  .carousel-inner > .item.next.left, .carousel-inner > .item.prev.right, .carousel-inner > .item.active {\n    -webkit-transform: translate3d(0, 0, 0);\n    transform: translate3d(0, 0, 0);\n    left: 0;\n  }\n}\n\n.carousel-inner > .active,\n.carousel-inner > .next,\n.carousel-inner > .prev {\n  display: block;\n}\n\n.carousel-inner > .active {\n  left: 0;\n}\n\n.carousel-inner > .next,\n.carousel-inner > .prev {\n  position: absolute;\n  top: 0;\n  width: 100%;\n}\n\n.carousel-inner > .next {\n  left: 100%;\n}\n\n.carousel-inner > .prev {\n  left: -100%;\n}\n\n.carousel-inner > .next.left,\n.carousel-inner > .prev.right {\n  left: 0;\n}\n\n.carousel-inner > .active.left {\n  left: -100%;\n}\n\n.carousel-inner > .active.right {\n  left: 100%;\n}\n\n.carousel-control {\n  position: absolute;\n  top: 0;\n  left: 0;\n  bottom: 0;\n  width: 15%;\n  opacity: 0.5;\n  filter: alpha(opacity=50);\n  font-size: 20px;\n  color: #fff;\n  text-align: center;\n  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.6);\n  background-color: transparent;\n}\n\n.carousel-control.left {\n  background-image: -webkit-linear-gradient(left, rgba(0, 0, 0, 0.5) 0%, rgba(0, 0, 0, 0.0001) 100%);\n  background-image: -o-linear-gradient(left, rgba(0, 0, 0, 0.5) 0%, rgba(0, 0, 0, 0.0001) 100%);\n  background-image: linear-gradient(to right, rgba(0, 0, 0, 0.5) 0%, rgba(0, 0, 0, 0.0001) 100%);\n  background-repeat: repeat-x;\n  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#80000000', endColorstr='#00000000', GradientType=1);\n}\n\n.carousel-control.right {\n  left: auto;\n  right: 0;\n  background-image: -webkit-linear-gradient(left, rgba(0, 0, 0, 0.0001) 0%, rgba(0, 0, 0, 0.5) 100%);\n  background-image: -o-linear-gradient(left, rgba(0, 0, 0, 0.0001) 0%, rgba(0, 0, 0, 0.5) 100%);\n  background-image: linear-gradient(to right, rgba(0, 0, 0, 0.0001) 0%, rgba(0, 0, 0, 0.5) 100%);\n  background-repeat: repeat-x;\n  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#00000000', endColorstr='#80000000', GradientType=1);\n}\n\n.carousel-control:hover, .carousel-control:focus {\n  outline: 0;\n  color: #fff;\n  text-decoration: none;\n  opacity: 0.9;\n  filter: alpha(opacity=90);\n}\n\n.carousel-control .icon-prev,\n.carousel-control .icon-next,\n.carousel-control .glyphicon-chevron-left,\n.carousel-control .glyphicon-chevron-right {\n  position: absolute;\n  top: 50%;\n  margin-top: -10px;\n  z-index: 5;\n  display: inline-block;\n}\n\n.carousel-control .icon-prev,\n.carousel-control .glyphicon-chevron-left {\n  left: 50%;\n  margin-left: -10px;\n}\n\n.carousel-control .icon-next,\n.carousel-control .glyphicon-chevron-right {\n  right: 50%;\n  margin-right: -10px;\n}\n\n.carousel-control .icon-prev,\n.carousel-control .icon-next {\n  width: 20px;\n  height: 20px;\n  line-height: 1;\n  font-family: serif;\n}\n\n.carousel-control .icon-prev:before {\n  content: '\\2039';\n}\n\n.carousel-control .icon-next:before {\n  content: '\\203a';\n}\n\n.carousel-indicators {\n  position: absolute;\n  bottom: 10px;\n  left: 50%;\n  z-index: 15;\n  width: 60%;\n  margin-left: -30%;\n  padding-left: 0;\n  list-style: none;\n  text-align: center;\n}\n\n.carousel-indicators li {\n  display: inline-block;\n  width: 10px;\n  height: 10px;\n  margin: 1px;\n  text-indent: -999px;\n  border: 1px solid #fff;\n  border-radius: 10px;\n  cursor: pointer;\n  background-color: #000 \\9;\n  background-color: transparent;\n}\n\n.carousel-indicators .active {\n  margin: 0;\n  width: 12px;\n  height: 12px;\n  background-color: #fff;\n}\n\n.carousel-caption {\n  position: absolute;\n  left: 15%;\n  right: 15%;\n  bottom: 20px;\n  z-index: 10;\n  padding-top: 20px;\n  padding-bottom: 20px;\n  color: #fff;\n  text-align: center;\n  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.6);\n}\n\n.carousel-caption .btn {\n  text-shadow: none;\n}\n\n@media screen and (min-width: 768px) {\n  .carousel-control .glyphicon-chevron-left,\n  .carousel-control .glyphicon-chevron-right,\n  .carousel-control .icon-prev,\n  .carousel-control .icon-next {\n    width: 30px;\n    height: 30px;\n    margin-top: -10px;\n    font-size: 30px;\n  }\n  .carousel-control .glyphicon-chevron-left,\n  .carousel-control .icon-prev {\n    margin-left: -10px;\n  }\n  .carousel-control .glyphicon-chevron-right,\n  .carousel-control .icon-next {\n    margin-right: -10px;\n  }\n  .carousel-caption {\n    left: 20%;\n    right: 20%;\n    padding-bottom: 30px;\n  }\n  .carousel-indicators {\n    bottom: 20px;\n  }\n}\n\n.clearfix:before, .clearfix:after {\n  content: \" \";\n  display: table;\n}\n\n.clearfix:after {\n  clear: both;\n}\n\n.center-block {\n  display: block;\n  margin-left: auto;\n  margin-right: auto;\n}\n\n.pull-right {\n  float: right !important;\n}\n\n.pull-left {\n  float: left !important;\n}\n\n.hide {\n  display: none !important;\n}\n\n.show {\n  display: block !important;\n}\n\n.invisible {\n  visibility: hidden;\n}\n\n.text-hide {\n  font: 0/0 a;\n  color: transparent;\n  text-shadow: none;\n  background-color: transparent;\n  border: 0;\n}\n\n.hidden {\n  display: none !important;\n}\n\n.affix {\n  position: fixed;\n}\n\n@-ms-viewport {\n  width: device-width;\n}\n\n.visible-xs {\n  display: none !important;\n}\n\n.visible-sm {\n  display: none !important;\n}\n\n.visible-md {\n  display: none !important;\n}\n\n.visible-lg {\n  display: none !important;\n}\n\n.visible-xs-block,\n.visible-xs-inline,\n.visible-xs-inline-block,\n.visible-sm-block,\n.visible-sm-inline,\n.visible-sm-inline-block,\n.visible-md-block,\n.visible-md-inline,\n.visible-md-inline-block,\n.visible-lg-block,\n.visible-lg-inline,\n.visible-lg-inline-block {\n  display: none !important;\n}\n\n@media (max-width: 767px) {\n  .visible-xs {\n    display: block !important;\n  }\n  table.visible-xs {\n    display: table !important;\n  }\n  tr.visible-xs {\n    display: table-row !important;\n  }\n  th.visible-xs,\n  td.visible-xs {\n    display: table-cell !important;\n  }\n}\n\n@media (max-width: 767px) {\n  .visible-xs-block {\n    display: block !important;\n  }\n}\n\n@media (max-width: 767px) {\n  .visible-xs-inline {\n    display: inline !important;\n  }\n}\n\n@media (max-width: 767px) {\n  .visible-xs-inline-block {\n    display: inline-block !important;\n  }\n}\n\n@media (min-width: 768px) and (max-width: 991px) {\n  .visible-sm {\n    display: block !important;\n  }\n  table.visible-sm {\n    display: table !important;\n  }\n  tr.visible-sm {\n    display: table-row !important;\n  }\n  th.visible-sm,\n  td.visible-sm {\n    display: table-cell !important;\n  }\n}\n\n@media (min-width: 768px) and (max-width: 991px) {\n  .visible-sm-block {\n    display: block !important;\n  }\n}\n\n@media (min-width: 768px) and (max-width: 991px) {\n  .visible-sm-inline {\n    display: inline !important;\n  }\n}\n\n@media (min-width: 768px) and (max-width: 991px) {\n  .visible-sm-inline-block {\n    display: inline-block !important;\n  }\n}\n\n@media (min-width: 992px) and (max-width: 1199px) {\n  .visible-md {\n    display: block !important;\n  }\n  table.visible-md {\n    display: table !important;\n  }\n  tr.visible-md {\n    display: table-row !important;\n  }\n  th.visible-md,\n  td.visible-md {\n    display: table-cell !important;\n  }\n}\n\n@media (min-width: 992px) and (max-width: 1199px) {\n  .visible-md-block {\n    display: block !important;\n  }\n}\n\n@media (min-width: 992px) and (max-width: 1199px) {\n  .visible-md-inline {\n    display: inline !important;\n  }\n}\n\n@media (min-width: 992px) and (max-width: 1199px) {\n  .visible-md-inline-block {\n    display: inline-block !important;\n  }\n}\n\n@media (min-width: 1200px) {\n  .visible-lg {\n    display: block !important;\n  }\n  table.visible-lg {\n    display: table !important;\n  }\n  tr.visible-lg {\n    display: table-row !important;\n  }\n  th.visible-lg,\n  td.visible-lg {\n    display: table-cell !important;\n  }\n}\n\n@media (min-width: 1200px) {\n  .visible-lg-block {\n    display: block !important;\n  }\n}\n\n@media (min-width: 1200px) {\n  .visible-lg-inline {\n    display: inline !important;\n  }\n}\n\n@media (min-width: 1200px) {\n  .visible-lg-inline-block {\n    display: inline-block !important;\n  }\n}\n\n@media (max-width: 767px) {\n  .hidden-xs {\n    display: none !important;\n  }\n}\n\n@media (min-width: 768px) and (max-width: 991px) {\n  .hidden-sm {\n    display: none !important;\n  }\n}\n\n@media (min-width: 992px) and (max-width: 1199px) {\n  .hidden-md {\n    display: none !important;\n  }\n}\n\n@media (min-width: 1200px) {\n  .hidden-lg {\n    display: none !important;\n  }\n}\n\n.visible-print {\n  display: none !important;\n}\n\n@media print {\n  .visible-print {\n    display: block !important;\n  }\n  table.visible-print {\n    display: table !important;\n  }\n  tr.visible-print {\n    display: table-row !important;\n  }\n  th.visible-print,\n  td.visible-print {\n    display: table-cell !important;\n  }\n}\n\n.visible-print-block {\n  display: none !important;\n}\n\n@media print {\n  .visible-print-block {\n    display: block !important;\n  }\n}\n\n.visible-print-inline {\n  display: none !important;\n}\n\n@media print {\n  .visible-print-inline {\n    display: inline !important;\n  }\n}\n\n.visible-print-inline-block {\n  display: none !important;\n}\n\n@media print {\n  .visible-print-inline-block {\n    display: inline-block !important;\n  }\n}\n\n@media print {\n  .hidden-print {\n    display: none !important;\n  }\n}\n\nhtml, body {\n  background-color: #fff;\n  color: #636b6f;\n  font-family: 'Raleway', sans-serif;\n  font-weight: 300;\n  height: 100vh;\n  margin: 0;\n}\n\na {\n  color: #e66531;\n}\n\na:hover {\n  text-decoration: none;\n}\n\n.edit-table td {\n  width: 15%;\n}\n\n.mr-3 {\n  margin-right: 3px;\n}\n\n.mb-3 {\n  margin-bottom: 3px;\n}\n\n.mt-10 {\n  margin-top: 10px;\n}\n\n.mb-10 {\n  margin-bottom: 10px;\n}\n\nh1 {\n  font-size: 2em;\n  margin-bottom: 0.4em;\n}\n\n.navbar-brand {\n  padding: 0px;\n  height: 85px;\n}\n\n.navbar-brand img {\n  width: 200px;\n  padding: 5px 0;\n}\n\n.btn-primary {\n  background: #e66531;\n  border-color: #e66531;\n}\n\n.burndown-title {\n  font-size: 2em;\n  padding-top: 42px;\n  text-align: center;\n}\n\n.label {\n  padding: 6px 12px;\n  font-size: 14px;\n}\n\n.label.label-committed {\n  background: #636b6f;\n}\n\n.label.label-done {\n  background: #636b6f;\n}\n\n.label.label-added {\n  background: #636b6f;\n}\n", "/*! minireset.css v0.0.2 | MIT License | github.com/jgthms/minireset.css */\nhtml,\nbody,\np,\nol,\nul,\nli,\ndl,\ndt,\ndd,\nblockquote,\nfigure,\nfieldset,\nlegend,\ntextarea,\npre,\niframe,\nhr,\nh1,\nh2,\nh3,\nh4,\nh5,\nh6 {\n  margin: 0;\n  padding: 0;\n}\n\nh1,\nh2,\nh3,\nh4,\nh5,\nh6 {\n  font-size: 100%;\n  font-weight: normal;\n}\n\nul {\n  list-style: none;\n}\n\nbutton,\ninput,\nselect,\ntextarea {\n  margin: 0;\n}\n\nhtml {\n  box-sizing: border-box;\n}\n\n*, *:before, *:after {\n  box-sizing: inherit;\n}\n\nimg,\nembed,\nobject,\naudio,\nvideo {\n  height: auto;\n  max-width: 100%;\n}\n\niframe {\n  border: 0;\n}\n\ntable {\n  border-collapse: collapse;\n  border-spacing: 0;\n}\n\ntd,\nth {\n  padding: 0;\n  text-align: left;\n}\n", "@import \"minireset\";\n\n// Fonts\n@import url(https://fonts.googleapis.com/css?family=Raleway:200,300,400,600);\n\n// Bootstrap\n@import \"node_modules/bootstrap-sass/assets/stylesheets/bootstrap\";\n\n// Variables\n@import \"variables\";\n\nhtml, body {\n    background-color: #fff;\n    color: #636b6f;\n    font-family: 'Raleway', sans-serif;\n    font-weight: 300;\n    height: 100vh;\n    margin: 0;\n}\n\na {\n    color: $brand-primary;\n\n    &:hover {\n        text-decoration: none;\n    }\n}\n\n.edit-table {\n    td {\n        width: 15%;\n    }\n}\n.mr-3 {\n    margin-right: 3px;\n}\n.mb-3 {\n    margin-bottom: 3px;\n}\n.mt-10 {\n    margin-top: 10px;\n}\n.mb-10 {\n    margin-bottom: 10px;\n}\n\nh1 {\n    font-size: 2em;\n    margin-bottom: 0.4em;\n}\n\n.navbar-brand {\n    padding: 0px;\n    height: 85px;\n\n    img {\n        width:200px;\n        padding: 5px 0;\n    }\n}\n.btn-primary {\n    background: $brand-primary;\n    border-color: $brand-primary;\n}\n.burndown-title {\n    font-size: 2em;\n    padding-top: 42px;\n    text-align: center;\n}\n\n.label {\n    padding: 6px 12px;\n    font-size: 14px;\n\n    &.label-committed {\n        background: $text-color;\n    }\n    &.label-done {\n        background: $text-color;\n    }\n    &.label-added {\n        background: $text-color;\n    }\n}\n\n", "/*!\n * Bootstrap v3.3.7 (http://getbootstrap.com)\n * Copyright 2011-2016 Twitter, Inc.\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/master/LICENSE)\n */\n\n// Core variables and mixins\n@import \"bootstrap/variables\";\n@import \"bootstrap/mixins\";\n\n// Reset and dependencies\n@import \"bootstrap/normalize\";\n@import \"bootstrap/print\";\n@import \"bootstrap/glyphicons\";\n\n// Core CSS\n@import \"bootstrap/scaffolding\";\n@import \"bootstrap/type\";\n@import \"bootstrap/code\";\n@import \"bootstrap/grid\";\n@import \"bootstrap/tables\";\n@import \"bootstrap/forms\";\n@import \"bootstrap/buttons\";\n\n// Components\n@import \"bootstrap/component-animations\";\n@import \"bootstrap/dropdowns\";\n@import \"bootstrap/button-groups\";\n@import \"bootstrap/input-groups\";\n@import \"bootstrap/navs\";\n@import \"bootstrap/navbar\";\n@import \"bootstrap/breadcrumbs\";\n@import \"bootstrap/pagination\";\n@import \"bootstrap/pager\";\n@import \"bootstrap/labels\";\n@import \"bootstrap/badges\";\n@import \"bootstrap/jumbotron\";\n@import \"bootstrap/thumbnails\";\n@import \"bootstrap/alerts\";\n@import \"bootstrap/progress-bars\";\n@import \"bootstrap/media\";\n@import \"bootstrap/list-group\";\n@import \"bootstrap/panels\";\n@import \"bootstrap/responsive-embed\";\n@import \"bootstrap/wells\";\n@import \"bootstrap/close\";\n\n// Components w/ JavaScript\n@import \"bootstrap/modals\";\n@import \"bootstrap/tooltip\";\n@import \"bootstrap/popovers\";\n@import \"bootstrap/carousel\";\n\n// Utility classes\n@import \"bootstrap/utilities\";\n@import \"bootstrap/responsive-utilities\";\n", "/*! normalize.css v3.0.3 | MIT License | github.com/necolas/normalize.css */\n\n//\n// 1. Set default font family to sans-serif.\n// 2. Prevent iOS and IE text size adjust after device orientation change,\n//    without disabling user zoom.\n//\n\nhtml {\n  font-family: sans-serif; // 1\n  -ms-text-size-adjust: 100%; // 2\n  -webkit-text-size-adjust: 100%; // 2\n}\n\n//\n// Remove default margin.\n//\n\nbody {\n  margin: 0;\n}\n\n// HTML5 display definitions\n// ==========================================================================\n\n//\n// Correct `block` display not defined for any HTML5 element in IE 8/9.\n// Correct `block` display not defined for `details` or `summary` in IE 10/11\n// and Firefox.\n// Correct `block` display not defined for `main` in IE 11.\n//\n\narticle,\naside,\ndetails,\nfigcaption,\nfigure,\nfooter,\nheader,\nhgroup,\nmain,\nmenu,\nnav,\nsection,\nsummary {\n  display: block;\n}\n\n//\n// 1. Correct `inline-block` display not defined in IE 8/9.\n// 2. Normalize vertical alignment of `progress` in Chrome, Firefox, and Opera.\n//\n\naudio,\ncanvas,\nprogress,\nvideo {\n  display: inline-block; // 1\n  vertical-align: baseline; // 2\n}\n\n//\n// Prevent modern browsers from displaying `audio` without controls.\n// Remove excess height in iOS 5 devices.\n//\n\naudio:not([controls]) {\n  display: none;\n  height: 0;\n}\n\n//\n// Address `[hidden]` styling not present in IE 8/9/10.\n// Hide the `template` element in IE 8/9/10/11, Safari, and Firefox < 22.\n//\n\n[hidden],\ntemplate {\n  display: none;\n}\n\n// Links\n// ==========================================================================\n\n//\n// Remove the gray background color from active links in IE 10.\n//\n\na {\n  background-color: transparent;\n}\n\n//\n// Improve readability of focused elements when they are also in an\n// active/hover state.\n//\n\na:active,\na:hover {\n  outline: 0;\n}\n\n// Text-level semantics\n// ==========================================================================\n\n//\n// Address styling not present in IE 8/9/10/11, Safari, and Chrome.\n//\n\nabbr[title] {\n  border-bottom: 1px dotted;\n}\n\n//\n// Address style set to `bolder` in Firefox 4+, Safari, and Chrome.\n//\n\nb,\nstrong {\n  font-weight: bold;\n}\n\n//\n// Address styling not present in Safari and Chrome.\n//\n\ndfn {\n  font-style: italic;\n}\n\n//\n// Address variable `h1` font-size and margin within `section` and `article`\n// contexts in Firefox 4+, Safari, and Chrome.\n//\n\nh1 {\n  font-size: 2em;\n  margin: 0.67em 0;\n}\n\n//\n// Address styling not present in IE 8/9.\n//\n\nmark {\n  background: #ff0;\n  color: #000;\n}\n\n//\n// Address inconsistent and variable font size in all browsers.\n//\n\nsmall {\n  font-size: 80%;\n}\n\n//\n// Prevent `sub` and `sup` affecting `line-height` in all browsers.\n//\n\nsub,\nsup {\n  font-size: 75%;\n  line-height: 0;\n  position: relative;\n  vertical-align: baseline;\n}\n\nsup {\n  top: -0.5em;\n}\n\nsub {\n  bottom: -0.25em;\n}\n\n// Embedded content\n// ==========================================================================\n\n//\n// Remove border when inside `a` element in IE 8/9/10.\n//\n\nimg {\n  border: 0;\n}\n\n//\n// Correct overflow not hidden in IE 9/10/11.\n//\n\nsvg:not(:root) {\n  overflow: hidden;\n}\n\n// Grouping content\n// ==========================================================================\n\n//\n// Address margin not present in IE 8/9 and Safari.\n//\n\nfigure {\n  margin: 1em 40px;\n}\n\n//\n// Address differences between Firefox and other browsers.\n//\n\nhr {\n  box-sizing: content-box;\n  height: 0;\n}\n\n//\n// Contain overflow in all browsers.\n//\n\npre {\n  overflow: auto;\n}\n\n//\n// Address odd `em`-unit font size rendering in all browsers.\n//\n\ncode,\nkbd,\npre,\nsamp {\n  font-family: monospace, monospace;\n  font-size: 1em;\n}\n\n// Forms\n// ==========================================================================\n\n//\n// Known limitation: by default, Chrome and Safari on OS X allow very limited\n// styling of `select`, unless a `border` property is set.\n//\n\n//\n// 1. Correct color not being inherited.\n//    Known issue: affects color of disabled elements.\n// 2. Correct font properties not being inherited.\n// 3. Address margins set differently in Firefox 4+, Safari, and Chrome.\n//\n\nbutton,\ninput,\noptgroup,\nselect,\ntextarea {\n  color: inherit; // 1\n  font: inherit; // 2\n  margin: 0; // 3\n}\n\n//\n// Address `overflow` set to `hidden` in IE 8/9/10/11.\n//\n\nbutton {\n  overflow: visible;\n}\n\n//\n// Address inconsistent `text-transform` inheritance for `button` and `select`.\n// All other form control elements do not inherit `text-transform` values.\n// Correct `button` style inheritance in Firefox, IE 8/9/10/11, and Opera.\n// Correct `select` style inheritance in Firefox.\n//\n\nbutton,\nselect {\n  text-transform: none;\n}\n\n//\n// 1. Avoid the WebKit bug in Android 4.0.* where (2) destroys native `audio`\n//    and `video` controls.\n// 2. Correct inability to style clickable `input` types in iOS.\n// 3. Improve usability and consistency of cursor style between image-type\n//    `input` and others.\n//\n\nbutton,\nhtml input[type=\"button\"], // 1\ninput[type=\"reset\"],\ninput[type=\"submit\"] {\n  -webkit-appearance: button; // 2\n  cursor: pointer; // 3\n}\n\n//\n// Re-set default cursor for disabled elements.\n//\n\nbutton[disabled],\nhtml input[disabled] {\n  cursor: default;\n}\n\n//\n// Remove inner padding and border in Firefox 4+.\n//\n\nbutton::-moz-focus-inner,\ninput::-moz-focus-inner {\n  border: 0;\n  padding: 0;\n}\n\n//\n// Address Firefox 4+ setting `line-height` on `input` using `!important` in\n// the UA stylesheet.\n//\n\ninput {\n  line-height: normal;\n}\n\n//\n// It's recommended that you don't attempt to style these elements.\n// Firefox's implementation doesn't respect box-sizing, padding, or width.\n//\n// 1. Address box sizing set to `content-box` in IE 8/9/10.\n// 2. Remove excess padding in IE 8/9/10.\n//\n\ninput[type=\"checkbox\"],\ninput[type=\"radio\"] {\n  box-sizing: border-box; // 1\n  padding: 0; // 2\n}\n\n//\n// Fix the cursor style for Chrome's increment/decrement buttons. For certain\n// `font-size` values of the `input`, it causes the cursor style of the\n// decrement button to change from `default` to `text`.\n//\n\ninput[type=\"number\"]::-webkit-inner-spin-button,\ninput[type=\"number\"]::-webkit-outer-spin-button {\n  height: auto;\n}\n\n//\n// 1. Address `appearance` set to `searchfield` in Safari and Chrome.\n// 2. Address `box-sizing` set to `border-box` in Safari and Chrome.\n//\n\ninput[type=\"search\"] {\n  -webkit-appearance: textfield; // 1\n  box-sizing: content-box; //2\n}\n\n//\n// Remove inner padding and search cancel button in Safari and Chrome on OS X.\n// Safari (but not Chrome) clips the cancel button when the search input has\n// padding (and `textfield` appearance).\n//\n\ninput[type=\"search\"]::-webkit-search-cancel-button,\ninput[type=\"search\"]::-webkit-search-decoration {\n  -webkit-appearance: none;\n}\n\n//\n// Define consistent border, margin, and padding.\n//\n\nfieldset {\n  border: 1px solid #c0c0c0;\n  margin: 0 2px;\n  padding: 0.35em 0.625em 0.75em;\n}\n\n//\n// 1. Correct `color` not being inherited in IE 8/9/10/11.\n// 2. Remove padding so people aren't caught out if they zero out fieldsets.\n//\n\nlegend {\n  border: 0; // 1\n  padding: 0; // 2\n}\n\n//\n// Remove default vertical scrollbar in IE 8/9/10/11.\n//\n\ntextarea {\n  overflow: auto;\n}\n\n//\n// Don't inherit the `font-weight` (applied by a rule above).\n// NOTE: the default cannot safely be changed in Chrome and Safari on OS X.\n//\n\noptgroup {\n  font-weight: bold;\n}\n\n// Tables\n// ==========================================================================\n\n//\n// Remove most spacing between table cells.\n//\n\ntable {\n  border-collapse: collapse;\n  border-spacing: 0;\n}\n\ntd,\nth {\n  padding: 0;\n}\n", "/*! Source: https://github.com/h5bp/html5-boilerplate/blob/master/src/css/main.css */\n\n// ==========================================================================\n// Print styles.\n// Inlined to avoid the additional HTTP request: h5bp.com/r\n// ==========================================================================\n\n@media print {\n    *,\n    *:before,\n    *:after {\n        background: transparent !important;\n        color: #000 !important; // Black prints faster: h5bp.com/s\n        box-shadow: none !important;\n        text-shadow: none !important;\n    }\n\n    a,\n    a:visited {\n        text-decoration: underline;\n    }\n\n    a[href]:after {\n        content: \" (\" attr(href) \")\";\n    }\n\n    abbr[title]:after {\n        content: \" (\" attr(title) \")\";\n    }\n\n    // Don't show links that are fragment identifiers,\n    // or use the `javascript:` pseudo protocol\n    a[href^=\"#\"]:after,\n    a[href^=\"javascript:\"]:after {\n        content: \"\";\n    }\n\n    pre,\n    blockquote {\n        border: 1px solid #999;\n        page-break-inside: avoid;\n    }\n\n    thead {\n        display: table-header-group; // h5bp.com/t\n    }\n\n    tr,\n    img {\n        page-break-inside: avoid;\n    }\n\n    img {\n        max-width: 100% !important;\n    }\n\n    p,\n    h2,\n    h3 {\n        orphans: 3;\n        widows: 3;\n    }\n\n    h2,\n    h3 {\n        page-break-after: avoid;\n    }\n\n    // Bootstrap specific changes start\n\n    // Bootstrap components\n    .navbar {\n        display: none;\n    }\n    .btn,\n    .dropup > .btn {\n        > .caret {\n            border-top-color: #000 !important;\n        }\n    }\n    .label {\n        border: 1px solid #000;\n    }\n\n    .table {\n        border-collapse: collapse !important;\n\n        td,\n        th {\n            background-color: #fff !important;\n        }\n    }\n    .table-bordered {\n        th,\n        td {\n            border: 1px solid #ddd !important;\n        }\n    }\n\n    // Bootstrap specific changes end\n}\n", "//\n// Glyphicons for Bootstrap\n//\n// Since icons are fonts, they can be placed anywhere text is placed and are\n// thus automatically sized to match the surrounding child. To use, create an\n// inline element with the appropriate classes, like so:\n//\n// <a href=\"#\"><span class=\"glyphicon glyphicon-star\"></span> Star</a>\n\n@at-root {\n  // Import the fonts\n  @font-face {\n    font-family: 'Glyphicons Halflings';\n    src: url(if($bootstrap-sass-asset-helper, twbs-font-path('#{$icon-font-path}#{$icon-font-name}.eot'), '#{$icon-font-path}#{$icon-font-name}.eot'));\n    src: url(if($bootstrap-sass-asset-helper, twbs-font-path('#{$icon-font-path}#{$icon-font-name}.eot?#iefix'), '#{$icon-font-path}#{$icon-font-name}.eot?#iefix')) format('embedded-opentype'),\n         url(if($bootstrap-sass-asset-helper, twbs-font-path('#{$icon-font-path}#{$icon-font-name}.woff2'), '#{$icon-font-path}#{$icon-font-name}.woff2')) format('woff2'),\n         url(if($bootstrap-sass-asset-helper, twbs-font-path('#{$icon-font-path}#{$icon-font-name}.woff'), '#{$icon-font-path}#{$icon-font-name}.woff')) format('woff'),\n         url(if($bootstrap-sass-asset-helper, twbs-font-path('#{$icon-font-path}#{$icon-font-name}.ttf'), '#{$icon-font-path}#{$icon-font-name}.ttf')) format('truetype'),\n         url(if($bootstrap-sass-asset-helper, twbs-font-path('#{$icon-font-path}#{$icon-font-name}.svg##{$icon-font-svg-id}'), '#{$icon-font-path}#{$icon-font-name}.svg##{$icon-font-svg-id}')) format('svg');\n  }\n}\n\n// Catchall baseclass\n.glyphicon {\n  position: relative;\n  top: 1px;\n  display: inline-block;\n  font-family: 'Glyphicons Halflings';\n  font-style: normal;\n  font-weight: normal;\n  line-height: 1;\n  -webkit-font-smoothing: antialiased;\n  -moz-osx-font-smoothing: grayscale;\n}\n\n// Individual icons\n.glyphicon-asterisk               { &:before { content: \"\\002a\"; } }\n.glyphicon-plus                   { &:before { content: \"\\002b\"; } }\n.glyphicon-euro,\n.glyphicon-eur                    { &:before { content: \"\\20ac\"; } }\n.glyphicon-minus                  { &:before { content: \"\\2212\"; } }\n.glyphicon-cloud                  { &:before { content: \"\\2601\"; } }\n.glyphicon-envelope               { &:before { content: \"\\2709\"; } }\n.glyphicon-pencil                 { &:before { content: \"\\270f\"; } }\n.glyphicon-glass                  { &:before { content: \"\\e001\"; } }\n.glyphicon-music                  { &:before { content: \"\\e002\"; } }\n.glyphicon-search                 { &:before { content: \"\\e003\"; } }\n.glyphicon-heart                  { &:before { content: \"\\e005\"; } }\n.glyphicon-star                   { &:before { content: \"\\e006\"; } }\n.glyphicon-star-empty             { &:before { content: \"\\e007\"; } }\n.glyphicon-user                   { &:before { content: \"\\e008\"; } }\n.glyphicon-film                   { &:before { content: \"\\e009\"; } }\n.glyphicon-th-large               { &:before { content: \"\\e010\"; } }\n.glyphicon-th                     { &:before { content: \"\\e011\"; } }\n.glyphicon-th-list                { &:before { content: \"\\e012\"; } }\n.glyphicon-ok                     { &:before { content: \"\\e013\"; } }\n.glyphicon-remove                 { &:before { content: \"\\e014\"; } }\n.glyphicon-zoom-in                { &:before { content: \"\\e015\"; } }\n.glyphicon-zoom-out               { &:before { content: \"\\e016\"; } }\n.glyphicon-off                    { &:before { content: \"\\e017\"; } }\n.glyphicon-signal                 { &:before { content: \"\\e018\"; } }\n.glyphicon-cog                    { &:before { content: \"\\e019\"; } }\n.glyphicon-trash                  { &:before { content: \"\\e020\"; } }\n.glyphicon-home                   { &:before { content: \"\\e021\"; } }\n.glyphicon-file                   { &:before { content: \"\\e022\"; } }\n.glyphicon-time                   { &:before { content: \"\\e023\"; } }\n.glyphicon-road                   { &:before { content: \"\\e024\"; } }\n.glyphicon-download-alt           { &:before { content: \"\\e025\"; } }\n.glyphicon-download               { &:before { content: \"\\e026\"; } }\n.glyphicon-upload                 { &:before { content: \"\\e027\"; } }\n.glyphicon-inbox                  { &:before { content: \"\\e028\"; } }\n.glyphicon-play-circle            { &:before { content: \"\\e029\"; } }\n.glyphicon-repeat                 { &:before { content: \"\\e030\"; } }\n.glyphicon-refresh                { &:before { content: \"\\e031\"; } }\n.glyphicon-list-alt               { &:before { content: \"\\e032\"; } }\n.glyphicon-lock                   { &:before { content: \"\\e033\"; } }\n.glyphicon-flag                   { &:before { content: \"\\e034\"; } }\n.glyphicon-headphones             { &:before { content: \"\\e035\"; } }\n.glyphicon-volume-off             { &:before { content: \"\\e036\"; } }\n.glyphicon-volume-down            { &:before { content: \"\\e037\"; } }\n.glyphicon-volume-up              { &:before { content: \"\\e038\"; } }\n.glyphicon-qrcode                 { &:before { content: \"\\e039\"; } }\n.glyphicon-barcode                { &:before { content: \"\\e040\"; } }\n.glyphicon-tag                    { &:before { content: \"\\e041\"; } }\n.glyphicon-tags                   { &:before { content: \"\\e042\"; } }\n.glyphicon-book                   { &:before { content: \"\\e043\"; } }\n.glyphicon-bookmark               { &:before { content: \"\\e044\"; } }\n.glyphicon-print                  { &:before { content: \"\\e045\"; } }\n.glyphicon-camera                 { &:before { content: \"\\e046\"; } }\n.glyphicon-font                   { &:before { content: \"\\e047\"; } }\n.glyphicon-bold                   { &:before { content: \"\\e048\"; } }\n.glyphicon-italic                 { &:before { content: \"\\e049\"; } }\n.glyphicon-text-height            { &:before { content: \"\\e050\"; } }\n.glyphicon-text-width             { &:before { content: \"\\e051\"; } }\n.glyphicon-align-left             { &:before { content: \"\\e052\"; } }\n.glyphicon-align-center           { &:before { content: \"\\e053\"; } }\n.glyphicon-align-right            { &:before { content: \"\\e054\"; } }\n.glyphicon-align-justify          { &:before { content: \"\\e055\"; } }\n.glyphicon-list                   { &:before { content: \"\\e056\"; } }\n.glyphicon-indent-left            { &:before { content: \"\\e057\"; } }\n.glyphicon-indent-right           { &:before { content: \"\\e058\"; } }\n.glyphicon-facetime-video         { &:before { content: \"\\e059\"; } }\n.glyphicon-picture                { &:before { content: \"\\e060\"; } }\n.glyphicon-map-marker             { &:before { content: \"\\e062\"; } }\n.glyphicon-adjust                 { &:before { content: \"\\e063\"; } }\n.glyphicon-tint                   { &:before { content: \"\\e064\"; } }\n.glyphicon-edit                   { &:before { content: \"\\e065\"; } }\n.glyphicon-share                  { &:before { content: \"\\e066\"; } }\n.glyphicon-check                  { &:before { content: \"\\e067\"; } }\n.glyphicon-move                   { &:before { content: \"\\e068\"; } }\n.glyphicon-step-backward          { &:before { content: \"\\e069\"; } }\n.glyphicon-fast-backward          { &:before { content: \"\\e070\"; } }\n.glyphicon-backward               { &:before { content: \"\\e071\"; } }\n.glyphicon-play                   { &:before { content: \"\\e072\"; } }\n.glyphicon-pause                  { &:before { content: \"\\e073\"; } }\n.glyphicon-stop                   { &:before { content: \"\\e074\"; } }\n.glyphicon-forward                { &:before { content: \"\\e075\"; } }\n.glyphicon-fast-forward           { &:before { content: \"\\e076\"; } }\n.glyphicon-step-forward           { &:before { content: \"\\e077\"; } }\n.glyphicon-eject                  { &:before { content: \"\\e078\"; } }\n.glyphicon-chevron-left           { &:before { content: \"\\e079\"; } }\n.glyphicon-chevron-right          { &:before { content: \"\\e080\"; } }\n.glyphicon-plus-sign              { &:before { content: \"\\e081\"; } }\n.glyphicon-minus-sign             { &:before { content: \"\\e082\"; } }\n.glyphicon-remove-sign            { &:before { content: \"\\e083\"; } }\n.glyphicon-ok-sign                { &:before { content: \"\\e084\"; } }\n.glyphicon-question-sign          { &:before { content: \"\\e085\"; } }\n.glyphicon-info-sign              { &:before { content: \"\\e086\"; } }\n.glyphicon-screenshot             { &:before { content: \"\\e087\"; } }\n.glyphicon-remove-circle          { &:before { content: \"\\e088\"; } }\n.glyphicon-ok-circle              { &:before { content: \"\\e089\"; } }\n.glyphicon-ban-circle             { &:before { content: \"\\e090\"; } }\n.glyphicon-arrow-left             { &:before { content: \"\\e091\"; } }\n.glyphicon-arrow-right            { &:before { content: \"\\e092\"; } }\n.glyphicon-arrow-up               { &:before { content: \"\\e093\"; } }\n.glyphicon-arrow-down             { &:before { content: \"\\e094\"; } }\n.glyphicon-share-alt              { &:before { content: \"\\e095\"; } }\n.glyphicon-resize-full            { &:before { content: \"\\e096\"; } }\n.glyphicon-resize-small           { &:before { content: \"\\e097\"; } }\n.glyphicon-exclamation-sign       { &:before { content: \"\\e101\"; } }\n.glyphicon-gift                   { &:before { content: \"\\e102\"; } }\n.glyphicon-leaf                   { &:before { content: \"\\e103\"; } }\n.glyphicon-fire                   { &:before { content: \"\\e104\"; } }\n.glyphicon-eye-open               { &:before { content: \"\\e105\"; } }\n.glyphicon-eye-close              { &:before { content: \"\\e106\"; } }\n.glyphicon-warning-sign           { &:before { content: \"\\e107\"; } }\n.glyphicon-plane                  { &:before { content: \"\\e108\"; } }\n.glyphicon-calendar               { &:before { content: \"\\e109\"; } }\n.glyphicon-random                 { &:before { content: \"\\e110\"; } }\n.glyphicon-comment                { &:before { content: \"\\e111\"; } }\n.glyphicon-magnet                 { &:before { content: \"\\e112\"; } }\n.glyphicon-chevron-up             { &:before { content: \"\\e113\"; } }\n.glyphicon-chevron-down           { &:before { content: \"\\e114\"; } }\n.glyphicon-retweet                { &:before { content: \"\\e115\"; } }\n.glyphicon-shopping-cart          { &:before { content: \"\\e116\"; } }\n.glyphicon-folder-close           { &:before { content: \"\\e117\"; } }\n.glyphicon-folder-open            { &:before { content: \"\\e118\"; } }\n.glyphicon-resize-vertical        { &:before { content: \"\\e119\"; } }\n.glyphicon-resize-horizontal      { &:before { content: \"\\e120\"; } }\n.glyphicon-hdd                    { &:before { content: \"\\e121\"; } }\n.glyphicon-bullhorn               { &:before { content: \"\\e122\"; } }\n.glyphicon-bell                   { &:before { content: \"\\e123\"; } }\n.glyphicon-certificate            { &:before { content: \"\\e124\"; } }\n.glyphicon-thumbs-up              { &:before { content: \"\\e125\"; } }\n.glyphicon-thumbs-down            { &:before { content: \"\\e126\"; } }\n.glyphicon-hand-right             { &:before { content: \"\\e127\"; } }\n.glyphicon-hand-left              { &:before { content: \"\\e128\"; } }\n.glyphicon-hand-up                { &:before { content: \"\\e129\"; } }\n.glyphicon-hand-down              { &:before { content: \"\\e130\"; } }\n.glyphicon-circle-arrow-right     { &:before { content: \"\\e131\"; } }\n.glyphicon-circle-arrow-left      { &:before { content: \"\\e132\"; } }\n.glyphicon-circle-arrow-up        { &:before { content: \"\\e133\"; } }\n.glyphicon-circle-arrow-down      { &:before { content: \"\\e134\"; } }\n.glyphicon-globe                  { &:before { content: \"\\e135\"; } }\n.glyphicon-wrench                 { &:before { content: \"\\e136\"; } }\n.glyphicon-tasks                  { &:before { content: \"\\e137\"; } }\n.glyphicon-filter                 { &:before { content: \"\\e138\"; } }\n.glyphicon-briefcase              { &:before { content: \"\\e139\"; } }\n.glyphicon-fullscreen             { &:before { content: \"\\e140\"; } }\n.glyphicon-dashboard              { &:before { content: \"\\e141\"; } }\n.glyphicon-paperclip              { &:before { content: \"\\e142\"; } }\n.glyphicon-heart-empty            { &:before { content: \"\\e143\"; } }\n.glyphicon-link                   { &:before { content: \"\\e144\"; } }\n.glyphicon-phone                  { &:before { content: \"\\e145\"; } }\n.glyphicon-pushpin                { &:before { content: \"\\e146\"; } }\n.glyphicon-usd                    { &:before { content: \"\\e148\"; } }\n.glyphicon-gbp                    { &:before { content: \"\\e149\"; } }\n.glyphicon-sort                   { &:before { content: \"\\e150\"; } }\n.glyphicon-sort-by-alphabet       { &:before { content: \"\\e151\"; } }\n.glyphicon-sort-by-alphabet-alt   { &:before { content: \"\\e152\"; } }\n.glyphicon-sort-by-order          { &:before { content: \"\\e153\"; } }\n.glyphicon-sort-by-order-alt      { &:before { content: \"\\e154\"; } }\n.glyphicon-sort-by-attributes     { &:before { content: \"\\e155\"; } }\n.glyphicon-sort-by-attributes-alt { &:before { content: \"\\e156\"; } }\n.glyphicon-unchecked              { &:before { content: \"\\e157\"; } }\n.glyphicon-expand                 { &:before { content: \"\\e158\"; } }\n.glyphicon-collapse-down          { &:before { content: \"\\e159\"; } }\n.glyphicon-collapse-up            { &:before { content: \"\\e160\"; } }\n.glyphicon-log-in                 { &:before { content: \"\\e161\"; } }\n.glyphicon-flash                  { &:before { content: \"\\e162\"; } }\n.glyphicon-log-out                { &:before { content: \"\\e163\"; } }\n.glyphicon-new-window             { &:before { content: \"\\e164\"; } }\n.glyphicon-record                 { &:before { content: \"\\e165\"; } }\n.glyphicon-save                   { &:before { content: \"\\e166\"; } }\n.glyphicon-open                   { &:before { content: \"\\e167\"; } }\n.glyphicon-saved                  { &:before { content: \"\\e168\"; } }\n.glyphicon-import                 { &:before { content: \"\\e169\"; } }\n.glyphicon-export                 { &:before { content: \"\\e170\"; } }\n.glyphicon-send                   { &:before { content: \"\\e171\"; } }\n.glyphicon-floppy-disk            { &:before { content: \"\\e172\"; } }\n.glyphicon-floppy-saved           { &:before { content: \"\\e173\"; } }\n.glyphicon-floppy-remove          { &:before { content: \"\\e174\"; } }\n.glyphicon-floppy-save            { &:before { content: \"\\e175\"; } }\n.glyphicon-floppy-open            { &:before { content: \"\\e176\"; } }\n.glyphicon-credit-card            { &:before { content: \"\\e177\"; } }\n.glyphicon-transfer               { &:before { content: \"\\e178\"; } }\n.glyphicon-cutlery                { &:before { content: \"\\e179\"; } }\n.glyphicon-header                 { &:before { content: \"\\e180\"; } }\n.glyphicon-compressed             { &:before { content: \"\\e181\"; } }\n.glyphicon-earphone               { &:before { content: \"\\e182\"; } }\n.glyphicon-phone-alt              { &:before { content: \"\\e183\"; } }\n.glyphicon-tower                  { &:before { content: \"\\e184\"; } }\n.glyphicon-stats                  { &:before { content: \"\\e185\"; } }\n.glyphicon-sd-video               { &:before { content: \"\\e186\"; } }\n.glyphicon-hd-video               { &:before { content: \"\\e187\"; } }\n.glyphicon-subtitles              { &:before { content: \"\\e188\"; } }\n.glyphicon-sound-stereo           { &:before { content: \"\\e189\"; } }\n.glyphicon-sound-dolby            { &:before { content: \"\\e190\"; } }\n.glyphicon-sound-5-1              { &:before { content: \"\\e191\"; } }\n.glyphicon-sound-6-1              { &:before { content: \"\\e192\"; } }\n.glyphicon-sound-7-1              { &:before { content: \"\\e193\"; } }\n.glyphicon-copyright-mark         { &:before { content: \"\\e194\"; } }\n.glyphicon-registration-mark      { &:before { content: \"\\e195\"; } }\n.glyphicon-cloud-download         { &:before { content: \"\\e197\"; } }\n.glyphicon-cloud-upload           { &:before { content: \"\\e198\"; } }\n.glyphicon-tree-conifer           { &:before { content: \"\\e199\"; } }\n.glyphicon-tree-deciduous         { &:before { content: \"\\e200\"; } }\n.glyphicon-cd                     { &:before { content: \"\\e201\"; } }\n.glyphicon-save-file              { &:before { content: \"\\e202\"; } }\n.glyphicon-open-file              { &:before { content: \"\\e203\"; } }\n.glyphicon-level-up               { &:before { content: \"\\e204\"; } }\n.glyphicon-copy                   { &:before { content: \"\\e205\"; } }\n.glyphicon-paste                  { &:before { content: \"\\e206\"; } }\n// The following 2 Glyphicons are omitted for the time being because\n// they currently use Unicode codepoints that are outside the\n// Basic Multilingual Plane (BMP). Older buggy versions of WebKit can't handle\n// non-BMP codepoints in CSS string escapes, and thus can't display these two icons.\n// Notably, the bug affects some older versions of the Android Browser.\n// More info: https://github.com/twbs/bootstrap/issues/10106\n// .glyphicon-door                   { &:before { content: \"\\1f6aa\"; } }\n// .glyphicon-key                    { &:before { content: \"\\1f511\"; } }\n.glyphicon-alert                  { &:before { content: \"\\e209\"; } }\n.glyphicon-equalizer              { &:before { content: \"\\e210\"; } }\n.glyphicon-king                   { &:before { content: \"\\e211\"; } }\n.glyphicon-queen                  { &:before { content: \"\\e212\"; } }\n.glyphicon-pawn                   { &:before { content: \"\\e213\"; } }\n.glyphicon-bishop                 { &:before { content: \"\\e214\"; } }\n.glyphicon-knight                 { &:before { content: \"\\e215\"; } }\n.glyphicon-baby-formula           { &:before { content: \"\\e216\"; } }\n.glyphicon-tent                   { &:before { content: \"\\26fa\"; } }\n.glyphicon-blackboard             { &:before { content: \"\\e218\"; } }\n.glyphicon-bed                    { &:before { content: \"\\e219\"; } }\n.glyphicon-apple                  { &:before { content: \"\\f8ff\"; } }\n.glyphicon-erase                  { &:before { content: \"\\e221\"; } }\n.glyphicon-hourglass              { &:before { content: \"\\231b\"; } }\n.glyphicon-lamp                   { &:before { content: \"\\e223\"; } }\n.glyphicon-duplicate              { &:before { content: \"\\e224\"; } }\n.glyphicon-piggy-bank             { &:before { content: \"\\e225\"; } }\n.glyphicon-scissors               { &:before { content: \"\\e226\"; } }\n.glyphicon-bitcoin                { &:before { content: \"\\e227\"; } }\n.glyphicon-btc                    { &:before { content: \"\\e227\"; } }\n.glyphicon-xbt                    { &:before { content: \"\\e227\"; } }\n.glyphicon-yen                    { &:before { content: \"\\00a5\"; } }\n.glyphicon-jpy                    { &:before { content: \"\\00a5\"; } }\n.glyphicon-ruble                  { &:before { content: \"\\20bd\"; } }\n.glyphicon-rub                    { &:before { content: \"\\20bd\"; } }\n.glyphicon-scale                  { &:before { content: \"\\e230\"; } }\n.glyphicon-ice-lolly              { &:before { content: \"\\e231\"; } }\n.glyphicon-ice-lolly-tasted       { &:before { content: \"\\e232\"; } }\n.glyphicon-education              { &:before { content: \"\\e233\"; } }\n.glyphicon-option-horizontal      { &:before { content: \"\\e234\"; } }\n.glyphicon-option-vertical        { &:before { content: \"\\e235\"; } }\n.glyphicon-menu-hamburger         { &:before { content: \"\\e236\"; } }\n.glyphicon-modal-window           { &:before { content: \"\\e237\"; } }\n.glyphicon-oil                    { &:before { content: \"\\e238\"; } }\n.glyphicon-grain                  { &:before { content: \"\\e239\"; } }\n.glyphicon-sunglasses             { &:before { content: \"\\e240\"; } }\n.glyphicon-text-size              { &:before { content: \"\\e241\"; } }\n.glyphicon-text-color             { &:before { content: \"\\e242\"; } }\n.glyphicon-text-background        { &:before { content: \"\\e243\"; } }\n.glyphicon-object-align-top       { &:before { content: \"\\e244\"; } }\n.glyphicon-object-align-bottom    { &:before { content: \"\\e245\"; } }\n.glyphicon-object-align-horizontal{ &:before { content: \"\\e246\"; } }\n.glyphicon-object-align-left      { &:before { content: \"\\e247\"; } }\n.glyphicon-object-align-vertical  { &:before { content: \"\\e248\"; } }\n.glyphicon-object-align-right     { &:before { content: \"\\e249\"; } }\n.glyphicon-triangle-right         { &:before { content: \"\\e250\"; } }\n.glyphicon-triangle-left          { &:before { content: \"\\e251\"; } }\n.glyphicon-triangle-bottom        { &:before { content: \"\\e252\"; } }\n.glyphicon-triangle-top           { &:before { content: \"\\e253\"; } }\n.glyphicon-console                { &:before { content: \"\\e254\"; } }\n.glyphicon-superscript            { &:before { content: \"\\e255\"; } }\n.glyphicon-subscript              { &:before { content: \"\\e256\"; } }\n.glyphicon-menu-left              { &:before { content: \"\\e257\"; } }\n.glyphicon-menu-right             { &:before { content: \"\\e258\"; } }\n.glyphicon-menu-down              { &:before { content: \"\\e259\"; } }\n.glyphicon-menu-up                { &:before { content: \"\\e260\"; } }\n", "//\n// Scaffolding\n// --------------------------------------------------\n\n\n// Reset the box-sizing\n//\n// Heads up! This reset may cause conflicts with some third-party widgets.\n// For recommendations on resolving such conflicts, see\n// http://getbootstrap.com/getting-started/#third-box-sizing\n* {\n  @include box-sizing(border-box);\n}\n*:before,\n*:after {\n  @include box-sizing(border-box);\n}\n\n\n// Body reset\n\nhtml {\n  font-size: 10px;\n  -webkit-tap-highlight-color: rgba(0,0,0,0);\n}\n\nbody {\n  font-family: $font-family-base;\n  font-size: $font-size-base;\n  line-height: $line-height-base;\n  color: $text-color;\n  background-color: $body-bg;\n}\n\n// Reset fonts for relevant elements\ninput,\nbutton,\nselect,\ntextarea {\n  font-family: inherit;\n  font-size: inherit;\n  line-height: inherit;\n}\n\n\n// Links\n\na {\n  color: $link-color;\n  text-decoration: none;\n\n  &:hover,\n  &:focus {\n    color: $link-hover-color;\n    text-decoration: $link-hover-decoration;\n  }\n\n  &:focus {\n    @include tab-focus;\n  }\n}\n\n\n// Figures\n//\n// We reset this here because previously Normalize had no `figure` margins. This\n// ensures we don't break anyone's use of the element.\n\nfigure {\n  margin: 0;\n}\n\n\n// Images\n\nimg {\n  vertical-align: middle;\n}\n\n// Responsive images (ensure images don't scale beyond their parents)\n.img-responsive {\n  @include img-responsive;\n}\n\n// Rounded corners\n.img-rounded {\n  border-radius: $border-radius-large;\n}\n\n// Image thumbnails\n//\n// Heads up! This is mixin-ed into thumbnails.less for `.thumbnail`.\n.img-thumbnail {\n  padding: $thumbnail-padding;\n  line-height: $line-height-base;\n  background-color: $thumbnail-bg;\n  border: 1px solid $thumbnail-border;\n  border-radius: $thumbnail-border-radius;\n  @include transition(all .2s ease-in-out);\n\n  // Keep them at most 100% wide\n  @include img-responsive(inline-block);\n}\n\n// Perfect circle\n.img-circle {\n  border-radius: 50%; // set radius in percents\n}\n\n\n// Horizontal rules\n\nhr {\n  margin-top:    $line-height-computed;\n  margin-bottom: $line-height-computed;\n  border: 0;\n  border-top: 1px solid $hr-border;\n}\n\n\n// Only display content to screen readers\n//\n// See: http://a11yproject.com/posts/how-to-hide-content\n\n.sr-only {\n  position: absolute;\n  width: 1px;\n  height: 1px;\n  margin: -1px;\n  padding: 0;\n  overflow: hidden;\n  clip: rect(0,0,0,0);\n  border: 0;\n}\n\n// Use in conjunction with .sr-only to only display content when it's focused.\n// Useful for \"Skip to main content\" links; see http://www.w3.org/TR/2013/NOTE-WCAG20-TECHS-20130905/G1\n// Credit: HTML5 Boilerplate\n\n.sr-only-focusable {\n  &:active,\n  &:focus {\n    position: static;\n    width: auto;\n    height: auto;\n    margin: 0;\n    overflow: visible;\n    clip: auto;\n  }\n}\n\n\n// iOS \"clickable elements\" fix for role=\"button\"\n//\n// Fixes \"clickability\" issue (and more generally, the firing of events such as focus as well)\n// for traditionally non-focusable elements with role=\"button\"\n// see https://developer.mozilla.org/en-US/docs/Web/Events/click#Safari_Mobile\n\n[role=\"button\"] {\n  cursor: pointer;\n}\n", "// Vendor Prefixes\n//\n// All vendor mixins are deprecated as of v3.2.0 due to the introduction of\n// Autoprefixer in our Gruntfile. They have been removed in v4.\n\n// - Animations\n// - Backface visibility\n// - Box shadow\n// - Box sizing\n// - Content columns\n// - Hyphens\n// - Placeholder text\n// - Transformations\n// - Transitions\n// - User Select\n\n\n// Animations\n@mixin animation($animation) {\n  -webkit-animation: $animation;\n       -o-animation: $animation;\n          animation: $animation;\n}\n@mixin animation-name($name) {\n  -webkit-animation-name: $name;\n          animation-name: $name;\n}\n@mixin animation-duration($duration) {\n  -webkit-animation-duration: $duration;\n          animation-duration: $duration;\n}\n@mixin animation-timing-function($timing-function) {\n  -webkit-animation-timing-function: $timing-function;\n          animation-timing-function: $timing-function;\n}\n@mixin animation-delay($delay) {\n  -webkit-animation-delay: $delay;\n          animation-delay: $delay;\n}\n@mixin animation-iteration-count($iteration-count) {\n  -webkit-animation-iteration-count: $iteration-count;\n          animation-iteration-count: $iteration-count;\n}\n@mixin animation-direction($direction) {\n  -webkit-animation-direction: $direction;\n          animation-direction: $direction;\n}\n@mixin animation-fill-mode($fill-mode) {\n  -webkit-animation-fill-mode: $fill-mode;\n          animation-fill-mode: $fill-mode;\n}\n\n// Backface visibility\n// Prevent browsers from flickering when using CSS 3D transforms.\n// Default value is `visible`, but can be changed to `hidden`\n\n@mixin backface-visibility($visibility) {\n  -webkit-backface-visibility: $visibility;\n     -moz-backface-visibility: $visibility;\n          backface-visibility: $visibility;\n}\n\n// Drop shadows\n//\n// Note: Deprecated `.box-shadow()` as of v3.1.0 since all of Bootstrap's\n// supported browsers that have box shadow capabilities now support it.\n\n@mixin box-shadow($shadow...) {\n  -webkit-box-shadow: $shadow; // iOS <4.3 & Android <4.1\n          box-shadow: $shadow;\n}\n\n// Box sizing\n@mixin box-sizing($boxmodel) {\n  -webkit-box-sizing: $boxmodel;\n     -moz-box-sizing: $boxmodel;\n          box-sizing: $boxmodel;\n}\n\n// CSS3 Content Columns\n@mixin content-columns($column-count, $column-gap: $grid-gutter-width) {\n  -webkit-column-count: $column-count;\n     -moz-column-count: $column-count;\n          column-count: $column-count;\n  -webkit-column-gap: $column-gap;\n     -moz-column-gap: $column-gap;\n          column-gap: $column-gap;\n}\n\n// Optional hyphenation\n@mixin hyphens($mode: auto) {\n  word-wrap: break-word;\n  -webkit-hyphens: $mode;\n     -moz-hyphens: $mode;\n      -ms-hyphens: $mode; // IE10+\n       -o-hyphens: $mode;\n          hyphens: $mode;\n}\n\n// Placeholder text\n@mixin placeholder($color: $input-color-placeholder) {\n  // Firefox\n  &::-moz-placeholder {\n    color: $color;\n    opacity: 1; // Override Firefox's unusual default opacity; see https://github.com/twbs/bootstrap/pull/11526\n  }\n  &:-ms-input-placeholder { color: $color; } // Internet Explorer 10+\n  &::-webkit-input-placeholder  { color: $color; } // Safari and Chrome\n}\n\n// Transformations\n@mixin scale($ratio...) {\n  -webkit-transform: scale($ratio);\n      -ms-transform: scale($ratio); // IE9 only\n       -o-transform: scale($ratio);\n          transform: scale($ratio);\n}\n\n@mixin scaleX($ratio) {\n  -webkit-transform: scaleX($ratio);\n      -ms-transform: scaleX($ratio); // IE9 only\n       -o-transform: scaleX($ratio);\n          transform: scaleX($ratio);\n}\n@mixin scaleY($ratio) {\n  -webkit-transform: scaleY($ratio);\n      -ms-transform: scaleY($ratio); // IE9 only\n       -o-transform: scaleY($ratio);\n          transform: scaleY($ratio);\n}\n@mixin skew($x, $y) {\n  -webkit-transform: skewX($x) skewY($y);\n      -ms-transform: skewX($x) skewY($y); // See https://github.com/twbs/bootstrap/issues/4885; IE9+\n       -o-transform: skewX($x) skewY($y);\n          transform: skewX($x) skewY($y);\n}\n@mixin translate($x, $y) {\n  -webkit-transform: translate($x, $y);\n      -ms-transform: translate($x, $y); // IE9 only\n       -o-transform: translate($x, $y);\n          transform: translate($x, $y);\n}\n@mixin translate3d($x, $y, $z) {\n  -webkit-transform: translate3d($x, $y, $z);\n          transform: translate3d($x, $y, $z);\n}\n@mixin rotate($degrees) {\n  -webkit-transform: rotate($degrees);\n      -ms-transform: rotate($degrees); // IE9 only\n       -o-transform: rotate($degrees);\n          transform: rotate($degrees);\n}\n@mixin rotateX($degrees) {\n  -webkit-transform: rotateX($degrees);\n      -ms-transform: rotateX($degrees); // IE9 only\n       -o-transform: rotateX($degrees);\n          transform: rotateX($degrees);\n}\n@mixin rotateY($degrees) {\n  -webkit-transform: rotateY($degrees);\n      -ms-transform: rotateY($degrees); // IE9 only\n       -o-transform: rotateY($degrees);\n          transform: rotateY($degrees);\n}\n@mixin perspective($perspective) {\n  -webkit-perspective: $perspective;\n     -moz-perspective: $perspective;\n          perspective: $perspective;\n}\n@mixin perspective-origin($perspective) {\n  -webkit-perspective-origin: $perspective;\n     -moz-perspective-origin: $perspective;\n          perspective-origin: $perspective;\n}\n@mixin transform-origin($origin) {\n  -webkit-transform-origin: $origin;\n     -moz-transform-origin: $origin;\n      -ms-transform-origin: $origin; // IE9 only\n          transform-origin: $origin;\n}\n\n\n// Transitions\n\n@mixin transition($transition...) {\n  -webkit-transition: $transition;\n       -o-transition: $transition;\n          transition: $transition;\n}\n@mixin transition-property($transition-property...) {\n  -webkit-transition-property: $transition-property;\n          transition-property: $transition-property;\n}\n@mixin transition-delay($transition-delay) {\n  -webkit-transition-delay: $transition-delay;\n          transition-delay: $transition-delay;\n}\n@mixin transition-duration($transition-duration...) {\n  -webkit-transition-duration: $transition-duration;\n          transition-duration: $transition-duration;\n}\n@mixin transition-timing-function($timing-function) {\n  -webkit-transition-timing-function: $timing-function;\n          transition-timing-function: $timing-function;\n}\n@mixin transition-transform($transition...) {\n  -webkit-transition: -webkit-transform $transition;\n     -moz-transition: -moz-transform $transition;\n       -o-transition: -o-transform $transition;\n          transition: transform $transition;\n}\n\n\n// User select\n// For selecting text on the page\n\n@mixin user-select($select) {\n  -webkit-user-select: $select;\n     -moz-user-select: $select;\n      -ms-user-select: $select; // IE10+\n          user-select: $select;\n}\n", "$bootstrap-sass-asset-helper: false !default;\n//\n// Variables\n// --------------------------------------------------\n\n\n//== Colors\n//\n//## Gray and brand colors for use across Bootstrap.\n\n$gray-base:              #000 !default;\n$gray-darker:            lighten($gray-base, 13.5%) !default; // #222\n$gray-dark:              lighten($gray-base, 20%) !default;   // #333\n$gray:                   lighten($gray-base, 33.5%) !default; // #555\n$gray-light:             lighten($gray-base, 46.7%) !default; // #777\n$gray-lighter:           lighten($gray-base, 93.5%) !default; // #eee\n\n$brand-primary:         darken(#428bca, 6.5%) !default; // #337ab7\n$brand-success:         #5cb85c !default;\n$brand-info:            #5bc0de !default;\n$brand-warning:         #f0ad4e !default;\n$brand-danger:          #d9534f !default;\n\n\n//== Scaffolding\n//\n//## Settings for some of the most global styles.\n\n//** Background color for `<body>`.\n$body-bg:               #fff !default;\n//** Global text color on `<body>`.\n$text-color:            $gray-dark !default;\n\n//** Global textual link color.\n$link-color:            $brand-primary !default;\n//** Link hover color set via `darken()` function.\n$link-hover-color:      darken($link-color, 15%) !default;\n//** Link hover decoration.\n$link-hover-decoration: underline !default;\n\n\n//== Typography\n//\n//## Font, line-height, and color for body text, headings, and more.\n\n$font-family-sans-serif:  \"Helvetica Neue\", Helvetica, Arial, sans-serif !default;\n$font-family-serif:       Georgia, \"Times New Roman\", Times, serif !default;\n//** Default monospace fonts for `<code>`, `<kbd>`, and `<pre>`.\n$font-family-monospace:   Menlo, Monaco, Consolas, \"Courier New\", monospace !default;\n$font-family-base:        $font-family-sans-serif !default;\n\n$font-size-base:          14px !default;\n$font-size-large:         ceil(($font-size-base * 1.25)) !default; // ~18px\n$font-size-small:         ceil(($font-size-base * 0.85)) !default; // ~12px\n\n$font-size-h1:            floor(($font-size-base * 2.6)) !default; // ~36px\n$font-size-h2:            floor(($font-size-base * 2.15)) !default; // ~30px\n$font-size-h3:            ceil(($font-size-base * 1.7)) !default; // ~24px\n$font-size-h4:            ceil(($font-size-base * 1.25)) !default; // ~18px\n$font-size-h5:            $font-size-base !default;\n$font-size-h6:            ceil(($font-size-base * 0.85)) !default; // ~12px\n\n//** Unit-less `line-height` for use in components like buttons.\n$line-height-base:        1.428571429 !default; // 20/14\n//** Computed \"line-height\" (`font-size` * `line-height`) for use with `margin`, `padding`, etc.\n$line-height-computed:    floor(($font-size-base * $line-height-base)) !default; // ~20px\n\n//** By default, this inherits from the `<body>`.\n$headings-font-family:    inherit !default;\n$headings-font-weight:    500 !default;\n$headings-line-height:    1.1 !default;\n$headings-color:          inherit !default;\n\n\n//== Iconography\n//\n//## Specify custom location and filename of the included Glyphicons icon font. Useful for those including Bootstrap via Bower.\n\n//** Load fonts from this directory.\n\n// [converter] If $bootstrap-sass-asset-helper if used, provide path relative to the assets load path.\n// [converter] This is because some asset helpers, such as Sprockets, do not work with file-relative paths.\n$icon-font-path: if($bootstrap-sass-asset-helper, \"bootstrap/\", \"../fonts/bootstrap/\") !default;\n\n//** File name for all font files.\n$icon-font-name:          \"glyphicons-halflings-regular\" !default;\n//** Element ID within SVG icon file.\n$icon-font-svg-id:        \"glyphicons_halflingsregular\" !default;\n\n\n//== Components\n//\n//## Define common padding and border radius sizes and more. Values based on 14px text and 1.428 line-height (~20px to start).\n\n$padding-base-vertical:     6px !default;\n$padding-base-horizontal:   12px !default;\n\n$padding-large-vertical:    10px !default;\n$padding-large-horizontal:  16px !default;\n\n$padding-small-vertical:    5px !default;\n$padding-small-horizontal:  10px !default;\n\n$padding-xs-vertical:       1px !default;\n$padding-xs-horizontal:     5px !default;\n\n$line-height-large:         1.3333333 !default; // extra decimals for Win 8.1 Chrome\n$line-height-small:         1.5 !default;\n\n$border-radius-base:        4px !default;\n$border-radius-large:       6px !default;\n$border-radius-small:       3px !default;\n\n//** Global color for active items (e.g., navs or dropdowns).\n$component-active-color:    #fff !default;\n//** Global background color for active items (e.g., navs or dropdowns).\n$component-active-bg:       $brand-primary !default;\n\n//** Width of the `border` for generating carets that indicate dropdowns.\n$caret-width-base:          4px !default;\n//** Carets increase slightly in size for larger components.\n$caret-width-large:         5px !default;\n\n\n//== Tables\n//\n//## Customizes the `.table` component with basic values, each used across all table variations.\n\n//** Padding for `<th>`s and `<td>`s.\n$table-cell-padding:            8px !default;\n//** Padding for cells in `.table-condensed`.\n$table-condensed-cell-padding:  5px !default;\n\n//** Default background color used for all tables.\n$table-bg:                      transparent !default;\n//** Background color used for `.table-striped`.\n$table-bg-accent:               #f9f9f9 !default;\n//** Background color used for `.table-hover`.\n$table-bg-hover:                #f5f5f5 !default;\n$table-bg-active:               $table-bg-hover !default;\n\n//** Border color for table and cell borders.\n$table-border-color:            #ddd !default;\n\n\n//== Buttons\n//\n//## For each of Bootstrap's buttons, define text, background and border color.\n\n$btn-font-weight:                normal !default;\n\n$btn-default-color:              #333 !default;\n$btn-default-bg:                 #fff !default;\n$btn-default-border:             #ccc !default;\n\n$btn-primary-color:              #fff !default;\n$btn-primary-bg:                 $brand-primary !default;\n$btn-primary-border:             darken($btn-primary-bg, 5%) !default;\n\n$btn-success-color:              #fff !default;\n$btn-success-bg:                 $brand-success !default;\n$btn-success-border:             darken($btn-success-bg, 5%) !default;\n\n$btn-info-color:                 #fff !default;\n$btn-info-bg:                    $brand-info !default;\n$btn-info-border:                darken($btn-info-bg, 5%) !default;\n\n$btn-warning-color:              #fff !default;\n$btn-warning-bg:                 $brand-warning !default;\n$btn-warning-border:             darken($btn-warning-bg, 5%) !default;\n\n$btn-danger-color:               #fff !default;\n$btn-danger-bg:                  $brand-danger !default;\n$btn-danger-border:              darken($btn-danger-bg, 5%) !default;\n\n$btn-link-disabled-color:        $gray-light !default;\n\n// Allows for customizing button radius independently from global border radius\n$btn-border-radius-base:         $border-radius-base !default;\n$btn-border-radius-large:        $border-radius-large !default;\n$btn-border-radius-small:        $border-radius-small !default;\n\n\n//== Forms\n//\n//##\n\n//** `<input>` background color\n$input-bg:                       #fff !default;\n//** `<input disabled>` background color\n$input-bg-disabled:              $gray-lighter !default;\n\n//** Text color for `<input>`s\n$input-color:                    $gray !default;\n//** `<input>` border color\n$input-border:                   #ccc !default;\n\n// TODO: Rename `$input-border-radius` to `$input-border-radius-base` in v4\n//** Default `.form-control` border radius\n// This has no effect on `<select>`s in some browsers, due to the limited stylability of `<select>`s in CSS.\n$input-border-radius:            $border-radius-base !default;\n//** Large `.form-control` border radius\n$input-border-radius-large:      $border-radius-large !default;\n//** Small `.form-control` border radius\n$input-border-radius-small:      $border-radius-small !default;\n\n//** Border color for inputs on focus\n$input-border-focus:             #66afe9 !default;\n\n//** Placeholder text color\n$input-color-placeholder:        #999 !default;\n\n//** Default `.form-control` height\n$input-height-base:              ($line-height-computed + ($padding-base-vertical * 2) + 2) !default;\n//** Large `.form-control` height\n$input-height-large:             (ceil($font-size-large * $line-height-large) + ($padding-large-vertical * 2) + 2) !default;\n//** Small `.form-control` height\n$input-height-small:             (floor($font-size-small * $line-height-small) + ($padding-small-vertical * 2) + 2) !default;\n\n//** `.form-group` margin\n$form-group-margin-bottom:       15px !default;\n\n$legend-color:                   $gray-dark !default;\n$legend-border-color:            #e5e5e5 !default;\n\n//** Background color for textual input addons\n$input-group-addon-bg:           $gray-lighter !default;\n//** Border color for textual input addons\n$input-group-addon-border-color: $input-border !default;\n\n//** Disabled cursor for form controls and buttons.\n$cursor-disabled:                not-allowed !default;\n\n\n//== Dropdowns\n//\n//## Dropdown menu container and contents.\n\n//** Background for the dropdown menu.\n$dropdown-bg:                    #fff !default;\n//** Dropdown menu `border-color`.\n$dropdown-border:                rgba(0,0,0,.15) !default;\n//** Dropdown menu `border-color` **for IE8**.\n$dropdown-fallback-border:       #ccc !default;\n//** Divider color for between dropdown items.\n$dropdown-divider-bg:            #e5e5e5 !default;\n\n//** Dropdown link text color.\n$dropdown-link-color:            $gray-dark !default;\n//** Hover color for dropdown links.\n$dropdown-link-hover-color:      darken($gray-dark, 5%) !default;\n//** Hover background for dropdown links.\n$dropdown-link-hover-bg:         #f5f5f5 !default;\n\n//** Active dropdown menu item text color.\n$dropdown-link-active-color:     $component-active-color !default;\n//** Active dropdown menu item background color.\n$dropdown-link-active-bg:        $component-active-bg !default;\n\n//** Disabled dropdown menu item background color.\n$dropdown-link-disabled-color:   $gray-light !default;\n\n//** Text color for headers within dropdown menus.\n$dropdown-header-color:          $gray-light !default;\n\n//** Deprecated `$dropdown-caret-color` as of v3.1.0\n$dropdown-caret-color:           #000 !default;\n\n\n//-- Z-index master list\n//\n// Warning: Avoid customizing these values. They're used for a bird's eye view\n// of components dependent on the z-axis and are designed to all work together.\n//\n// Note: These variables are not generated into the Customizer.\n\n$zindex-navbar:            1000 !default;\n$zindex-dropdown:          1000 !default;\n$zindex-popover:           1060 !default;\n$zindex-tooltip:           1070 !default;\n$zindex-navbar-fixed:      1030 !default;\n$zindex-modal-background:  1040 !default;\n$zindex-modal:             1050 !default;\n\n\n//== Media queries breakpoints\n//\n//## Define the breakpoints at which your layout will change, adapting to different screen sizes.\n\n// Extra small screen / phone\n//** Deprecated `$screen-xs` as of v3.0.1\n$screen-xs:                  480px !default;\n//** Deprecated `$screen-xs-min` as of v3.2.0\n$screen-xs-min:              $screen-xs !default;\n//** Deprecated `$screen-phone` as of v3.0.1\n$screen-phone:               $screen-xs-min !default;\n\n// Small screen / tablet\n//** Deprecated `$screen-sm` as of v3.0.1\n$screen-sm:                  768px !default;\n$screen-sm-min:              $screen-sm !default;\n//** Deprecated `$screen-tablet` as of v3.0.1\n$screen-tablet:              $screen-sm-min !default;\n\n// Medium screen / desktop\n//** Deprecated `$screen-md` as of v3.0.1\n$screen-md:                  992px !default;\n$screen-md-min:              $screen-md !default;\n//** Deprecated `$screen-desktop` as of v3.0.1\n$screen-desktop:             $screen-md-min !default;\n\n// Large screen / wide desktop\n//** Deprecated `$screen-lg` as of v3.0.1\n$screen-lg:                  1200px !default;\n$screen-lg-min:              $screen-lg !default;\n//** Deprecated `$screen-lg-desktop` as of v3.0.1\n$screen-lg-desktop:          $screen-lg-min !default;\n\n// So media queries don't overlap when required, provide a maximum\n$screen-xs-max:              ($screen-sm-min - 1) !default;\n$screen-sm-max:              ($screen-md-min - 1) !default;\n$screen-md-max:              ($screen-lg-min - 1) !default;\n\n\n//== Grid system\n//\n//## Define your custom responsive grid.\n\n//** Number of columns in the grid.\n$grid-columns:              12 !default;\n//** Padding between columns. Gets divided in half for the left and right.\n$grid-gutter-width:         30px !default;\n// Navbar collapse\n//** Point at which the navbar becomes uncollapsed.\n$grid-float-breakpoint:     $screen-sm-min !default;\n//** Point at which the navbar begins collapsing.\n$grid-float-breakpoint-max: ($grid-float-breakpoint - 1) !default;\n\n\n//== Container sizes\n//\n//## Define the maximum width of `.container` for different screen sizes.\n\n// Small screen / tablet\n$container-tablet:             (720px + $grid-gutter-width) !default;\n//** For `$screen-sm-min` and up.\n$container-sm:                 $container-tablet !default;\n\n// Medium screen / desktop\n$container-desktop:            (940px + $grid-gutter-width) !default;\n//** For `$screen-md-min` and up.\n$container-md:                 $container-desktop !default;\n\n// Large screen / wide desktop\n$container-large-desktop:      (1140px + $grid-gutter-width) !default;\n//** For `$screen-lg-min` and up.\n$container-lg:                 $container-large-desktop !default;\n\n\n//== Navbar\n//\n//##\n\n// Basics of a navbar\n$navbar-height:                    50px !default;\n$navbar-margin-bottom:             $line-height-computed !default;\n$navbar-border-radius:             $border-radius-base !default;\n$navbar-padding-horizontal:        floor(($grid-gutter-width / 2)) !default;\n$navbar-padding-vertical:          (($navbar-height - $line-height-computed) / 2) !default;\n$navbar-collapse-max-height:       340px !default;\n\n$navbar-default-color:             #777 !default;\n$navbar-default-bg:                #f8f8f8 !default;\n$navbar-default-border:            darken($navbar-default-bg, 6.5%) !default;\n\n// Navbar links\n$navbar-default-link-color:                #777 !default;\n$navbar-default-link-hover-color:          #333 !default;\n$navbar-default-link-hover-bg:             transparent !default;\n$navbar-default-link-active-color:         #555 !default;\n$navbar-default-link-active-bg:            darken($navbar-default-bg, 6.5%) !default;\n$navbar-default-link-disabled-color:       #ccc !default;\n$navbar-default-link-disabled-bg:          transparent !default;\n\n// Navbar brand label\n$navbar-default-brand-color:               $navbar-default-link-color !default;\n$navbar-default-brand-hover-color:         darken($navbar-default-brand-color, 10%) !default;\n$navbar-default-brand-hover-bg:            transparent !default;\n\n// Navbar toggle\n$navbar-default-toggle-hover-bg:           #ddd !default;\n$navbar-default-toggle-icon-bar-bg:        #888 !default;\n$navbar-default-toggle-border-color:       #ddd !default;\n\n\n//=== Inverted navbar\n// Reset inverted navbar basics\n$navbar-inverse-color:                      lighten($gray-light, 15%) !default;\n$navbar-inverse-bg:                         #222 !default;\n$navbar-inverse-border:                     darken($navbar-inverse-bg, 10%) !default;\n\n// Inverted navbar links\n$navbar-inverse-link-color:                 lighten($gray-light, 15%) !default;\n$navbar-inverse-link-hover-color:           #fff !default;\n$navbar-inverse-link-hover-bg:              transparent !default;\n$navbar-inverse-link-active-color:          $navbar-inverse-link-hover-color !default;\n$navbar-inverse-link-active-bg:             darken($navbar-inverse-bg, 10%) !default;\n$navbar-inverse-link-disabled-color:        #444 !default;\n$navbar-inverse-link-disabled-bg:           transparent !default;\n\n// Inverted navbar brand label\n$navbar-inverse-brand-color:                $navbar-inverse-link-color !default;\n$navbar-inverse-brand-hover-color:          #fff !default;\n$navbar-inverse-brand-hover-bg:             transparent !default;\n\n// Inverted navbar toggle\n$navbar-inverse-toggle-hover-bg:            #333 !default;\n$navbar-inverse-toggle-icon-bar-bg:         #fff !default;\n$navbar-inverse-toggle-border-color:        #333 !default;\n\n\n//== Navs\n//\n//##\n\n//=== Shared nav styles\n$nav-link-padding:                          10px 15px !default;\n$nav-link-hover-bg:                         $gray-lighter !default;\n\n$nav-disabled-link-color:                   $gray-light !default;\n$nav-disabled-link-hover-color:             $gray-light !default;\n\n//== Tabs\n$nav-tabs-border-color:                     #ddd !default;\n\n$nav-tabs-link-hover-border-color:          $gray-lighter !default;\n\n$nav-tabs-active-link-hover-bg:             $body-bg !default;\n$nav-tabs-active-link-hover-color:          $gray !default;\n$nav-tabs-active-link-hover-border-color:   #ddd !default;\n\n$nav-tabs-justified-link-border-color:            #ddd !default;\n$nav-tabs-justified-active-link-border-color:     $body-bg !default;\n\n//== Pills\n$nav-pills-border-radius:                   $border-radius-base !default;\n$nav-pills-active-link-hover-bg:            $component-active-bg !default;\n$nav-pills-active-link-hover-color:         $component-active-color !default;\n\n\n//== Pagination\n//\n//##\n\n$pagination-color:                     $link-color !default;\n$pagination-bg:                        #fff !default;\n$pagination-border:                    #ddd !default;\n\n$pagination-hover-color:               $link-hover-color !default;\n$pagination-hover-bg:                  $gray-lighter !default;\n$pagination-hover-border:              #ddd !default;\n\n$pagination-active-color:              #fff !default;\n$pagination-active-bg:                 $brand-primary !default;\n$pagination-active-border:             $brand-primary !default;\n\n$pagination-disabled-color:            $gray-light !default;\n$pagination-disabled-bg:               #fff !default;\n$pagination-disabled-border:           #ddd !default;\n\n\n//== Pager\n//\n//##\n\n$pager-bg:                             $pagination-bg !default;\n$pager-border:                         $pagination-border !default;\n$pager-border-radius:                  15px !default;\n\n$pager-hover-bg:                       $pagination-hover-bg !default;\n\n$pager-active-bg:                      $pagination-active-bg !default;\n$pager-active-color:                   $pagination-active-color !default;\n\n$pager-disabled-color:                 $pagination-disabled-color !default;\n\n\n//== Jumbotron\n//\n//##\n\n$jumbotron-padding:              30px !default;\n$jumbotron-color:                inherit !default;\n$jumbotron-bg:                   $gray-lighter !default;\n$jumbotron-heading-color:        inherit !default;\n$jumbotron-font-size:            ceil(($font-size-base * 1.5)) !default;\n$jumbotron-heading-font-size:    ceil(($font-size-base * 4.5)) !default;\n\n\n//== Form states and alerts\n//\n//## Define colors for form feedback states and, by default, alerts.\n\n$state-success-text:             #3c763d !default;\n$state-success-bg:               #dff0d8 !default;\n$state-success-border:           darken(adjust-hue($state-success-bg, -10), 5%) !default;\n\n$state-info-text:                #31708f !default;\n$state-info-bg:                  #d9edf7 !default;\n$state-info-border:              darken(adjust-hue($state-info-bg, -10), 7%) !default;\n\n$state-warning-text:             #8a6d3b !default;\n$state-warning-bg:               #fcf8e3 !default;\n$state-warning-border:           darken(adjust-hue($state-warning-bg, -10), 5%) !default;\n\n$state-danger-text:              #a94442 !default;\n$state-danger-bg:                #f2dede !default;\n$state-danger-border:            darken(adjust-hue($state-danger-bg, -10), 5%) !default;\n\n\n//== Tooltips\n//\n//##\n\n//** Tooltip max width\n$tooltip-max-width:           200px !default;\n//** Tooltip text color\n$tooltip-color:               #fff !default;\n//** Tooltip background color\n$tooltip-bg:                  #000 !default;\n$tooltip-opacity:             .9 !default;\n\n//** Tooltip arrow width\n$tooltip-arrow-width:         5px !default;\n//** Tooltip arrow color\n$tooltip-arrow-color:         $tooltip-bg !default;\n\n\n//== Popovers\n//\n//##\n\n//** Popover body background color\n$popover-bg:                          #fff !default;\n//** Popover maximum width\n$popover-max-width:                   276px !default;\n//** Popover border color\n$popover-border-color:                rgba(0,0,0,.2) !default;\n//** Popover fallback border color\n$popover-fallback-border-color:       #ccc !default;\n\n//** Popover title background color\n$popover-title-bg:                    darken($popover-bg, 3%) !default;\n\n//** Popover arrow width\n$popover-arrow-width:                 10px !default;\n//** Popover arrow color\n$popover-arrow-color:                 $popover-bg !default;\n\n//** Popover outer arrow width\n$popover-arrow-outer-width:           ($popover-arrow-width + 1) !default;\n//** Popover outer arrow color\n$popover-arrow-outer-color:           fade_in($popover-border-color, 0.05) !default;\n//** Popover outer arrow fallback color\n$popover-arrow-outer-fallback-color:  darken($popover-fallback-border-color, 20%) !default;\n\n\n//== Labels\n//\n//##\n\n//** Default label background color\n$label-default-bg:            $gray-light !default;\n//** Primary label background color\n$label-primary-bg:            $brand-primary !default;\n//** Success label background color\n$label-success-bg:            $brand-success !default;\n//** Info label background color\n$label-info-bg:               $brand-info !default;\n//** Warning label background color\n$label-warning-bg:            $brand-warning !default;\n//** Danger label background color\n$label-danger-bg:             $brand-danger !default;\n\n//** Default label text color\n$label-color:                 #fff !default;\n//** Default text color of a linked label\n$label-link-hover-color:      #fff !default;\n\n\n//== Modals\n//\n//##\n\n//** Padding applied to the modal body\n$modal-inner-padding:         15px !default;\n\n//** Padding applied to the modal title\n$modal-title-padding:         15px !default;\n//** Modal title line-height\n$modal-title-line-height:     $line-height-base !default;\n\n//** Background color of modal content area\n$modal-content-bg:                             #fff !default;\n//** Modal content border color\n$modal-content-border-color:                   rgba(0,0,0,.2) !default;\n//** Modal content border color **for IE8**\n$modal-content-fallback-border-color:          #999 !default;\n\n//** Modal backdrop background color\n$modal-backdrop-bg:           #000 !default;\n//** Modal backdrop opacity\n$modal-backdrop-opacity:      .5 !default;\n//** Modal header border color\n$modal-header-border-color:   #e5e5e5 !default;\n//** Modal footer border color\n$modal-footer-border-color:   $modal-header-border-color !default;\n\n$modal-lg:                    900px !default;\n$modal-md:                    600px !default;\n$modal-sm:                    300px !default;\n\n\n//== Alerts\n//\n//## Define alert colors, border radius, and padding.\n\n$alert-padding:               15px !default;\n$alert-border-radius:         $border-radius-base !default;\n$alert-link-font-weight:      bold !default;\n\n$alert-success-bg:            $state-success-bg !default;\n$alert-success-text:          $state-success-text !default;\n$alert-success-border:        $state-success-border !default;\n\n$alert-info-bg:               $state-info-bg !default;\n$alert-info-text:             $state-info-text !default;\n$alert-info-border:           $state-info-border !default;\n\n$alert-warning-bg:            $state-warning-bg !default;\n$alert-warning-text:          $state-warning-text !default;\n$alert-warning-border:        $state-warning-border !default;\n\n$alert-danger-bg:             $state-danger-bg !default;\n$alert-danger-text:           $state-danger-text !default;\n$alert-danger-border:         $state-danger-border !default;\n\n\n//== Progress bars\n//\n//##\n\n//** Background color of the whole progress component\n$progress-bg:                 #f5f5f5 !default;\n//** Progress bar text color\n$progress-bar-color:          #fff !default;\n//** Variable for setting rounded corners on progress bar.\n$progress-border-radius:      $border-radius-base !default;\n\n//** Default progress bar color\n$progress-bar-bg:             $brand-primary !default;\n//** Success progress bar color\n$progress-bar-success-bg:     $brand-success !default;\n//** Warning progress bar color\n$progress-bar-warning-bg:     $brand-warning !default;\n//** Danger progress bar color\n$progress-bar-danger-bg:      $brand-danger !default;\n//** Info progress bar color\n$progress-bar-info-bg:        $brand-info !default;\n\n\n//== List group\n//\n//##\n\n//** Background color on `.list-group-item`\n$list-group-bg:                 #fff !default;\n//** `.list-group-item` border color\n$list-group-border:             #ddd !default;\n//** List group border radius\n$list-group-border-radius:      $border-radius-base !default;\n\n//** Background color of single list items on hover\n$list-group-hover-bg:           #f5f5f5 !default;\n//** Text color of active list items\n$list-group-active-color:       $component-active-color !default;\n//** Background color of active list items\n$list-group-active-bg:          $component-active-bg !default;\n//** Border color of active list elements\n$list-group-active-border:      $list-group-active-bg !default;\n//** Text color for content within active list items\n$list-group-active-text-color:  lighten($list-group-active-bg, 40%) !default;\n\n//** Text color of disabled list items\n$list-group-disabled-color:      $gray-light !default;\n//** Background color of disabled list items\n$list-group-disabled-bg:         $gray-lighter !default;\n//** Text color for content within disabled list items\n$list-group-disabled-text-color: $list-group-disabled-color !default;\n\n$list-group-link-color:         #555 !default;\n$list-group-link-hover-color:   $list-group-link-color !default;\n$list-group-link-heading-color: #333 !default;\n\n\n//== Panels\n//\n//##\n\n$panel-bg:                    #fff !default;\n$panel-body-padding:          15px !default;\n$panel-heading-padding:       10px 15px !default;\n$panel-footer-padding:        $panel-heading-padding !default;\n$panel-border-radius:         $border-radius-base !default;\n\n//** Border color for elements within panels\n$panel-inner-border:          #ddd !default;\n$panel-footer-bg:             #f5f5f5 !default;\n\n$panel-default-text:          $gray-dark !default;\n$panel-default-border:        #ddd !default;\n$panel-default-heading-bg:    #f5f5f5 !default;\n\n$panel-primary-text:          #fff !default;\n$panel-primary-border:        $brand-primary !default;\n$panel-primary-heading-bg:    $brand-primary !default;\n\n$panel-success-text:          $state-success-text !default;\n$panel-success-border:        $state-success-border !default;\n$panel-success-heading-bg:    $state-success-bg !default;\n\n$panel-info-text:             $state-info-text !default;\n$panel-info-border:           $state-info-border !default;\n$panel-info-heading-bg:       $state-info-bg !default;\n\n$panel-warning-text:          $state-warning-text !default;\n$panel-warning-border:        $state-warning-border !default;\n$panel-warning-heading-bg:    $state-warning-bg !default;\n\n$panel-danger-text:           $state-danger-text !default;\n$panel-danger-border:         $state-danger-border !default;\n$panel-danger-heading-bg:     $state-danger-bg !default;\n\n\n//== Thumbnails\n//\n//##\n\n//** Padding around the thumbnail image\n$thumbnail-padding:           4px !default;\n//** Thumbnail background color\n$thumbnail-bg:                $body-bg !default;\n//** Thumbnail border color\n$thumbnail-border:            #ddd !default;\n//** Thumbnail border radius\n$thumbnail-border-radius:     $border-radius-base !default;\n\n//** Custom text color for thumbnail captions\n$thumbnail-caption-color:     $text-color !default;\n//** Padding around the thumbnail caption\n$thumbnail-caption-padding:   9px !default;\n\n\n//== Wells\n//\n//##\n\n$well-bg:                     #f5f5f5 !default;\n$well-border:                 darken($well-bg, 7%) !default;\n\n\n//== Badges\n//\n//##\n\n$badge-color:                 #fff !default;\n//** Linked badge text color on hover\n$badge-link-hover-color:      #fff !default;\n$badge-bg:                    $gray-light !default;\n\n//** Badge text color in active nav link\n$badge-active-color:          $link-color !default;\n//** Badge background color in active nav link\n$badge-active-bg:             #fff !default;\n\n$badge-font-weight:           bold !default;\n$badge-line-height:           1 !default;\n$badge-border-radius:         10px !default;\n\n\n//== Breadcrumbs\n//\n//##\n\n$breadcrumb-padding-vertical:   8px !default;\n$breadcrumb-padding-horizontal: 15px !default;\n//** Breadcrumb background color\n$breadcrumb-bg:                 #f5f5f5 !default;\n//** Breadcrumb text color\n$breadcrumb-color:              #ccc !default;\n//** Text color of current page in the breadcrumb\n$breadcrumb-active-color:       $gray-light !default;\n//** Textual separator for between breadcrumb elements\n$breadcrumb-separator:          \"/\" !default;\n\n\n//== Carousel\n//\n//##\n\n$carousel-text-shadow:                        0 1px 2px rgba(0,0,0,.6) !default;\n\n$carousel-control-color:                      #fff !default;\n$carousel-control-width:                      15% !default;\n$carousel-control-opacity:                    .5 !default;\n$carousel-control-font-size:                  20px !default;\n\n$carousel-indicator-active-bg:                #fff !default;\n$carousel-indicator-border-color:             #fff !default;\n\n$carousel-caption-color:                      #fff !default;\n\n\n//== Close\n//\n//##\n\n$close-font-weight:           bold !default;\n$close-color:                 #000 !default;\n$close-text-shadow:           0 1px 0 #fff !default;\n\n\n//== Code\n//\n//##\n\n$code-color:                  #c7254e !default;\n$code-bg:                     #f9f2f4 !default;\n\n$kbd-color:                   #fff !default;\n$kbd-bg:                      #333 !default;\n\n$pre-bg:                      #f5f5f5 !default;\n$pre-color:                   $gray-dark !default;\n$pre-border-color:            #ccc !default;\n$pre-scrollable-max-height:   340px !default;\n\n\n//== Type\n//\n//##\n\n//** Horizontal offset for forms and lists.\n$component-offset-horizontal: 180px !default;\n//** Text muted color\n$text-muted:                  $gray-light !default;\n//** Abbreviations and acronyms border color\n$abbr-border-color:           $gray-light !default;\n//** Headings small color\n$headings-small-color:        $gray-light !default;\n//** Blockquote small color\n$blockquote-small-color:      $gray-light !default;\n//** Blockquote font size\n$blockquote-font-size:        ($font-size-base * 1.25) !default;\n//** Blockquote border color\n$blockquote-border-color:     $gray-lighter !default;\n//** Page header border color\n$page-header-border-color:    $gray-lighter !default;\n//** Width of horizontal description list titles\n$dl-horizontal-offset:        $component-offset-horizontal !default;\n//** Point at which .dl-horizontal becomes horizontal\n$dl-horizontal-breakpoint:    $grid-float-breakpoint !default;\n//** Horizontal line color.\n$hr-border:                   $gray-lighter !default;\n", "// WebKit-style focus\n\n@mixin tab-focus() {\n  // WebKit-specific. Other browsers will keep their default outline style.\n  // (Initially tried to also force default via `outline: initial`,\n  // but that seems to erroneously remove the outline in Firefox altogether.)\n  outline: 5px auto -webkit-focus-ring-color;\n  outline-offset: -2px;\n}\n", "// Image Mixins\n// - Responsive image\n// - Retina image\n\n\n// Responsive image\n//\n// Keep images from scaling beyond the width of their parents.\n@mixin img-responsive($display: block) {\n  display: $display;\n  max-width: 100%; // Part 1: Set a maximum relative to the parent\n  height: auto; // Part 2: Scale the height according to the width, otherwise you get stretching\n}\n\n\n// Retina image\n//\n// Short retina mixin for setting background-image and -size. Note that the\n// spelling of `min--moz-device-pixel-ratio` is intentional.\n@mixin img-retina($file-1x, $file-2x, $width-1x, $height-1x) {\n  background-image: url(if($bootstrap-sass-asset-helper, twbs-image-path(\"#{$file-1x}\"), \"#{$file-1x}\"));\n\n  @media\n  only screen and (-webkit-min-device-pixel-ratio: 2),\n  only screen and (   min--moz-device-pixel-ratio: 2),\n  only screen and (     -o-min-device-pixel-ratio: 2/1),\n  only screen and (        min-device-pixel-ratio: 2),\n  only screen and (                min-resolution: 192dpi),\n  only screen and (                min-resolution: 2dppx) {\n    background-image: url(if($bootstrap-sass-asset-helper, twbs-image-path(\"#{$file-2x}\"), \"#{$file-2x}\"));\n    background-size: $width-1x $height-1x;\n  }\n}\n", "//\n// Typography\n// --------------------------------------------------\n\n\n// Headings\n// -------------------------\n\nh1, h2, h3, h4, h5, h6,\n.h1, .h2, .h3, .h4, .h5, .h6 {\n  font-family: $headings-font-family;\n  font-weight: $headings-font-weight;\n  line-height: $headings-line-height;\n  color: $headings-color;\n\n  small,\n  .small {\n    font-weight: normal;\n    line-height: 1;\n    color: $headings-small-color;\n  }\n}\n\nh1, .h1,\nh2, .h2,\nh3, .h3 {\n  margin-top: $line-height-computed;\n  margin-bottom: ($line-height-computed / 2);\n\n  small,\n  .small {\n    font-size: 65%;\n  }\n}\nh4, .h4,\nh5, .h5,\nh6, .h6 {\n  margin-top: ($line-height-computed / 2);\n  margin-bottom: ($line-height-computed / 2);\n\n  small,\n  .small {\n    font-size: 75%;\n  }\n}\n\nh1, .h1 { font-size: $font-size-h1; }\nh2, .h2 { font-size: $font-size-h2; }\nh3, .h3 { font-size: $font-size-h3; }\nh4, .h4 { font-size: $font-size-h4; }\nh5, .h5 { font-size: $font-size-h5; }\nh6, .h6 { font-size: $font-size-h6; }\n\n\n// Body text\n// -------------------------\n\np {\n  margin: 0 0 ($line-height-computed / 2);\n}\n\n.lead {\n  margin-bottom: $line-height-computed;\n  font-size: floor(($font-size-base * 1.15));\n  font-weight: 300;\n  line-height: 1.4;\n\n  @media (min-width: $screen-sm-min) {\n    font-size: ($font-size-base * 1.5);\n  }\n}\n\n\n// Emphasis & misc\n// -------------------------\n\n// Ex: (12px small font / 14px base font) * 100% = about 85%\nsmall,\n.small {\n  font-size: floor((100% * $font-size-small / $font-size-base));\n}\n\nmark,\n.mark {\n  background-color: $state-warning-bg;\n  padding: .2em;\n}\n\n// Alignment\n.text-left           { text-align: left; }\n.text-right          { text-align: right; }\n.text-center         { text-align: center; }\n.text-justify        { text-align: justify; }\n.text-nowrap         { white-space: nowrap; }\n\n// Transformation\n.text-lowercase      { text-transform: lowercase; }\n.text-uppercase      { text-transform: uppercase; }\n.text-capitalize     { text-transform: capitalize; }\n\n// Contextual colors\n.text-muted {\n  color: $text-muted;\n}\n\n@include text-emphasis-variant('.text-primary', $brand-primary);\n\n@include text-emphasis-variant('.text-success', $state-success-text);\n\n@include text-emphasis-variant('.text-info', $state-info-text);\n\n@include text-emphasis-variant('.text-warning', $state-warning-text);\n\n@include text-emphasis-variant('.text-danger', $state-danger-text);\n\n// Contextual backgrounds\n// For now we'll leave these alongside the text classes until v4 when we can\n// safely shift things around (per SemVer rules).\n.bg-primary {\n  // Given the contrast here, this is the only class to have its color inverted\n  // automatically.\n  color: #fff;\n}\n@include bg-variant('.bg-primary', $brand-primary);\n\n@include bg-variant('.bg-success', $state-success-bg);\n\n@include bg-variant('.bg-info', $state-info-bg);\n\n@include bg-variant('.bg-warning', $state-warning-bg);\n\n@include bg-variant('.bg-danger', $state-danger-bg);\n\n\n// Page header\n// -------------------------\n\n.page-header {\n  padding-bottom: (($line-height-computed / 2) - 1);\n  margin: ($line-height-computed * 2) 0 $line-height-computed;\n  border-bottom: 1px solid $page-header-border-color;\n}\n\n\n// Lists\n// -------------------------\n\n// Unordered and Ordered lists\nul,\nol {\n  margin-top: 0;\n  margin-bottom: ($line-height-computed / 2);\n  ul,\n  ol {\n    margin-bottom: 0;\n  }\n}\n\n// List options\n\n// [converter] extracted from `.list-unstyled` for libsass compatibility\n@mixin list-unstyled {\n  padding-left: 0;\n  list-style: none;\n}\n// [converter] extracted as `@mixin list-unstyled` for libsass compatibility\n.list-unstyled {\n  @include list-unstyled;\n}\n\n\n// Inline turns list items into inline-block\n.list-inline {\n  @include list-unstyled;\n  margin-left: -5px;\n\n  > li {\n    display: inline-block;\n    padding-left: 5px;\n    padding-right: 5px;\n  }\n}\n\n// Description Lists\ndl {\n  margin-top: 0; // Remove browser default\n  margin-bottom: $line-height-computed;\n}\ndt,\ndd {\n  line-height: $line-height-base;\n}\ndt {\n  font-weight: bold;\n}\ndd {\n  margin-left: 0; // Undo browser default\n}\n\n// Horizontal description lists\n//\n// Defaults to being stacked without any of the below styles applied, until the\n// grid breakpoint is reached (default of ~768px).\n\n.dl-horizontal {\n  dd {\n    @include clearfix; // Clear the floated `dt` if an empty `dd` is present\n  }\n\n  @media (min-width: $dl-horizontal-breakpoint) {\n    dt {\n      float: left;\n      width: ($dl-horizontal-offset - 20);\n      clear: left;\n      text-align: right;\n      @include text-overflow;\n    }\n    dd {\n      margin-left: $dl-horizontal-offset;\n    }\n  }\n}\n\n\n// Misc\n// -------------------------\n\n// Abbreviations and acronyms\nabbr[title],\n// Add data-* attribute to help out our tooltip plugin, per https://github.com/twbs/bootstrap/issues/5257\nabbr[data-original-title] {\n  cursor: help;\n  border-bottom: 1px dotted $abbr-border-color;\n}\n.initialism {\n  font-size: 90%;\n  @extend .text-uppercase;\n}\n\n// Blockquotes\nblockquote {\n  padding: ($line-height-computed / 2) $line-height-computed;\n  margin: 0 0 $line-height-computed;\n  font-size: $blockquote-font-size;\n  border-left: 5px solid $blockquote-border-color;\n\n  p,\n  ul,\n  ol {\n    &:last-child {\n      margin-bottom: 0;\n    }\n  }\n\n  // Note: Deprecated small and .small as of v3.1.0\n  // Context: https://github.com/twbs/bootstrap/issues/11660\n  footer,\n  small,\n  .small {\n    display: block;\n    font-size: 80%; // back to default font-size\n    line-height: $line-height-base;\n    color: $blockquote-small-color;\n\n    &:before {\n      content: '\\2014 \\00A0'; // em dash, nbsp\n    }\n  }\n}\n\n// Opposite alignment of blockquote\n//\n// Heads up: `blockquote.pull-right` has been deprecated as of v3.1.0.\n.blockquote-reverse,\nblockquote.pull-right {\n  padding-right: 15px;\n  padding-left: 0;\n  border-right: 5px solid $blockquote-border-color;\n  border-left: 0;\n  text-align: right;\n\n  // Account for citation\n  footer,\n  small,\n  .small {\n    &:before { content: ''; }\n    &:after {\n      content: '\\00A0 \\2014'; // nbsp, em dash\n    }\n  }\n}\n\n// Addresses\naddress {\n  margin-bottom: $line-height-computed;\n  font-style: normal;\n  line-height: $line-height-base;\n}\n", "// Typography\n\n// [converter] $parent hack\n@mixin text-emphasis-variant($parent, $color) {\n  #{$parent} {\n    color: $color;\n  }\n  a#{$parent}:hover,\n  a#{$parent}:focus {\n    color: darken($color, 10%);\n  }\n}\n", "// Contextual backgrounds\n\n// [converter] $parent hack\n@mixin bg-variant($parent, $color) {\n  #{$parent} {\n    background-color: $color;\n  }\n  a#{$parent}:hover,\n  a#{$parent}:focus {\n    background-color: darken($color, 10%);\n  }\n}\n", "// Clearfix\n//\n// For modern browsers\n// 1. The space content is one way to avoid an Opera bug when the\n//    contenteditable attribute is included anywhere else in the document.\n//    Otherwise it causes space to appear at the top and bottom of elements\n//    that are clearfixed.\n// 2. The use of `table` rather than `block` is only necessary if using\n//    `:before` to contain the top-margins of child elements.\n//\n// Source: http://nicolasgallagher.com/micro-clearfix-hack/\n\n@mixin clearfix() {\n  &:before,\n  &:after {\n    content: \" \"; // 1\n    display: table; // 2\n  }\n  &:after {\n    clear: both;\n  }\n}\n", "// Text overflow\n// Requires inline-block or block for proper styling\n\n@mixin text-overflow() {\n  overflow: hidden;\n  text-overflow: ellipsis;\n  white-space: nowrap;\n}\n", "//\n// Code (inline and block)\n// --------------------------------------------------\n\n\n// Inline and block code styles\ncode,\nkbd,\npre,\nsamp {\n  font-family: $font-family-monospace;\n}\n\n// Inline code\ncode {\n  padding: 2px 4px;\n  font-size: 90%;\n  color: $code-color;\n  background-color: $code-bg;\n  border-radius: $border-radius-base;\n}\n\n// User input typically entered via keyboard\nkbd {\n  padding: 2px 4px;\n  font-size: 90%;\n  color: $kbd-color;\n  background-color: $kbd-bg;\n  border-radius: $border-radius-small;\n  box-shadow: inset 0 -1px 0 rgba(0,0,0,.25);\n\n  kbd {\n    padding: 0;\n    font-size: 100%;\n    font-weight: bold;\n    box-shadow: none;\n  }\n}\n\n// Blocks of code\npre {\n  display: block;\n  padding: (($line-height-computed - 1) / 2);\n  margin: 0 0 ($line-height-computed / 2);\n  font-size: ($font-size-base - 1); // 14px to 13px\n  line-height: $line-height-base;\n  word-break: break-all;\n  word-wrap: break-word;\n  color: $pre-color;\n  background-color: $pre-bg;\n  border: 1px solid $pre-border-color;\n  border-radius: $border-radius-base;\n\n  // Account for some code outputs that place code tags in pre tags\n  code {\n    padding: 0;\n    font-size: inherit;\n    color: inherit;\n    white-space: pre-wrap;\n    background-color: transparent;\n    border-radius: 0;\n  }\n}\n\n// Enable scrollable blocks of code\n.pre-scrollable {\n  max-height: $pre-scrollable-max-height;\n  overflow-y: scroll;\n}\n", "//\n// Grid system\n// --------------------------------------------------\n\n\n// Container widths\n//\n// Set the container width, and override it for fixed navbars in media queries.\n\n.container {\n  @include container-fixed;\n\n  @media (min-width: $screen-sm-min) {\n    width: $container-sm;\n  }\n  @media (min-width: $screen-md-min) {\n    width: $container-md;\n  }\n  @media (min-width: $screen-lg-min) {\n    width: $container-lg;\n  }\n}\n\n\n// Fluid container\n//\n// Utilizes the mixin meant for fixed width containers, but without any defined\n// width for fluid, full width layouts.\n\n.container-fluid {\n  @include container-fixed;\n}\n\n\n// Row\n//\n// Rows contain and clear the floats of your columns.\n\n.row {\n  @include make-row;\n}\n\n\n// Columns\n//\n// Common styles for small and large grid columns\n\n@include make-grid-columns;\n\n\n// Extra small grid\n//\n// Columns, offsets, pushes, and pulls for extra small devices like\n// smartphones.\n\n@include make-grid(xs);\n\n\n// Small grid\n//\n// Columns, offsets, pushes, and pulls for the small device range, from phones\n// to tablets.\n\n@media (min-width: $screen-sm-min) {\n  @include make-grid(sm);\n}\n\n\n// Medium grid\n//\n// Columns, offsets, pushes, and pulls for the desktop device range.\n\n@media (min-width: $screen-md-min) {\n  @include make-grid(md);\n}\n\n\n// Large grid\n//\n// Columns, offsets, pushes, and pulls for the large desktop device range.\n\n@media (min-width: $screen-lg-min) {\n  @include make-grid(lg);\n}\n", "// Grid system\n//\n// Generate semantic grid columns with these mixins.\n\n// Centered container element\n@mixin container-fixed($gutter: $grid-gutter-width) {\n  margin-right: auto;\n  margin-left: auto;\n  padding-left:  floor(($gutter / 2));\n  padding-right: ceil(($gutter / 2));\n  @include clearfix;\n}\n\n// Creates a wrapper for a series of columns\n@mixin make-row($gutter: $grid-gutter-width) {\n  margin-left:  ceil(($gutter / -2));\n  margin-right: floor(($gutter / -2));\n  @include clearfix;\n}\n\n// Generate the extra small columns\n@mixin make-xs-column($columns, $gutter: $grid-gutter-width) {\n  position: relative;\n  float: left;\n  width: percentage(($columns / $grid-columns));\n  min-height: 1px;\n  padding-left:  ($gutter / 2);\n  padding-right: ($gutter / 2);\n}\n@mixin make-xs-column-offset($columns) {\n  margin-left: percentage(($columns / $grid-columns));\n}\n@mixin make-xs-column-push($columns) {\n  left: percentage(($columns / $grid-columns));\n}\n@mixin make-xs-column-pull($columns) {\n  right: percentage(($columns / $grid-columns));\n}\n\n// Generate the small columns\n@mixin make-sm-column($columns, $gutter: $grid-gutter-width) {\n  position: relative;\n  min-height: 1px;\n  padding-left:  ($gutter / 2);\n  padding-right: ($gutter / 2);\n\n  @media (min-width: $screen-sm-min) {\n    float: left;\n    width: percentage(($columns / $grid-columns));\n  }\n}\n@mixin make-sm-column-offset($columns) {\n  @media (min-width: $screen-sm-min) {\n    margin-left: percentage(($columns / $grid-columns));\n  }\n}\n@mixin make-sm-column-push($columns) {\n  @media (min-width: $screen-sm-min) {\n    left: percentage(($columns / $grid-columns));\n  }\n}\n@mixin make-sm-column-pull($columns) {\n  @media (min-width: $screen-sm-min) {\n    right: percentage(($columns / $grid-columns));\n  }\n}\n\n// Generate the medium columns\n@mixin make-md-column($columns, $gutter: $grid-gutter-width) {\n  position: relative;\n  min-height: 1px;\n  padding-left:  ($gutter / 2);\n  padding-right: ($gutter / 2);\n\n  @media (min-width: $screen-md-min) {\n    float: left;\n    width: percentage(($columns / $grid-columns));\n  }\n}\n@mixin make-md-column-offset($columns) {\n  @media (min-width: $screen-md-min) {\n    margin-left: percentage(($columns / $grid-columns));\n  }\n}\n@mixin make-md-column-push($columns) {\n  @media (min-width: $screen-md-min) {\n    left: percentage(($columns / $grid-columns));\n  }\n}\n@mixin make-md-column-pull($columns) {\n  @media (min-width: $screen-md-min) {\n    right: percentage(($columns / $grid-columns));\n  }\n}\n\n// Generate the large columns\n@mixin make-lg-column($columns, $gutter: $grid-gutter-width) {\n  position: relative;\n  min-height: 1px;\n  padding-left:  ($gutter / 2);\n  padding-right: ($gutter / 2);\n\n  @media (min-width: $screen-lg-min) {\n    float: left;\n    width: percentage(($columns / $grid-columns));\n  }\n}\n@mixin make-lg-column-offset($columns) {\n  @media (min-width: $screen-lg-min) {\n    margin-left: percentage(($columns / $grid-columns));\n  }\n}\n@mixin make-lg-column-push($columns) {\n  @media (min-width: $screen-lg-min) {\n    left: percentage(($columns / $grid-columns));\n  }\n}\n@mixin make-lg-column-pull($columns) {\n  @media (min-width: $screen-lg-min) {\n    right: percentage(($columns / $grid-columns));\n  }\n}\n", "// Framework grid generation\n//\n// Used only by <PERSON><PERSON><PERSON> to generate the correct number of grid classes given\n// any value of `$grid-columns`.\n\n// [converter] This is defined recursively in LESS, but Sass supports real loops\n@mixin make-grid-columns($i: 1, $list: \".col-xs-#{$i}, .col-sm-#{$i}, .col-md-#{$i}, .col-lg-#{$i}\") {\n  @for $i from (1 + 1) through $grid-columns {\n    $list: \"#{$list}, .col-xs-#{$i}, .col-sm-#{$i}, .col-md-#{$i}, .col-lg-#{$i}\";\n  }\n  #{$list} {\n    position: relative;\n    // Prevent columns from collapsing when empty\n    min-height: 1px;\n    // Inner gutter via padding\n    padding-left:  ceil(($grid-gutter-width / 2));\n    padding-right: floor(($grid-gutter-width / 2));\n  }\n}\n\n\n// [converter] This is defined recursively in LESS, but Sass supports real loops\n@mixin float-grid-columns($class, $i: 1, $list: \".col-#{$class}-#{$i}\") {\n  @for $i from (1 + 1) through $grid-columns {\n    $list: \"#{$list}, .col-#{$class}-#{$i}\";\n  }\n  #{$list} {\n    float: left;\n  }\n}\n\n\n@mixin calc-grid-column($index, $class, $type) {\n  @if ($type == width) and ($index > 0) {\n    .col-#{$class}-#{$index} {\n      width: percentage(($index / $grid-columns));\n    }\n  }\n  @if ($type == push) and ($index > 0) {\n    .col-#{$class}-push-#{$index} {\n      left: percentage(($index / $grid-columns));\n    }\n  }\n  @if ($type == push) and ($index == 0) {\n    .col-#{$class}-push-0 {\n      left: auto;\n    }\n  }\n  @if ($type == pull) and ($index > 0) {\n    .col-#{$class}-pull-#{$index} {\n      right: percentage(($index / $grid-columns));\n    }\n  }\n  @if ($type == pull) and ($index == 0) {\n    .col-#{$class}-pull-0 {\n      right: auto;\n    }\n  }\n  @if ($type == offset) {\n    .col-#{$class}-offset-#{$index} {\n      margin-left: percentage(($index / $grid-columns));\n    }\n  }\n}\n\n// [converter] This is defined recursively in LESS, but Sass supports real loops\n@mixin loop-grid-columns($columns, $class, $type) {\n  @for $i from 0 through $columns {\n    @include calc-grid-column($i, $class, $type);\n  }\n}\n\n\n// Create grid for specific class\n@mixin make-grid($class) {\n  @include float-grid-columns($class);\n  @include loop-grid-columns($grid-columns, $class, width);\n  @include loop-grid-columns($grid-columns, $class, pull);\n  @include loop-grid-columns($grid-columns, $class, push);\n  @include loop-grid-columns($grid-columns, $class, offset);\n}\n", "//\n// Tables\n// --------------------------------------------------\n\n\ntable {\n  background-color: $table-bg;\n}\ncaption {\n  padding-top: $table-cell-padding;\n  padding-bottom: $table-cell-padding;\n  color: $text-muted;\n  text-align: left;\n}\nth {\n  text-align: left;\n}\n\n\n// Baseline styles\n\n.table {\n  width: 100%;\n  max-width: 100%;\n  margin-bottom: $line-height-computed;\n  // Cells\n  > thead,\n  > tbody,\n  > tfoot {\n    > tr {\n      > th,\n      > td {\n        padding: $table-cell-padding;\n        line-height: $line-height-base;\n        vertical-align: top;\n        border-top: 1px solid $table-border-color;\n      }\n    }\n  }\n  // Bottom align for column headings\n  > thead > tr > th {\n    vertical-align: bottom;\n    border-bottom: 2px solid $table-border-color;\n  }\n  // Remove top border from thead by default\n  > caption + thead,\n  > colgroup + thead,\n  > thead:first-child {\n    > tr:first-child {\n      > th,\n      > td {\n        border-top: 0;\n      }\n    }\n  }\n  // Account for multiple tbody instances\n  > tbody + tbody {\n    border-top: 2px solid $table-border-color;\n  }\n\n  // Nesting\n  .table {\n    background-color: $body-bg;\n  }\n}\n\n\n// Condensed table w/ half padding\n\n.table-condensed {\n  > thead,\n  > tbody,\n  > tfoot {\n    > tr {\n      > th,\n      > td {\n        padding: $table-condensed-cell-padding;\n      }\n    }\n  }\n}\n\n\n// Bordered version\n//\n// Add borders all around the table and between all the columns.\n\n.table-bordered {\n  border: 1px solid $table-border-color;\n  > thead,\n  > tbody,\n  > tfoot {\n    > tr {\n      > th,\n      > td {\n        border: 1px solid $table-border-color;\n      }\n    }\n  }\n  > thead > tr {\n    > th,\n    > td {\n      border-bottom-width: 2px;\n    }\n  }\n}\n\n\n// Zebra-striping\n//\n// Default zebra-stripe styles (alternating gray and transparent backgrounds)\n\n.table-striped {\n  > tbody > tr:nth-of-type(odd) {\n    background-color: $table-bg-accent;\n  }\n}\n\n\n// Hover effect\n//\n// Placed here since it has to come after the potential zebra striping\n\n.table-hover {\n  > tbody > tr:hover {\n    background-color: $table-bg-hover;\n  }\n}\n\n\n// Table cell sizing\n//\n// Reset default table behavior\n\ntable col[class*=\"col-\"] {\n  position: static; // Prevent border hiding in Firefox and IE9-11 (see https://github.com/twbs/bootstrap/issues/11623)\n  float: none;\n  display: table-column;\n}\ntable {\n  td,\n  th {\n    &[class*=\"col-\"] {\n      position: static; // Prevent border hiding in Firefox and IE9-11 (see https://github.com/twbs/bootstrap/issues/11623)\n      float: none;\n      display: table-cell;\n    }\n  }\n}\n\n\n// Table backgrounds\n//\n// Exact selectors below required to override `.table-striped` and prevent\n// inheritance to nested tables.\n\n// Generate the contextual variants\n@include table-row-variant('active', $table-bg-active);\n@include table-row-variant('success', $state-success-bg);\n@include table-row-variant('info', $state-info-bg);\n@include table-row-variant('warning', $state-warning-bg);\n@include table-row-variant('danger', $state-danger-bg);\n\n\n// Responsive tables\n//\n// Wrap your tables in `.table-responsive` and we'll make them mobile friendly\n// by enabling horizontal scrolling. Only applies <768px. Everything above that\n// will display normally.\n\n.table-responsive {\n  overflow-x: auto;\n  min-height: 0.01%; // Workaround for IE9 bug (see https://github.com/twbs/bootstrap/issues/14837)\n\n  @media screen and (max-width: $screen-xs-max) {\n    width: 100%;\n    margin-bottom: ($line-height-computed * 0.75);\n    overflow-y: hidden;\n    -ms-overflow-style: -ms-autohiding-scrollbar;\n    border: 1px solid $table-border-color;\n\n    // Tighten up spacing\n    > .table {\n      margin-bottom: 0;\n\n      // Ensure the content doesn't wrap\n      > thead,\n      > tbody,\n      > tfoot {\n        > tr {\n          > th,\n          > td {\n            white-space: nowrap;\n          }\n        }\n      }\n    }\n\n    // Special overrides for the bordered tables\n    > .table-bordered {\n      border: 0;\n\n      // Nuke the appropriate borders so that the parent can handle them\n      > thead,\n      > tbody,\n      > tfoot {\n        > tr {\n          > th:first-child,\n          > td:first-child {\n            border-left: 0;\n          }\n          > th:last-child,\n          > td:last-child {\n            border-right: 0;\n          }\n        }\n      }\n\n      // Only nuke the last row's bottom-border in `tbody` and `tfoot` since\n      // chances are there will be only one `tr` in a `thead` and that would\n      // remove the border altogether.\n      > tbody,\n      > tfoot {\n        > tr:last-child {\n          > th,\n          > td {\n            border-bottom: 0;\n          }\n        }\n      }\n\n    }\n  }\n}\n", "// Tables\n\n@mixin table-row-variant($state, $background) {\n  // Exact selectors below required to override `.table-striped` and prevent\n  // inheritance to nested tables.\n  .table > thead > tr,\n  .table > tbody > tr,\n  .table > tfoot > tr {\n    > td.#{$state},\n    > th.#{$state},\n    &.#{$state} > td,\n    &.#{$state} > th {\n      background-color: $background;\n    }\n  }\n\n  // Hover states for `.table-hover`\n  // Note: this is not available for cells or rows within `thead` or `tfoot`.\n  .table-hover > tbody > tr {\n    > td.#{$state}:hover,\n    > th.#{$state}:hover,\n    &.#{$state}:hover > td,\n    &:hover > .#{$state},\n    &.#{$state}:hover > th {\n      background-color: darken($background, 5%);\n    }\n  }\n}\n", "//\n// Forms\n// --------------------------------------------------\n\n\n// Normalize non-controls\n//\n// Restyle and baseline non-control form elements.\n\nfieldset {\n  padding: 0;\n  margin: 0;\n  border: 0;\n  // Chrome and Firefox set a `min-width: min-content;` on fieldsets,\n  // so we reset that to ensure it behaves more like a standard block element.\n  // See https://github.com/twbs/bootstrap/issues/12359.\n  min-width: 0;\n}\n\nlegend {\n  display: block;\n  width: 100%;\n  padding: 0;\n  margin-bottom: $line-height-computed;\n  font-size: ($font-size-base * 1.5);\n  line-height: inherit;\n  color: $legend-color;\n  border: 0;\n  border-bottom: 1px solid $legend-border-color;\n}\n\nlabel {\n  display: inline-block;\n  max-width: 100%; // Force IE8 to wrap long content (see https://github.com/twbs/bootstrap/issues/13141)\n  margin-bottom: 5px;\n  font-weight: bold;\n}\n\n\n// Normalize form controls\n//\n// While most of our form styles require extra classes, some basic normalization\n// is required to ensure optimum display with or without those classes to better\n// address browser inconsistencies.\n\n// Override content-box in Normalize (* isn't specific enough)\ninput[type=\"search\"] {\n  @include box-sizing(border-box);\n}\n\n// Position radios and checkboxes better\ninput[type=\"radio\"],\ninput[type=\"checkbox\"] {\n  margin: 4px 0 0;\n  margin-top: 1px \\9; // IE8-9\n  line-height: normal;\n}\n\ninput[type=\"file\"] {\n  display: block;\n}\n\n// Make range inputs behave like textual form controls\ninput[type=\"range\"] {\n  display: block;\n  width: 100%;\n}\n\n// Make multiple select elements height not fixed\nselect[multiple],\nselect[size] {\n  height: auto;\n}\n\n// Focus for file, radio, and checkbox\ninput[type=\"file\"]:focus,\ninput[type=\"radio\"]:focus,\ninput[type=\"checkbox\"]:focus {\n  @include tab-focus;\n}\n\n// Adjust output element\noutput {\n  display: block;\n  padding-top: ($padding-base-vertical + 1);\n  font-size: $font-size-base;\n  line-height: $line-height-base;\n  color: $input-color;\n}\n\n\n// Common form controls\n//\n// Shared size and type resets for form controls. Apply `.form-control` to any\n// of the following form controls:\n//\n// select\n// textarea\n// input[type=\"text\"]\n// input[type=\"password\"]\n// input[type=\"datetime\"]\n// input[type=\"datetime-local\"]\n// input[type=\"date\"]\n// input[type=\"month\"]\n// input[type=\"time\"]\n// input[type=\"week\"]\n// input[type=\"number\"]\n// input[type=\"email\"]\n// input[type=\"url\"]\n// input[type=\"search\"]\n// input[type=\"tel\"]\n// input[type=\"color\"]\n\n.form-control {\n  display: block;\n  width: 100%;\n  height: $input-height-base; // Make inputs at least the height of their button counterpart (base line-height + padding + border)\n  padding: $padding-base-vertical $padding-base-horizontal;\n  font-size: $font-size-base;\n  line-height: $line-height-base;\n  color: $input-color;\n  background-color: $input-bg;\n  background-image: none; // Reset unusual Firefox-on-Android default style; see https://github.com/necolas/normalize.css/issues/214\n  border: 1px solid $input-border;\n  border-radius: $input-border-radius; // Note: This has no effect on <select>s in some browsers, due to the limited stylability of <select>s in CSS.\n  @include box-shadow(inset 0 1px 1px rgba(0,0,0,.075));\n  @include transition(border-color ease-in-out .15s, box-shadow ease-in-out .15s);\n\n  // Customize the `:focus` state to imitate native WebKit styles.\n  @include form-control-focus;\n\n  // Placeholder\n  @include placeholder;\n\n  // Unstyle the caret on `<select>`s in IE10+.\n  &::-ms-expand {\n    border: 0;\n    background-color: transparent;\n  }\n\n  // Disabled and read-only inputs\n  //\n  // HTML5 says that controls under a fieldset > legend:first-child won't be\n  // disabled if the fieldset is disabled. Due to implementation difficulty, we\n  // don't honor that edge case; we style them as disabled anyway.\n  &[disabled],\n  &[readonly],\n  fieldset[disabled] & {\n    background-color: $input-bg-disabled;\n    opacity: 1; // iOS fix for unreadable disabled content; see https://github.com/twbs/bootstrap/issues/11655\n  }\n\n  &[disabled],\n  fieldset[disabled] & {\n    cursor: $cursor-disabled;\n  }\n\n  // [converter] extracted textarea& to textarea.form-control\n}\n\n// Reset height for `textarea`s\ntextarea.form-control {\n  height: auto;\n}\n\n\n// Search inputs in iOS\n//\n// This overrides the extra rounded corners on search inputs in iOS so that our\n// `.form-control` class can properly style them. Note that this cannot simply\n// be added to `.form-control` as it's not specific enough. For details, see\n// https://github.com/twbs/bootstrap/issues/11586.\n\ninput[type=\"search\"] {\n  -webkit-appearance: none;\n}\n\n\n// Special styles for iOS temporal inputs\n//\n// In Mobile Safari, setting `display: block` on temporal inputs causes the\n// text within the input to become vertically misaligned. As a workaround, we\n// set a pixel line-height that matches the given height of the input, but only\n// for Safari. See https://bugs.webkit.org/show_bug.cgi?id=139848\n//\n// Note that as of 9.3, iOS doesn't support `week`.\n\n@media screen and (-webkit-min-device-pixel-ratio: 0) {\n  input[type=\"date\"],\n  input[type=\"time\"],\n  input[type=\"datetime-local\"],\n  input[type=\"month\"] {\n    &.form-control {\n      line-height: $input-height-base;\n    }\n\n    &.input-sm,\n    .input-group-sm & {\n      line-height: $input-height-small;\n    }\n\n    &.input-lg,\n    .input-group-lg & {\n      line-height: $input-height-large;\n    }\n  }\n}\n\n\n// Form groups\n//\n// Designed to help with the organization and spacing of vertical forms. For\n// horizontal forms, use the predefined grid classes.\n\n.form-group {\n  margin-bottom: $form-group-margin-bottom;\n}\n\n\n// Checkboxes and radios\n//\n// Indent the labels to position radios/checkboxes as hanging controls.\n\n.radio,\n.checkbox {\n  position: relative;\n  display: block;\n  margin-top: 10px;\n  margin-bottom: 10px;\n\n  label {\n    min-height: $line-height-computed; // Ensure the input doesn't jump when there is no text\n    padding-left: 20px;\n    margin-bottom: 0;\n    font-weight: normal;\n    cursor: pointer;\n  }\n}\n.radio input[type=\"radio\"],\n.radio-inline input[type=\"radio\"],\n.checkbox input[type=\"checkbox\"],\n.checkbox-inline input[type=\"checkbox\"] {\n  position: absolute;\n  margin-left: -20px;\n  margin-top: 4px \\9;\n}\n\n.radio + .radio,\n.checkbox + .checkbox {\n  margin-top: -5px; // Move up sibling radios or checkboxes for tighter spacing\n}\n\n// Radios and checkboxes on same line\n.radio-inline,\n.checkbox-inline {\n  position: relative;\n  display: inline-block;\n  padding-left: 20px;\n  margin-bottom: 0;\n  vertical-align: middle;\n  font-weight: normal;\n  cursor: pointer;\n}\n.radio-inline + .radio-inline,\n.checkbox-inline + .checkbox-inline {\n  margin-top: 0;\n  margin-left: 10px; // space out consecutive inline controls\n}\n\n// Apply same disabled cursor tweak as for inputs\n// Some special care is needed because <label>s don't inherit their parent's `cursor`.\n//\n// Note: Neither radios nor checkboxes can be readonly.\ninput[type=\"radio\"],\ninput[type=\"checkbox\"] {\n  &[disabled],\n  &.disabled,\n  fieldset[disabled] & {\n    cursor: $cursor-disabled;\n  }\n}\n// These classes are used directly on <label>s\n.radio-inline,\n.checkbox-inline {\n  &.disabled,\n  fieldset[disabled] & {\n    cursor: $cursor-disabled;\n  }\n}\n// These classes are used on elements with <label> descendants\n.radio,\n.checkbox {\n  &.disabled,\n  fieldset[disabled] & {\n    label {\n      cursor: $cursor-disabled;\n    }\n  }\n}\n\n\n// Static form control text\n//\n// Apply class to a `p` element to make any string of text align with labels in\n// a horizontal form layout.\n\n.form-control-static {\n  // Size it appropriately next to real form controls\n  padding-top: ($padding-base-vertical + 1);\n  padding-bottom: ($padding-base-vertical + 1);\n  // Remove default margin from `p`\n  margin-bottom: 0;\n  min-height: ($line-height-computed + $font-size-base);\n\n  &.input-lg,\n  &.input-sm {\n    padding-left: 0;\n    padding-right: 0;\n  }\n}\n\n\n// Form control sizing\n//\n// Build on `.form-control` with modifier classes to decrease or increase the\n// height and font-size of form controls.\n//\n// The `.form-group-* form-control` variations are sadly duplicated to avoid the\n// issue documented in https://github.com/twbs/bootstrap/issues/15074.\n\n@include input-size('.input-sm', $input-height-small, $padding-small-vertical, $padding-small-horizontal, $font-size-small, $line-height-small, $input-border-radius-small);\n.form-group-sm {\n  .form-control {\n    height: $input-height-small;\n    padding: $padding-small-vertical $padding-small-horizontal;\n    font-size: $font-size-small;\n    line-height: $line-height-small;\n    border-radius: $input-border-radius-small;\n  }\n  select.form-control {\n    height: $input-height-small;\n    line-height: $input-height-small;\n  }\n  textarea.form-control,\n  select[multiple].form-control {\n    height: auto;\n  }\n  .form-control-static {\n    height: $input-height-small;\n    min-height: ($line-height-computed + $font-size-small);\n    padding: ($padding-small-vertical + 1) $padding-small-horizontal;\n    font-size: $font-size-small;\n    line-height: $line-height-small;\n  }\n}\n\n@include input-size('.input-lg', $input-height-large, $padding-large-vertical, $padding-large-horizontal, $font-size-large, $line-height-large, $input-border-radius-large);\n.form-group-lg {\n  .form-control {\n    height: $input-height-large;\n    padding: $padding-large-vertical $padding-large-horizontal;\n    font-size: $font-size-large;\n    line-height: $line-height-large;\n    border-radius: $input-border-radius-large;\n  }\n  select.form-control {\n    height: $input-height-large;\n    line-height: $input-height-large;\n  }\n  textarea.form-control,\n  select[multiple].form-control {\n    height: auto;\n  }\n  .form-control-static {\n    height: $input-height-large;\n    min-height: ($line-height-computed + $font-size-large);\n    padding: ($padding-large-vertical + 1) $padding-large-horizontal;\n    font-size: $font-size-large;\n    line-height: $line-height-large;\n  }\n}\n\n\n// Form control feedback states\n//\n// Apply contextual and semantic states to individual form controls.\n\n.has-feedback {\n  // Enable absolute positioning\n  position: relative;\n\n  // Ensure icons don't overlap text\n  .form-control {\n    padding-right: ($input-height-base * 1.25);\n  }\n}\n// Feedback icon (requires .glyphicon classes)\n.form-control-feedback {\n  position: absolute;\n  top: 0;\n  right: 0;\n  z-index: 2; // Ensure icon is above input groups\n  display: block;\n  width: $input-height-base;\n  height: $input-height-base;\n  line-height: $input-height-base;\n  text-align: center;\n  pointer-events: none;\n}\n.input-lg + .form-control-feedback,\n.input-group-lg + .form-control-feedback,\n.form-group-lg .form-control + .form-control-feedback {\n  width: $input-height-large;\n  height: $input-height-large;\n  line-height: $input-height-large;\n}\n.input-sm + .form-control-feedback,\n.input-group-sm + .form-control-feedback,\n.form-group-sm .form-control + .form-control-feedback {\n  width: $input-height-small;\n  height: $input-height-small;\n  line-height: $input-height-small;\n}\n\n// Feedback states\n.has-success {\n  @include form-control-validation($state-success-text, $state-success-text, $state-success-bg);\n}\n.has-warning {\n  @include form-control-validation($state-warning-text, $state-warning-text, $state-warning-bg);\n}\n.has-error {\n  @include form-control-validation($state-danger-text, $state-danger-text, $state-danger-bg);\n}\n\n// Reposition feedback icon if input has visible label above\n.has-feedback label {\n\n  & ~ .form-control-feedback {\n    top: ($line-height-computed + 5); // Height of the `label` and its margin\n  }\n  &.sr-only ~ .form-control-feedback {\n    top: 0;\n  }\n}\n\n\n// Help text\n//\n// Apply to any element you wish to create light text for placement immediately\n// below a form control. Use for general help, formatting, or instructional text.\n\n.help-block {\n  display: block; // account for any element using help-block\n  margin-top: 5px;\n  margin-bottom: 10px;\n  color: lighten($text-color, 25%); // lighten the text some for contrast\n}\n\n\n// Inline forms\n//\n// Make forms appear inline(-block) by adding the `.form-inline` class. Inline\n// forms begin stacked on extra small (mobile) devices and then go inline when\n// viewports reach <768px.\n//\n// Requires wrapping inputs and labels with `.form-group` for proper display of\n// default HTML form controls and our custom form controls (e.g., input groups).\n//\n// Heads up! This is mixin-ed into `.navbar-form` in navbars.less.\n\n// [converter] extracted from `.form-inline` for libsass compatibility\n@mixin form-inline {\n\n  // Kick in the inline\n  @media (min-width: $screen-sm-min) {\n    // Inline-block all the things for \"inline\"\n    .form-group {\n      display: inline-block;\n      margin-bottom: 0;\n      vertical-align: middle;\n    }\n\n    // In navbar-form, allow folks to *not* use `.form-group`\n    .form-control {\n      display: inline-block;\n      width: auto; // Prevent labels from stacking above inputs in `.form-group`\n      vertical-align: middle;\n    }\n\n    // Make static controls behave like regular ones\n    .form-control-static {\n      display: inline-block;\n    }\n\n    .input-group {\n      display: inline-table;\n      vertical-align: middle;\n\n      .input-group-addon,\n      .input-group-btn,\n      .form-control {\n        width: auto;\n      }\n    }\n\n    // Input groups need that 100% width though\n    .input-group > .form-control {\n      width: 100%;\n    }\n\n    .control-label {\n      margin-bottom: 0;\n      vertical-align: middle;\n    }\n\n    // Remove default margin on radios/checkboxes that were used for stacking, and\n    // then undo the floating of radios and checkboxes to match.\n    .radio,\n    .checkbox {\n      display: inline-block;\n      margin-top: 0;\n      margin-bottom: 0;\n      vertical-align: middle;\n\n      label {\n        padding-left: 0;\n      }\n    }\n    .radio input[type=\"radio\"],\n    .checkbox input[type=\"checkbox\"] {\n      position: relative;\n      margin-left: 0;\n    }\n\n    // Re-override the feedback icon.\n    .has-feedback .form-control-feedback {\n      top: 0;\n    }\n  }\n}\n// [converter] extracted as `@mixin form-inline` for libsass compatibility\n.form-inline {\n  @include form-inline;\n}\n\n\n\n// Horizontal forms\n//\n// Horizontal forms are built on grid classes and allow you to create forms with\n// labels on the left and inputs on the right.\n\n.form-horizontal {\n\n  // Consistent vertical alignment of radios and checkboxes\n  //\n  // Labels also get some reset styles, but that is scoped to a media query below.\n  .radio,\n  .checkbox,\n  .radio-inline,\n  .checkbox-inline {\n    margin-top: 0;\n    margin-bottom: 0;\n    padding-top: ($padding-base-vertical + 1); // Default padding plus a border\n  }\n  // Account for padding we're adding to ensure the alignment and of help text\n  // and other content below items\n  .radio,\n  .checkbox {\n    min-height: ($line-height-computed + ($padding-base-vertical + 1));\n  }\n\n  // Make form groups behave like rows\n  .form-group {\n    @include make-row;\n  }\n\n  // Reset spacing and right align labels, but scope to media queries so that\n  // labels on narrow viewports stack the same as a default form example.\n  @media (min-width: $screen-sm-min) {\n    .control-label {\n      text-align: right;\n      margin-bottom: 0;\n      padding-top: ($padding-base-vertical + 1); // Default padding plus a border\n    }\n  }\n\n  // Validation states\n  //\n  // Reposition the icon because it's now within a grid column and columns have\n  // `position: relative;` on them. Also accounts for the grid gutter padding.\n  .has-feedback .form-control-feedback {\n    right: floor(($grid-gutter-width / 2));\n  }\n\n  // Form group sizes\n  //\n  // Quick utility class for applying `.input-lg` and `.input-sm` styles to the\n  // inputs and labels within a `.form-group`.\n  .form-group-lg {\n    @media (min-width: $screen-sm-min) {\n      .control-label {\n        padding-top: ($padding-large-vertical + 1);\n        font-size: $font-size-large;\n      }\n    }\n  }\n  .form-group-sm {\n    @media (min-width: $screen-sm-min) {\n      .control-label {\n        padding-top: ($padding-small-vertical + 1);\n        font-size: $font-size-small;\n      }\n    }\n  }\n}\n", "// Form validation states\n//\n// Used in forms.less to generate the form validation CSS for warnings, errors,\n// and successes.\n\n@mixin form-control-validation($text-color: #555, $border-color: #ccc, $background-color: #f5f5f5) {\n  // Color the label and help text\n  .help-block,\n  .control-label,\n  .radio,\n  .checkbox,\n  .radio-inline,\n  .checkbox-inline,\n  &.radio label,\n  &.checkbox label,\n  &.radio-inline label,\n  &.checkbox-inline label  {\n    color: $text-color;\n  }\n  // Set the border and box shadow on specific inputs to match\n  .form-control {\n    border-color: $border-color;\n    @include box-shadow(inset 0 1px 1px rgba(0,0,0,.075)); // Redeclare so transitions work\n    &:focus {\n      border-color: darken($border-color, 10%);\n      $shadow: inset 0 1px 1px rgba(0,0,0,.075), 0 0 6px lighten($border-color, 20%);\n      @include box-shadow($shadow);\n    }\n  }\n  // Set validation states also for addons\n  .input-group-addon {\n    color: $text-color;\n    border-color: $border-color;\n    background-color: $background-color;\n  }\n  // Optional feedback icon\n  .form-control-feedback {\n    color: $text-color;\n  }\n}\n\n\n// Form control focus state\n//\n// Generate a customized focus state and for any input with the specified color,\n// which defaults to the `$input-border-focus` variable.\n//\n// We highly encourage you to not customize the default value, but instead use\n// this to tweak colors on an as-needed basis. This aesthetic change is based on\n// WebKit's default styles, but applicable to a wider range of browsers. Its\n// usability and accessibility should be taken into account with any change.\n//\n// Example usage: change the default blue border and shadow to white for better\n// contrast against a dark gray background.\n@mixin form-control-focus($color: $input-border-focus) {\n  $color-rgba: rgba(red($color), green($color), blue($color), .6);\n  &:focus {\n    border-color: $color;\n    outline: 0;\n    @include box-shadow(inset 0 1px 1px rgba(0,0,0,.075), 0 0 8px $color-rgba);\n  }\n}\n\n// Form control sizing\n//\n// Relative text size, padding, and border-radii changes for form controls. For\n// horizontal sizing, wrap controls in the predefined grid classes. `<select>`\n// element gets special love because it's special, and that's a fact!\n// [converter] $parent hack\n@mixin input-size($parent, $input-height, $padding-vertical, $padding-horizontal, $font-size, $line-height, $border-radius) {\n  #{$parent} {\n    height: $input-height;\n    padding: $padding-vertical $padding-horizontal;\n    font-size: $font-size;\n    line-height: $line-height;\n    border-radius: $border-radius;\n  }\n\n  select#{$parent} {\n    height: $input-height;\n    line-height: $input-height;\n  }\n\n  textarea#{$parent},\n  select[multiple]#{$parent} {\n    height: auto;\n  }\n}\n", "//\n// Buttons\n// --------------------------------------------------\n\n\n// Base styles\n// --------------------------------------------------\n\n.btn {\n  display: inline-block;\n  margin-bottom: 0; // For input.btn\n  font-weight: $btn-font-weight;\n  text-align: center;\n  vertical-align: middle;\n  touch-action: manipulation;\n  cursor: pointer;\n  background-image: none; // Reset unusual Firefox-on-Android default style; see https://github.com/necolas/normalize.css/issues/214\n  border: 1px solid transparent;\n  white-space: nowrap;\n  @include button-size($padding-base-vertical, $padding-base-horizontal, $font-size-base, $line-height-base, $btn-border-radius-base);\n  @include user-select(none);\n\n  &,\n  &:active,\n  &.active {\n    &:focus,\n    &.focus {\n      @include tab-focus;\n    }\n  }\n\n  &:hover,\n  &:focus,\n  &.focus {\n    color: $btn-default-color;\n    text-decoration: none;\n  }\n\n  &:active,\n  &.active {\n    outline: 0;\n    background-image: none;\n    @include box-shadow(inset 0 3px 5px rgba(0,0,0,.125));\n  }\n\n  &.disabled,\n  &[disabled],\n  fieldset[disabled] & {\n    cursor: $cursor-disabled;\n    @include opacity(.65);\n    @include box-shadow(none);\n  }\n\n  // [converter] extracted a& to a.btn\n}\n\na.btn {\n  &.disabled,\n  fieldset[disabled] & {\n    pointer-events: none; // Future-proof disabling of clicks on `<a>` elements\n  }\n}\n\n\n// Alternate buttons\n// --------------------------------------------------\n\n.btn-default {\n  @include button-variant($btn-default-color, $btn-default-bg, $btn-default-border);\n}\n.btn-primary {\n  @include button-variant($btn-primary-color, $btn-primary-bg, $btn-primary-border);\n}\n// Success appears as green\n.btn-success {\n  @include button-variant($btn-success-color, $btn-success-bg, $btn-success-border);\n}\n// Info appears as blue-green\n.btn-info {\n  @include button-variant($btn-info-color, $btn-info-bg, $btn-info-border);\n}\n// Warning appears as orange\n.btn-warning {\n  @include button-variant($btn-warning-color, $btn-warning-bg, $btn-warning-border);\n}\n// Danger and error appear as red\n.btn-danger {\n  @include button-variant($btn-danger-color, $btn-danger-bg, $btn-danger-border);\n}\n\n\n// Link buttons\n// -------------------------\n\n// Make a button look and behave like a link\n.btn-link {\n  color: $link-color;\n  font-weight: normal;\n  border-radius: 0;\n\n  &,\n  &:active,\n  &.active,\n  &[disabled],\n  fieldset[disabled] & {\n    background-color: transparent;\n    @include box-shadow(none);\n  }\n  &,\n  &:hover,\n  &:focus,\n  &:active {\n    border-color: transparent;\n  }\n  &:hover,\n  &:focus {\n    color: $link-hover-color;\n    text-decoration: $link-hover-decoration;\n    background-color: transparent;\n  }\n  &[disabled],\n  fieldset[disabled] & {\n    &:hover,\n    &:focus {\n      color: $btn-link-disabled-color;\n      text-decoration: none;\n    }\n  }\n}\n\n\n// Button Sizes\n// --------------------------------------------------\n\n.btn-lg {\n  // line-height: ensure even-numbered height of button next to large input\n  @include button-size($padding-large-vertical, $padding-large-horizontal, $font-size-large, $line-height-large, $btn-border-radius-large);\n}\n.btn-sm {\n  // line-height: ensure proper height of button next to small input\n  @include button-size($padding-small-vertical, $padding-small-horizontal, $font-size-small, $line-height-small, $btn-border-radius-small);\n}\n.btn-xs {\n  @include button-size($padding-xs-vertical, $padding-xs-horizontal, $font-size-small, $line-height-small, $btn-border-radius-small);\n}\n\n\n// Block button\n// --------------------------------------------------\n\n.btn-block {\n  display: block;\n  width: 100%;\n}\n\n// Vertically space out multiple block buttons\n.btn-block + .btn-block {\n  margin-top: 5px;\n}\n\n// Specificity overrides\ninput[type=\"submit\"],\ninput[type=\"reset\"],\ninput[type=\"button\"] {\n  &.btn-block {\n    width: 100%;\n  }\n}\n", "// Button variants\n//\n// Easily pump out default styles, as well as :hover, :focus, :active,\n// and disabled options for all buttons\n\n@mixin button-variant($color, $background, $border) {\n  color: $color;\n  background-color: $background;\n  border-color: $border;\n\n  &:focus,\n  &.focus {\n    color: $color;\n    background-color: darken($background, 10%);\n        border-color: darken($border, 25%);\n  }\n  &:hover {\n    color: $color;\n    background-color: darken($background, 10%);\n        border-color: darken($border, 12%);\n  }\n  &:active,\n  &.active,\n  .open > &.dropdown-toggle {\n    color: $color;\n    background-color: darken($background, 10%);\n        border-color: darken($border, 12%);\n\n    &:hover,\n    &:focus,\n    &.focus {\n      color: $color;\n      background-color: darken($background, 17%);\n          border-color: darken($border, 25%);\n    }\n  }\n  &:active,\n  &.active,\n  .open > &.dropdown-toggle {\n    background-image: none;\n  }\n  &.disabled,\n  &[disabled],\n  fieldset[disabled] & {\n    &:hover,\n    &:focus,\n    &.focus {\n      background-color: $background;\n          border-color: $border;\n    }\n  }\n\n  .badge {\n    color: $background;\n    background-color: $color;\n  }\n}\n\n// Button sizes\n@mixin button-size($padding-vertical, $padding-horizontal, $font-size, $line-height, $border-radius) {\n  padding: $padding-vertical $padding-horizontal;\n  font-size: $font-size;\n  line-height: $line-height;\n  border-radius: $border-radius;\n}\n", "// Opacity\n\n@mixin opacity($opacity) {\n  opacity: $opacity;\n  // IE8 filter\n  $opacity-ie: ($opacity * 100);\n  filter: alpha(opacity=$opacity-ie);\n}\n", "//\n// Component animations\n// --------------------------------------------------\n\n// Heads up!\n//\n// We don't use the `.opacity()` mixin here since it causes a bug with text\n// fields in IE7-8. Source: https://github.com/twbs/bootstrap/pull/3552.\n\n.fade {\n  opacity: 0;\n  @include transition(opacity .15s linear);\n  &.in {\n    opacity: 1;\n  }\n}\n\n.collapse {\n  display: none;\n\n  &.in      { display: block; }\n  // [converter] extracted tr&.in to tr.collapse.in\n  // [converter] extracted tbody&.in to tbody.collapse.in\n}\n\ntr.collapse.in    { display: table-row; }\n\ntbody.collapse.in { display: table-row-group; }\n\n.collapsing {\n  position: relative;\n  height: 0;\n  overflow: hidden;\n  @include transition-property(height, visibility);\n  @include transition-duration(.35s);\n  @include transition-timing-function(ease);\n}\n", "//\n// Dropdown menus\n// --------------------------------------------------\n\n\n// Dropdown arrow/caret\n.caret {\n  display: inline-block;\n  width: 0;\n  height: 0;\n  margin-left: 2px;\n  vertical-align: middle;\n  border-top:   $caret-width-base dashed;\n  border-top:   $caret-width-base solid \\9; // IE8\n  border-right: $caret-width-base solid transparent;\n  border-left:  $caret-width-base solid transparent;\n}\n\n// The dropdown wrapper (div)\n.dropup,\n.dropdown {\n  position: relative;\n}\n\n// Prevent the focus on the dropdown toggle when closing dropdowns\n.dropdown-toggle:focus {\n  outline: 0;\n}\n\n// The dropdown menu (ul)\n.dropdown-menu {\n  position: absolute;\n  top: 100%;\n  left: 0;\n  z-index: $zindex-dropdown;\n  display: none; // none by default, but block on \"open\" of the menu\n  float: left;\n  min-width: 160px;\n  padding: 5px 0;\n  margin: 2px 0 0; // override default ul\n  list-style: none;\n  font-size: $font-size-base;\n  text-align: left; // Ensures proper alignment if parent has it changed (e.g., modal footer)\n  background-color: $dropdown-bg;\n  border: 1px solid $dropdown-fallback-border; // IE8 fallback\n  border: 1px solid $dropdown-border;\n  border-radius: $border-radius-base;\n  @include box-shadow(0 6px 12px rgba(0,0,0,.175));\n  background-clip: padding-box;\n\n  // Aligns the dropdown menu to right\n  //\n  // Deprecated as of 3.1.0 in favor of `.dropdown-menu-[dir]`\n  &.pull-right {\n    right: 0;\n    left: auto;\n  }\n\n  // Dividers (basically an hr) within the dropdown\n  .divider {\n    @include nav-divider($dropdown-divider-bg);\n  }\n\n  // Links within the dropdown menu\n  > li > a {\n    display: block;\n    padding: 3px 20px;\n    clear: both;\n    font-weight: normal;\n    line-height: $line-height-base;\n    color: $dropdown-link-color;\n    white-space: nowrap; // prevent links from randomly breaking onto new lines\n  }\n}\n\n// Hover/Focus state\n.dropdown-menu > li > a {\n  &:hover,\n  &:focus {\n    text-decoration: none;\n    color: $dropdown-link-hover-color;\n    background-color: $dropdown-link-hover-bg;\n  }\n}\n\n// Active state\n.dropdown-menu > .active > a {\n  &,\n  &:hover,\n  &:focus {\n    color: $dropdown-link-active-color;\n    text-decoration: none;\n    outline: 0;\n    background-color: $dropdown-link-active-bg;\n  }\n}\n\n// Disabled state\n//\n// Gray out text and ensure the hover/focus state remains gray\n\n.dropdown-menu > .disabled > a {\n  &,\n  &:hover,\n  &:focus {\n    color: $dropdown-link-disabled-color;\n  }\n\n  // Nuke hover/focus effects\n  &:hover,\n  &:focus {\n    text-decoration: none;\n    background-color: transparent;\n    background-image: none; // Remove CSS gradient\n    @include reset-filter;\n    cursor: $cursor-disabled;\n  }\n}\n\n// Open state for the dropdown\n.open {\n  // Show the menu\n  > .dropdown-menu {\n    display: block;\n  }\n\n  // Remove the outline when :focus is triggered\n  > a {\n    outline: 0;\n  }\n}\n\n// Menu positioning\n//\n// Add extra class to `.dropdown-menu` to flip the alignment of the dropdown\n// menu with the parent.\n.dropdown-menu-right {\n  left: auto; // Reset the default from `.dropdown-menu`\n  right: 0;\n}\n// With v3, we enabled auto-flipping if you have a dropdown within a right\n// aligned nav component. To enable the undoing of that, we provide an override\n// to restore the default dropdown menu alignment.\n//\n// This is only for left-aligning a dropdown menu within a `.navbar-right` or\n// `.pull-right` nav component.\n.dropdown-menu-left {\n  left: 0;\n  right: auto;\n}\n\n// Dropdown section headers\n.dropdown-header {\n  display: block;\n  padding: 3px 20px;\n  font-size: $font-size-small;\n  line-height: $line-height-base;\n  color: $dropdown-header-color;\n  white-space: nowrap; // as with > li > a\n}\n\n// Backdrop to catch body clicks on mobile, etc.\n.dropdown-backdrop {\n  position: fixed;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  top: 0;\n  z-index: ($zindex-dropdown - 10);\n}\n\n// Right aligned dropdowns\n.pull-right > .dropdown-menu {\n  right: 0;\n  left: auto;\n}\n\n// Allow for dropdowns to go bottom up (aka, dropup-menu)\n//\n// Just add .dropup after the standard .dropdown class and you're set, bro.\n// TODO: abstract this so that the navbar fixed styles are not placed here?\n\n.dropup,\n.navbar-fixed-bottom .dropdown {\n  // Reverse the caret\n  .caret {\n    border-top: 0;\n    border-bottom: $caret-width-base dashed;\n    border-bottom: $caret-width-base solid \\9; // IE8\n    content: \"\";\n  }\n  // Different positioning for bottom up menu\n  .dropdown-menu {\n    top: auto;\n    bottom: 100%;\n    margin-bottom: 2px;\n  }\n}\n\n\n// Component alignment\n//\n// Reiterate per navbar.less and the modified component alignment there.\n\n@media (min-width: $grid-float-breakpoint) {\n  .navbar-right {\n    .dropdown-menu {\n      right: 0; left: auto;\n    }\n    // Necessary for overrides of the default right aligned menu.\n    // Will remove come v4 in all likelihood.\n    .dropdown-menu-left {\n      left: 0; right: auto;\n    }\n  }\n}\n", "// Horizontal dividers\n//\n// Dividers (basically an hr) within dropdowns and nav lists\n\n@mixin nav-divider($color: #e5e5e5) {\n  height: 1px;\n  margin: (($line-height-computed / 2) - 1) 0;\n  overflow: hidden;\n  background-color: $color;\n}\n", "// Reset filters for IE\n//\n// When you need to remove a gradient background, do not forget to use this to reset\n// the IE filter for IE9 and below.\n\n@mixin reset-filter() {\n  filter: progid:DXImageTransform.Microsoft.gradient(enabled = false);\n}\n", "//\n// Button groups\n// --------------------------------------------------\n\n// Make the div behave like a button\n.btn-group,\n.btn-group-vertical {\n  position: relative;\n  display: inline-block;\n  vertical-align: middle; // match .btn alignment given font-size hack above\n  > .btn {\n    position: relative;\n    float: left;\n    // Bring the \"active\" button to the front\n    &:hover,\n    &:focus,\n    &:active,\n    &.active {\n      z-index: 2;\n    }\n  }\n}\n\n// Prevent double borders when buttons are next to each other\n.btn-group {\n  .btn + .btn,\n  .btn + .btn-group,\n  .btn-group + .btn,\n  .btn-group + .btn-group {\n    margin-left: -1px;\n  }\n}\n\n// Optional: Group multiple button groups together for a toolbar\n.btn-toolbar {\n  margin-left: -5px; // Offset the first child's margin\n  @include clearfix;\n\n  .btn,\n  .btn-group,\n  .input-group {\n    float: left;\n  }\n  > .btn,\n  > .btn-group,\n  > .input-group {\n    margin-left: 5px;\n  }\n}\n\n.btn-group > .btn:not(:first-child):not(:last-child):not(.dropdown-toggle) {\n  border-radius: 0;\n}\n\n// Set corners individual because sometimes a single button can be in a .btn-group and we need :first-child and :last-child to both match\n.btn-group > .btn:first-child {\n  margin-left: 0;\n  &:not(:last-child):not(.dropdown-toggle) {\n    @include border-right-radius(0);\n  }\n}\n// Need .dropdown-toggle since :last-child doesn't apply, given that a .dropdown-menu is used immediately after it\n.btn-group > .btn:last-child:not(:first-child),\n.btn-group > .dropdown-toggle:not(:first-child) {\n  @include border-left-radius(0);\n}\n\n// Custom edits for including btn-groups within btn-groups (useful for including dropdown buttons within a btn-group)\n.btn-group > .btn-group {\n  float: left;\n}\n.btn-group > .btn-group:not(:first-child):not(:last-child) > .btn {\n  border-radius: 0;\n}\n.btn-group > .btn-group:first-child:not(:last-child) {\n  > .btn:last-child,\n  > .dropdown-toggle {\n    @include border-right-radius(0);\n  }\n}\n.btn-group > .btn-group:last-child:not(:first-child) > .btn:first-child {\n  @include border-left-radius(0);\n}\n\n// On active and open, don't show outline\n.btn-group .dropdown-toggle:active,\n.btn-group.open .dropdown-toggle {\n  outline: 0;\n}\n\n\n// Sizing\n//\n// Remix the default button sizing classes into new ones for easier manipulation.\n\n.btn-group-xs > .btn { @extend .btn-xs; }\n.btn-group-sm > .btn { @extend .btn-sm; }\n.btn-group-lg > .btn { @extend .btn-lg; }\n\n\n// Split button dropdowns\n// ----------------------\n\n// Give the line between buttons some depth\n.btn-group > .btn + .dropdown-toggle {\n  padding-left: 8px;\n  padding-right: 8px;\n}\n.btn-group > .btn-lg + .dropdown-toggle {\n  padding-left: 12px;\n  padding-right: 12px;\n}\n\n// The clickable button for toggling the menu\n// Remove the gradient and set the same inset shadow as the :active state\n.btn-group.open .dropdown-toggle {\n  @include box-shadow(inset 0 3px 5px rgba(0,0,0,.125));\n\n  // Show no shadow for `.btn-link` since it has no other button styles.\n  &.btn-link {\n    @include box-shadow(none);\n  }\n}\n\n\n// Reposition the caret\n.btn .caret {\n  margin-left: 0;\n}\n// Carets in other button sizes\n.btn-lg .caret {\n  border-width: $caret-width-large $caret-width-large 0;\n  border-bottom-width: 0;\n}\n// Upside down carets for .dropup\n.dropup .btn-lg .caret {\n  border-width: 0 $caret-width-large $caret-width-large;\n}\n\n\n// Vertical button groups\n// ----------------------\n\n.btn-group-vertical {\n  > .btn,\n  > .btn-group,\n  > .btn-group > .btn {\n    display: block;\n    float: none;\n    width: 100%;\n    max-width: 100%;\n  }\n\n  // Clear floats so dropdown menus can be properly placed\n  > .btn-group {\n    @include clearfix;\n    > .btn {\n      float: none;\n    }\n  }\n\n  > .btn + .btn,\n  > .btn + .btn-group,\n  > .btn-group + .btn,\n  > .btn-group + .btn-group {\n    margin-top: -1px;\n    margin-left: 0;\n  }\n}\n\n.btn-group-vertical > .btn {\n  &:not(:first-child):not(:last-child) {\n    border-radius: 0;\n  }\n  &:first-child:not(:last-child) {\n    @include border-top-radius($btn-border-radius-base);\n    @include border-bottom-radius(0);\n  }\n  &:last-child:not(:first-child) {\n    @include border-top-radius(0);\n    @include border-bottom-radius($btn-border-radius-base);\n  }\n}\n.btn-group-vertical > .btn-group:not(:first-child):not(:last-child) > .btn {\n  border-radius: 0;\n}\n.btn-group-vertical > .btn-group:first-child:not(:last-child) {\n  > .btn:last-child,\n  > .dropdown-toggle {\n    @include border-bottom-radius(0);\n  }\n}\n.btn-group-vertical > .btn-group:last-child:not(:first-child) > .btn:first-child {\n  @include border-top-radius(0);\n}\n\n\n// Justified button groups\n// ----------------------\n\n.btn-group-justified {\n  display: table;\n  width: 100%;\n  table-layout: fixed;\n  border-collapse: separate;\n  > .btn,\n  > .btn-group {\n    float: none;\n    display: table-cell;\n    width: 1%;\n  }\n  > .btn-group .btn {\n    width: 100%;\n  }\n\n  > .btn-group .dropdown-menu {\n    left: auto;\n  }\n}\n\n\n// Checkbox and radio options\n//\n// In order to support the browser's form validation feedback, powered by the\n// `required` attribute, we have to \"hide\" the inputs via `clip`. We cannot use\n// `display: none;` or `visibility: hidden;` as that also hides the popover.\n// Simply visually hiding the inputs via `opacity` would leave them clickable in\n// certain cases which is prevented by using `clip` and `pointer-events`.\n// This way, we ensure a DOM element is visible to position the popover from.\n//\n// See https://github.com/twbs/bootstrap/pull/12794 and\n// https://github.com/twbs/bootstrap/pull/14559 for more information.\n\n[data-toggle=\"buttons\"] {\n  > .btn,\n  > .btn-group > .btn {\n    input[type=\"radio\"],\n    input[type=\"checkbox\"] {\n      position: absolute;\n      clip: rect(0,0,0,0);\n      pointer-events: none;\n    }\n  }\n}\n", "// Single side border-radius\n\n@mixin border-top-radius($radius) {\n  border-top-right-radius: $radius;\n   border-top-left-radius: $radius;\n}\n@mixin border-right-radius($radius) {\n  border-bottom-right-radius: $radius;\n     border-top-right-radius: $radius;\n}\n@mixin border-bottom-radius($radius) {\n  border-bottom-right-radius: $radius;\n   border-bottom-left-radius: $radius;\n}\n@mixin border-left-radius($radius) {\n  border-bottom-left-radius: $radius;\n     border-top-left-radius: $radius;\n}\n", "//\n// Input groups\n// --------------------------------------------------\n\n// Base styles\n// -------------------------\n.input-group {\n  position: relative; // For dropdowns\n  display: table;\n  border-collapse: separate; // prevent input groups from inheriting border styles from table cells when placed within a table\n\n  // Undo padding and float of grid classes\n  &[class*=\"col-\"] {\n    float: none;\n    padding-left: 0;\n    padding-right: 0;\n  }\n\n  .form-control {\n    // Ensure that the input is always above the *appended* addon button for\n    // proper border colors.\n    position: relative;\n    z-index: 2;\n\n    // IE9 fubars the placeholder attribute in text inputs and the arrows on\n    // select elements in input groups. To fix it, we float the input. Details:\n    // https://github.com/twbs/bootstrap/issues/11561#issuecomment-28936855\n    float: left;\n\n    width: 100%;\n    margin-bottom: 0;\n\n    &:focus {\n      z-index: 3;\n    }\n  }\n}\n\n// Sizing options\n//\n// Remix the default form control sizing classes into new ones for easier\n// manipulation.\n\n.input-group-lg > .form-control,\n.input-group-lg > .input-group-addon,\n.input-group-lg > .input-group-btn > .btn {\n  @extend .input-lg;\n}\n.input-group-sm > .form-control,\n.input-group-sm > .input-group-addon,\n.input-group-sm > .input-group-btn > .btn {\n  @extend .input-sm;\n}\n\n\n// Display as table-cell\n// -------------------------\n.input-group-addon,\n.input-group-btn,\n.input-group .form-control {\n  display: table-cell;\n\n  &:not(:first-child):not(:last-child) {\n    border-radius: 0;\n  }\n}\n// Addon and addon wrapper for buttons\n.input-group-addon,\n.input-group-btn {\n  width: 1%;\n  white-space: nowrap;\n  vertical-align: middle; // Match the inputs\n}\n\n// Text input groups\n// -------------------------\n.input-group-addon {\n  padding: $padding-base-vertical $padding-base-horizontal;\n  font-size: $font-size-base;\n  font-weight: normal;\n  line-height: 1;\n  color: $input-color;\n  text-align: center;\n  background-color: $input-group-addon-bg;\n  border: 1px solid $input-group-addon-border-color;\n  border-radius: $input-border-radius;\n\n  // Sizing\n  &.input-sm {\n    padding: $padding-small-vertical $padding-small-horizontal;\n    font-size: $font-size-small;\n    border-radius: $input-border-radius-small;\n  }\n  &.input-lg {\n    padding: $padding-large-vertical $padding-large-horizontal;\n    font-size: $font-size-large;\n    border-radius: $input-border-radius-large;\n  }\n\n  // Nuke default margins from checkboxes and radios to vertically center within.\n  input[type=\"radio\"],\n  input[type=\"checkbox\"] {\n    margin-top: 0;\n  }\n}\n\n// Reset rounded corners\n.input-group .form-control:first-child,\n.input-group-addon:first-child,\n.input-group-btn:first-child > .btn,\n.input-group-btn:first-child > .btn-group > .btn,\n.input-group-btn:first-child > .dropdown-toggle,\n.input-group-btn:last-child > .btn:not(:last-child):not(.dropdown-toggle),\n.input-group-btn:last-child > .btn-group:not(:last-child) > .btn {\n  @include border-right-radius(0);\n}\n.input-group-addon:first-child {\n  border-right: 0;\n}\n.input-group .form-control:last-child,\n.input-group-addon:last-child,\n.input-group-btn:last-child > .btn,\n.input-group-btn:last-child > .btn-group > .btn,\n.input-group-btn:last-child > .dropdown-toggle,\n.input-group-btn:first-child > .btn:not(:first-child),\n.input-group-btn:first-child > .btn-group:not(:first-child) > .btn {\n  @include border-left-radius(0);\n}\n.input-group-addon:last-child {\n  border-left: 0;\n}\n\n// Button input groups\n// -------------------------\n.input-group-btn {\n  position: relative;\n  // Jankily prevent input button groups from wrapping with `white-space` and\n  // `font-size` in combination with `inline-block` on buttons.\n  font-size: 0;\n  white-space: nowrap;\n\n  // Negative margin for spacing, position for bringing hovered/focused/actived\n  // element above the siblings.\n  > .btn {\n    position: relative;\n    + .btn {\n      margin-left: -1px;\n    }\n    // Bring the \"active\" button to the front\n    &:hover,\n    &:focus,\n    &:active {\n      z-index: 2;\n    }\n  }\n\n  // Negative margin to only have a 1px border between the two\n  &:first-child {\n    > .btn,\n    > .btn-group {\n      margin-right: -1px;\n    }\n  }\n  &:last-child {\n    > .btn,\n    > .btn-group {\n      z-index: 2;\n      margin-left: -1px;\n    }\n  }\n}\n", "//\n// Navs\n// --------------------------------------------------\n\n\n// Base class\n// --------------------------------------------------\n\n.nav {\n  margin-bottom: 0;\n  padding-left: 0; // Override default ul/ol\n  list-style: none;\n  @include clearfix;\n\n  > li {\n    position: relative;\n    display: block;\n\n    > a {\n      position: relative;\n      display: block;\n      padding: $nav-link-padding;\n      &:hover,\n      &:focus {\n        text-decoration: none;\n        background-color: $nav-link-hover-bg;\n      }\n    }\n\n    // Disabled state sets text to gray and nukes hover/tab effects\n    &.disabled > a {\n      color: $nav-disabled-link-color;\n\n      &:hover,\n      &:focus {\n        color: $nav-disabled-link-hover-color;\n        text-decoration: none;\n        background-color: transparent;\n        cursor: $cursor-disabled;\n      }\n    }\n  }\n\n  // Open dropdowns\n  .open > a {\n    &,\n    &:hover,\n    &:focus {\n      background-color: $nav-link-hover-bg;\n      border-color: $link-color;\n    }\n  }\n\n  // Nav dividers (deprecated with v3.0.1)\n  //\n  // This should have been removed in v3 with the dropping of `.nav-list`, but\n  // we missed it. We don't currently support this anywhere, but in the interest\n  // of maintaining backward compatibility in case you use it, it's deprecated.\n  .nav-divider {\n    @include nav-divider;\n  }\n\n  // Prevent IE8 from misplacing imgs\n  //\n  // See https://github.com/h5bp/html5-boilerplate/issues/984#issuecomment-3985989\n  > li > a > img {\n    max-width: none;\n  }\n}\n\n\n// Tabs\n// -------------------------\n\n// Give the tabs something to sit on\n.nav-tabs {\n  border-bottom: 1px solid $nav-tabs-border-color;\n  > li {\n    float: left;\n    // Make the list-items overlay the bottom border\n    margin-bottom: -1px;\n\n    // Actual tabs (as links)\n    > a {\n      margin-right: 2px;\n      line-height: $line-height-base;\n      border: 1px solid transparent;\n      border-radius: $border-radius-base $border-radius-base 0 0;\n      &:hover {\n        border-color: $nav-tabs-link-hover-border-color $nav-tabs-link-hover-border-color $nav-tabs-border-color;\n      }\n    }\n\n    // Active state, and its :hover to override normal :hover\n    &.active > a {\n      &,\n      &:hover,\n      &:focus {\n        color: $nav-tabs-active-link-hover-color;\n        background-color: $nav-tabs-active-link-hover-bg;\n        border: 1px solid $nav-tabs-active-link-hover-border-color;\n        border-bottom-color: transparent;\n        cursor: default;\n      }\n    }\n  }\n  // pulling this in mainly for less shorthand\n  &.nav-justified {\n    @extend .nav-justified;\n    @extend .nav-tabs-justified;\n  }\n}\n\n\n// Pills\n// -------------------------\n.nav-pills {\n  > li {\n    float: left;\n\n    // Links rendered as pills\n    > a {\n      border-radius: $nav-pills-border-radius;\n    }\n    + li {\n      margin-left: 2px;\n    }\n\n    // Active state\n    &.active > a {\n      &,\n      &:hover,\n      &:focus {\n        color: $nav-pills-active-link-hover-color;\n        background-color: $nav-pills-active-link-hover-bg;\n      }\n    }\n  }\n}\n\n\n// Stacked pills\n.nav-stacked {\n  > li {\n    float: none;\n    + li {\n      margin-top: 2px;\n      margin-left: 0; // no need for this gap between nav items\n    }\n  }\n}\n\n\n// Nav variations\n// --------------------------------------------------\n\n// Justified nav links\n// -------------------------\n\n.nav-justified {\n  width: 100%;\n\n  > li {\n    float: none;\n    > a {\n      text-align: center;\n      margin-bottom: 5px;\n    }\n  }\n\n  > .dropdown .dropdown-menu {\n    top: auto;\n    left: auto;\n  }\n\n  @media (min-width: $screen-sm-min) {\n    > li {\n      display: table-cell;\n      width: 1%;\n      > a {\n        margin-bottom: 0;\n      }\n    }\n  }\n}\n\n// Move borders to anchors instead of bottom of list\n//\n// Mixin for adding on top the shared `.nav-justified` styles for our tabs\n.nav-tabs-justified {\n  border-bottom: 0;\n\n  > li > a {\n    // Override margin from .nav-tabs\n    margin-right: 0;\n    border-radius: $border-radius-base;\n  }\n\n  > .active > a,\n  > .active > a:hover,\n  > .active > a:focus {\n    border: 1px solid $nav-tabs-justified-link-border-color;\n  }\n\n  @media (min-width: $screen-sm-min) {\n    > li > a {\n      border-bottom: 1px solid $nav-tabs-justified-link-border-color;\n      border-radius: $border-radius-base $border-radius-base 0 0;\n    }\n    > .active > a,\n    > .active > a:hover,\n    > .active > a:focus {\n      border-bottom-color: $nav-tabs-justified-active-link-border-color;\n    }\n  }\n}\n\n\n// Tabbable tabs\n// -------------------------\n\n// Hide tabbable panes to start, show them when `.active`\n.tab-content {\n  > .tab-pane {\n    display: none;\n  }\n  > .active {\n    display: block;\n  }\n}\n\n\n// Dropdowns\n// -------------------------\n\n// Specific dropdowns\n.nav-tabs .dropdown-menu {\n  // make dropdown border overlap tab border\n  margin-top: -1px;\n  // Remove the top rounded corners here since there is a hard edge above the menu\n  @include border-top-radius(0);\n}\n", "//\n// Navbars\n// --------------------------------------------------\n\n\n// Wrapper and base class\n//\n// Provide a static navbar from which we expand to create full-width, fixed, and\n// other navbar variations.\n\n.navbar {\n  position: relative;\n  min-height: $navbar-height; // Ensure a navbar always shows (e.g., without a .navbar-brand in collapsed mode)\n  margin-bottom: $navbar-margin-bottom;\n  border: 1px solid transparent;\n\n  // Prevent floats from breaking the navbar\n  @include clearfix;\n\n  @media (min-width: $grid-float-breakpoint) {\n    border-radius: $navbar-border-radius;\n  }\n}\n\n\n// Navbar heading\n//\n// Groups `.navbar-brand` and `.navbar-toggle` into a single component for easy\n// styling of responsive aspects.\n\n.navbar-header {\n  @include clearfix;\n\n  @media (min-width: $grid-float-breakpoint) {\n    float: left;\n  }\n}\n\n\n// Navbar collapse (body)\n//\n// Group your navbar content into this for easy collapsing and expanding across\n// various device sizes. By default, this content is collapsed when <768px, but\n// will expand past that for a horizontal display.\n//\n// To start (on mobile devices) the navbar links, forms, and buttons are stacked\n// vertically and include a `max-height` to overflow in case you have too much\n// content for the user's viewport.\n\n.navbar-collapse {\n  overflow-x: visible;\n  padding-right: $navbar-padding-horizontal;\n  padding-left:  $navbar-padding-horizontal;\n  border-top: 1px solid transparent;\n  box-shadow: inset 0 1px 0 rgba(255,255,255,.1);\n  @include clearfix;\n  -webkit-overflow-scrolling: touch;\n\n  &.in {\n    overflow-y: auto;\n  }\n\n  @media (min-width: $grid-float-breakpoint) {\n    width: auto;\n    border-top: 0;\n    box-shadow: none;\n\n    &.collapse {\n      display: block !important;\n      height: auto !important;\n      padding-bottom: 0; // Override default setting\n      overflow: visible !important;\n    }\n\n    &.in {\n      overflow-y: visible;\n    }\n\n    // Undo the collapse side padding for navbars with containers to ensure\n    // alignment of right-aligned contents.\n    .navbar-fixed-top &,\n    .navbar-static-top &,\n    .navbar-fixed-bottom & {\n      padding-left: 0;\n      padding-right: 0;\n    }\n  }\n}\n\n.navbar-fixed-top,\n.navbar-fixed-bottom {\n  .navbar-collapse {\n    max-height: $navbar-collapse-max-height;\n\n    @media (max-device-width: $screen-xs-min) and (orientation: landscape) {\n      max-height: 200px;\n    }\n  }\n}\n\n\n// Both navbar header and collapse\n//\n// When a container is present, change the behavior of the header and collapse.\n\n.container,\n.container-fluid {\n  > .navbar-header,\n  > .navbar-collapse {\n    margin-right: -$navbar-padding-horizontal;\n    margin-left:  -$navbar-padding-horizontal;\n\n    @media (min-width: $grid-float-breakpoint) {\n      margin-right: 0;\n      margin-left:  0;\n    }\n  }\n}\n\n\n//\n// Navbar alignment options\n//\n// Display the navbar across the entirety of the page or fixed it to the top or\n// bottom of the page.\n\n// Static top (unfixed, but 100% wide) navbar\n.navbar-static-top {\n  z-index: $zindex-navbar;\n  border-width: 0 0 1px;\n\n  @media (min-width: $grid-float-breakpoint) {\n    border-radius: 0;\n  }\n}\n\n// Fix the top/bottom navbars when screen real estate supports it\n.navbar-fixed-top,\n.navbar-fixed-bottom {\n  position: fixed;\n  right: 0;\n  left: 0;\n  z-index: $zindex-navbar-fixed;\n\n  // Undo the rounded corners\n  @media (min-width: $grid-float-breakpoint) {\n    border-radius: 0;\n  }\n}\n.navbar-fixed-top {\n  top: 0;\n  border-width: 0 0 1px;\n}\n.navbar-fixed-bottom {\n  bottom: 0;\n  margin-bottom: 0; // override .navbar defaults\n  border-width: 1px 0 0;\n}\n\n\n// Brand/project name\n\n.navbar-brand {\n  float: left;\n  padding: $navbar-padding-vertical $navbar-padding-horizontal;\n  font-size: $font-size-large;\n  line-height: $line-height-computed;\n  height: $navbar-height;\n\n  &:hover,\n  &:focus {\n    text-decoration: none;\n  }\n\n  > img {\n    display: block;\n  }\n\n  @media (min-width: $grid-float-breakpoint) {\n    .navbar > .container &,\n    .navbar > .container-fluid & {\n      margin-left: -$navbar-padding-horizontal;\n    }\n  }\n}\n\n\n// Navbar toggle\n//\n// Custom button for toggling the `.navbar-collapse`, powered by the collapse\n// JavaScript plugin.\n\n.navbar-toggle {\n  position: relative;\n  float: right;\n  margin-right: $navbar-padding-horizontal;\n  padding: 9px 10px;\n  @include navbar-vertical-align(34px);\n  background-color: transparent;\n  background-image: none; // Reset unusual Firefox-on-Android default style; see https://github.com/necolas/normalize.css/issues/214\n  border: 1px solid transparent;\n  border-radius: $border-radius-base;\n\n  // We remove the `outline` here, but later compensate by attaching `:hover`\n  // styles to `:focus`.\n  &:focus {\n    outline: 0;\n  }\n\n  // Bars\n  .icon-bar {\n    display: block;\n    width: 22px;\n    height: 2px;\n    border-radius: 1px;\n  }\n  .icon-bar + .icon-bar {\n    margin-top: 4px;\n  }\n\n  @media (min-width: $grid-float-breakpoint) {\n    display: none;\n  }\n}\n\n\n// Navbar nav links\n//\n// Builds on top of the `.nav` components with its own modifier class to make\n// the nav the full height of the horizontal nav (above 768px).\n\n.navbar-nav {\n  margin: ($navbar-padding-vertical / 2) (-$navbar-padding-horizontal);\n\n  > li > a {\n    padding-top:    10px;\n    padding-bottom: 10px;\n    line-height: $line-height-computed;\n  }\n\n  @media (max-width: $grid-float-breakpoint-max) {\n    // Dropdowns get custom display when collapsed\n    .open .dropdown-menu {\n      position: static;\n      float: none;\n      width: auto;\n      margin-top: 0;\n      background-color: transparent;\n      border: 0;\n      box-shadow: none;\n      > li > a,\n      .dropdown-header {\n        padding: 5px 15px 5px 25px;\n      }\n      > li > a {\n        line-height: $line-height-computed;\n        &:hover,\n        &:focus {\n          background-image: none;\n        }\n      }\n    }\n  }\n\n  // Uncollapse the nav\n  @media (min-width: $grid-float-breakpoint) {\n    float: left;\n    margin: 0;\n\n    > li {\n      float: left;\n      > a {\n        padding-top:    $navbar-padding-vertical;\n        padding-bottom: $navbar-padding-vertical;\n      }\n    }\n  }\n}\n\n\n// Navbar form\n//\n// Extension of the `.form-inline` with some extra flavor for optimum display in\n// our navbars.\n\n.navbar-form {\n  margin-left: -$navbar-padding-horizontal;\n  margin-right: -$navbar-padding-horizontal;\n  padding: 10px $navbar-padding-horizontal;\n  border-top: 1px solid transparent;\n  border-bottom: 1px solid transparent;\n  $shadow: inset 0 1px 0 rgba(255,255,255,.1), 0 1px 0 rgba(255,255,255,.1);\n  @include box-shadow($shadow);\n\n  // Mixin behavior for optimum display\n  @include form-inline;\n\n  .form-group {\n    @media (max-width: $grid-float-breakpoint-max) {\n      margin-bottom: 5px;\n\n      &:last-child {\n        margin-bottom: 0;\n      }\n    }\n  }\n\n  // Vertically center in expanded, horizontal navbar\n  @include navbar-vertical-align($input-height-base);\n\n  // Undo 100% width for pull classes\n  @media (min-width: $grid-float-breakpoint) {\n    width: auto;\n    border: 0;\n    margin-left: 0;\n    margin-right: 0;\n    padding-top: 0;\n    padding-bottom: 0;\n    @include box-shadow(none);\n  }\n}\n\n\n// Dropdown menus\n\n// Menu position and menu carets\n.navbar-nav > li > .dropdown-menu {\n  margin-top: 0;\n  @include border-top-radius(0);\n}\n// Menu position and menu caret support for dropups via extra dropup class\n.navbar-fixed-bottom .navbar-nav > li > .dropdown-menu {\n  margin-bottom: 0;\n  @include border-top-radius($navbar-border-radius);\n  @include border-bottom-radius(0);\n}\n\n\n// Buttons in navbars\n//\n// Vertically center a button within a navbar (when *not* in a form).\n\n.navbar-btn {\n  @include navbar-vertical-align($input-height-base);\n\n  &.btn-sm {\n    @include navbar-vertical-align($input-height-small);\n  }\n  &.btn-xs {\n    @include navbar-vertical-align(22);\n  }\n}\n\n\n// Text in navbars\n//\n// Add a class to make any element properly align itself vertically within the navbars.\n\n.navbar-text {\n  @include navbar-vertical-align($line-height-computed);\n\n  @media (min-width: $grid-float-breakpoint) {\n    float: left;\n    margin-left: $navbar-padding-horizontal;\n    margin-right: $navbar-padding-horizontal;\n  }\n}\n\n\n// Component alignment\n//\n// Repurpose the pull utilities as their own navbar utilities to avoid specificity\n// issues with parents and chaining. Only do this when the navbar is uncollapsed\n// though so that navbar contents properly stack and align in mobile.\n//\n// Declared after the navbar components to ensure more specificity on the margins.\n\n@media (min-width: $grid-float-breakpoint) {\n  .navbar-left {\n    float: left !important;\n  }\n  .navbar-right {\n    float: right !important;\n  margin-right: -$navbar-padding-horizontal;\n\n    ~ .navbar-right {\n      margin-right: 0;\n    }\n  }\n}\n\n\n// Alternate navbars\n// --------------------------------------------------\n\n// Default navbar\n.navbar-default {\n  background-color: $navbar-default-bg;\n  border-color: $navbar-default-border;\n\n  .navbar-brand {\n    color: $navbar-default-brand-color;\n    &:hover,\n    &:focus {\n      color: $navbar-default-brand-hover-color;\n      background-color: $navbar-default-brand-hover-bg;\n    }\n  }\n\n  .navbar-text {\n    color: $navbar-default-color;\n  }\n\n  .navbar-nav {\n    > li > a {\n      color: $navbar-default-link-color;\n\n      &:hover,\n      &:focus {\n        color: $navbar-default-link-hover-color;\n        background-color: $navbar-default-link-hover-bg;\n      }\n    }\n    > .active > a {\n      &,\n      &:hover,\n      &:focus {\n        color: $navbar-default-link-active-color;\n        background-color: $navbar-default-link-active-bg;\n      }\n    }\n    > .disabled > a {\n      &,\n      &:hover,\n      &:focus {\n        color: $navbar-default-link-disabled-color;\n        background-color: $navbar-default-link-disabled-bg;\n      }\n    }\n  }\n\n  .navbar-toggle {\n    border-color: $navbar-default-toggle-border-color;\n    &:hover,\n    &:focus {\n      background-color: $navbar-default-toggle-hover-bg;\n    }\n    .icon-bar {\n      background-color: $navbar-default-toggle-icon-bar-bg;\n    }\n  }\n\n  .navbar-collapse,\n  .navbar-form {\n    border-color: $navbar-default-border;\n  }\n\n  // Dropdown menu items\n  .navbar-nav {\n    // Remove background color from open dropdown\n    > .open > a {\n      &,\n      &:hover,\n      &:focus {\n        background-color: $navbar-default-link-active-bg;\n        color: $navbar-default-link-active-color;\n      }\n    }\n\n    @media (max-width: $grid-float-breakpoint-max) {\n      // Dropdowns get custom display when collapsed\n      .open .dropdown-menu {\n        > li > a {\n          color: $navbar-default-link-color;\n          &:hover,\n          &:focus {\n            color: $navbar-default-link-hover-color;\n            background-color: $navbar-default-link-hover-bg;\n          }\n        }\n        > .active > a {\n          &,\n          &:hover,\n          &:focus {\n            color: $navbar-default-link-active-color;\n            background-color: $navbar-default-link-active-bg;\n          }\n        }\n        > .disabled > a {\n          &,\n          &:hover,\n          &:focus {\n            color: $navbar-default-link-disabled-color;\n            background-color: $navbar-default-link-disabled-bg;\n          }\n        }\n      }\n    }\n  }\n\n\n  // Links in navbars\n  //\n  // Add a class to ensure links outside the navbar nav are colored correctly.\n\n  .navbar-link {\n    color: $navbar-default-link-color;\n    &:hover {\n      color: $navbar-default-link-hover-color;\n    }\n  }\n\n  .btn-link {\n    color: $navbar-default-link-color;\n    &:hover,\n    &:focus {\n      color: $navbar-default-link-hover-color;\n    }\n    &[disabled],\n    fieldset[disabled] & {\n      &:hover,\n      &:focus {\n        color: $navbar-default-link-disabled-color;\n      }\n    }\n  }\n}\n\n// Inverse navbar\n\n.navbar-inverse {\n  background-color: $navbar-inverse-bg;\n  border-color: $navbar-inverse-border;\n\n  .navbar-brand {\n    color: $navbar-inverse-brand-color;\n    &:hover,\n    &:focus {\n      color: $navbar-inverse-brand-hover-color;\n      background-color: $navbar-inverse-brand-hover-bg;\n    }\n  }\n\n  .navbar-text {\n    color: $navbar-inverse-color;\n  }\n\n  .navbar-nav {\n    > li > a {\n      color: $navbar-inverse-link-color;\n\n      &:hover,\n      &:focus {\n        color: $navbar-inverse-link-hover-color;\n        background-color: $navbar-inverse-link-hover-bg;\n      }\n    }\n    > .active > a {\n      &,\n      &:hover,\n      &:focus {\n        color: $navbar-inverse-link-active-color;\n        background-color: $navbar-inverse-link-active-bg;\n      }\n    }\n    > .disabled > a {\n      &,\n      &:hover,\n      &:focus {\n        color: $navbar-inverse-link-disabled-color;\n        background-color: $navbar-inverse-link-disabled-bg;\n      }\n    }\n  }\n\n  // Darken the responsive nav toggle\n  .navbar-toggle {\n    border-color: $navbar-inverse-toggle-border-color;\n    &:hover,\n    &:focus {\n      background-color: $navbar-inverse-toggle-hover-bg;\n    }\n    .icon-bar {\n      background-color: $navbar-inverse-toggle-icon-bar-bg;\n    }\n  }\n\n  .navbar-collapse,\n  .navbar-form {\n    border-color: darken($navbar-inverse-bg, 7%);\n  }\n\n  // Dropdowns\n  .navbar-nav {\n    > .open > a {\n      &,\n      &:hover,\n      &:focus {\n        background-color: $navbar-inverse-link-active-bg;\n        color: $navbar-inverse-link-active-color;\n      }\n    }\n\n    @media (max-width: $grid-float-breakpoint-max) {\n      // Dropdowns get custom display\n      .open .dropdown-menu {\n        > .dropdown-header {\n          border-color: $navbar-inverse-border;\n        }\n        .divider {\n          background-color: $navbar-inverse-border;\n        }\n        > li > a {\n          color: $navbar-inverse-link-color;\n          &:hover,\n          &:focus {\n            color: $navbar-inverse-link-hover-color;\n            background-color: $navbar-inverse-link-hover-bg;\n          }\n        }\n        > .active > a {\n          &,\n          &:hover,\n          &:focus {\n            color: $navbar-inverse-link-active-color;\n            background-color: $navbar-inverse-link-active-bg;\n          }\n        }\n        > .disabled > a {\n          &,\n          &:hover,\n          &:focus {\n            color: $navbar-inverse-link-disabled-color;\n            background-color: $navbar-inverse-link-disabled-bg;\n          }\n        }\n      }\n    }\n  }\n\n  .navbar-link {\n    color: $navbar-inverse-link-color;\n    &:hover {\n      color: $navbar-inverse-link-hover-color;\n    }\n  }\n\n  .btn-link {\n    color: $navbar-inverse-link-color;\n    &:hover,\n    &:focus {\n      color: $navbar-inverse-link-hover-color;\n    }\n    &[disabled],\n    fieldset[disabled] & {\n      &:hover,\n      &:focus {\n        color: $navbar-inverse-link-disabled-color;\n      }\n    }\n  }\n}\n", "// Navbar vertical align\n//\n// Vertically center elements in the navbar.\n// Example: an element has a height of 30px, so write out `.navbar-vertical-align(30px);` to calculate the appropriate top margin.\n\n@mixin navbar-vertical-align($element-height) {\n  margin-top: (($navbar-height - $element-height) / 2);\n  margin-bottom: (($navbar-height - $element-height) / 2);\n}\n", "//\n// Breadcrumbs\n// --------------------------------------------------\n\n\n.breadcrumb {\n  padding: $breadcrumb-padding-vertical $breadcrumb-padding-horizontal;\n  margin-bottom: $line-height-computed;\n  list-style: none;\n  background-color: $breadcrumb-bg;\n  border-radius: $border-radius-base;\n\n  > li {\n    display: inline-block;\n\n    + li:before {\n      // [converter] Workaround for https://github.com/sass/libsass/issues/1115\n      $nbsp: \"\\00a0\";\n      content: \"#{$breadcrumb-separator}#{$nbsp}\"; // Unicode space added since inline-block means non-collapsing white-space\n      padding: 0 5px;\n      color: $breadcrumb-color;\n    }\n  }\n\n  > .active {\n    color: $breadcrumb-active-color;\n  }\n}\n", "//\n// Pagination (multiple pages)\n// --------------------------------------------------\n.pagination {\n  display: inline-block;\n  padding-left: 0;\n  margin: $line-height-computed 0;\n  border-radius: $border-radius-base;\n\n  > li {\n    display: inline; // Remove list-style and block-level defaults\n    > a,\n    > span {\n      position: relative;\n      float: left; // Collapse white-space\n      padding: $padding-base-vertical $padding-base-horizontal;\n      line-height: $line-height-base;\n      text-decoration: none;\n      color: $pagination-color;\n      background-color: $pagination-bg;\n      border: 1px solid $pagination-border;\n      margin-left: -1px;\n    }\n    &:first-child {\n      > a,\n      > span {\n        margin-left: 0;\n        @include border-left-radius($border-radius-base);\n      }\n    }\n    &:last-child {\n      > a,\n      > span {\n        @include border-right-radius($border-radius-base);\n      }\n    }\n  }\n\n  > li > a,\n  > li > span {\n    &:hover,\n    &:focus {\n      z-index: 2;\n      color: $pagination-hover-color;\n      background-color: $pagination-hover-bg;\n      border-color: $pagination-hover-border;\n    }\n  }\n\n  > .active > a,\n  > .active > span {\n    &,\n    &:hover,\n    &:focus {\n      z-index: 3;\n      color: $pagination-active-color;\n      background-color: $pagination-active-bg;\n      border-color: $pagination-active-border;\n      cursor: default;\n    }\n  }\n\n  > .disabled {\n    > span,\n    > span:hover,\n    > span:focus,\n    > a,\n    > a:hover,\n    > a:focus {\n      color: $pagination-disabled-color;\n      background-color: $pagination-disabled-bg;\n      border-color: $pagination-disabled-border;\n      cursor: $cursor-disabled;\n    }\n  }\n}\n\n// Sizing\n// --------------------------------------------------\n\n// Large\n.pagination-lg {\n  @include pagination-size($padding-large-vertical, $padding-large-horizontal, $font-size-large, $line-height-large, $border-radius-large);\n}\n\n// Small\n.pagination-sm {\n  @include pagination-size($padding-small-vertical, $padding-small-horizontal, $font-size-small, $line-height-small, $border-radius-small);\n}\n", "// Pagination\n\n@mixin pagination-size($padding-vertical, $padding-horizontal, $font-size, $line-height, $border-radius) {\n  > li {\n    > a,\n    > span {\n      padding: $padding-vertical $padding-horizontal;\n      font-size: $font-size;\n      line-height: $line-height;\n    }\n    &:first-child {\n      > a,\n      > span {\n        @include border-left-radius($border-radius);\n      }\n    }\n    &:last-child {\n      > a,\n      > span {\n        @include border-right-radius($border-radius);\n      }\n    }\n  }\n}\n", "//\n// Pager pagination\n// --------------------------------------------------\n\n\n.pager {\n  padding-left: 0;\n  margin: $line-height-computed 0;\n  list-style: none;\n  text-align: center;\n  @include clearfix;\n  li {\n    display: inline;\n    > a,\n    > span {\n      display: inline-block;\n      padding: 5px 14px;\n      background-color: $pager-bg;\n      border: 1px solid $pager-border;\n      border-radius: $pager-border-radius;\n    }\n\n    > a:hover,\n    > a:focus {\n      text-decoration: none;\n      background-color: $pager-hover-bg;\n    }\n  }\n\n  .next {\n    > a,\n    > span {\n      float: right;\n    }\n  }\n\n  .previous {\n    > a,\n    > span {\n      float: left;\n    }\n  }\n\n  .disabled {\n    > a,\n    > a:hover,\n    > a:focus,\n    > span {\n      color: $pager-disabled-color;\n      background-color: $pager-bg;\n      cursor: $cursor-disabled;\n    }\n  }\n}\n", "//\n// Labels\n// --------------------------------------------------\n\n.label {\n  display: inline;\n  padding: .2em .6em .3em;\n  font-size: 75%;\n  font-weight: bold;\n  line-height: 1;\n  color: $label-color;\n  text-align: center;\n  white-space: nowrap;\n  vertical-align: baseline;\n  border-radius: .25em;\n\n  // [converter] extracted a& to a.label\n\n  // Empty labels collapse automatically (not available in IE8)\n  &:empty {\n    display: none;\n  }\n\n  // Quick fix for labels in buttons\n  .btn & {\n    position: relative;\n    top: -1px;\n  }\n}\n\n// Add hover effects, but only for links\na.label {\n  &:hover,\n  &:focus {\n    color: $label-link-hover-color;\n    text-decoration: none;\n    cursor: pointer;\n  }\n}\n\n// Colors\n// Contextual variations (linked labels get darker on :hover)\n\n.label-default {\n  @include label-variant($label-default-bg);\n}\n\n.label-primary {\n  @include label-variant($label-primary-bg);\n}\n\n.label-success {\n  @include label-variant($label-success-bg);\n}\n\n.label-info {\n  @include label-variant($label-info-bg);\n}\n\n.label-warning {\n  @include label-variant($label-warning-bg);\n}\n\n.label-danger {\n  @include label-variant($label-danger-bg);\n}\n", "// Labels\n\n@mixin label-variant($color) {\n  background-color: $color;\n\n  &[href] {\n    &:hover,\n    &:focus {\n      background-color: darken($color, 10%);\n    }\n  }\n}\n", "//\n// Badges\n// --------------------------------------------------\n\n\n// Base class\n.badge {\n  display: inline-block;\n  min-width: 10px;\n  padding: 3px 7px;\n  font-size: $font-size-small;\n  font-weight: $badge-font-weight;\n  color: $badge-color;\n  line-height: $badge-line-height;\n  vertical-align: middle;\n  white-space: nowrap;\n  text-align: center;\n  background-color: $badge-bg;\n  border-radius: $badge-border-radius;\n\n  // Empty badges collapse automatically (not available in IE8)\n  &:empty {\n    display: none;\n  }\n\n  // Quick fix for badges in buttons\n  .btn & {\n    position: relative;\n    top: -1px;\n  }\n\n  .btn-xs &,\n  .btn-group-xs > .btn & {\n    top: 0;\n    padding: 1px 5px;\n  }\n\n  // [converter] extracted a& to a.badge\n\n  // Account for badges in navs\n  .list-group-item.active > &,\n  .nav-pills > .active > a > & {\n    color: $badge-active-color;\n    background-color: $badge-active-bg;\n  }\n\n  .list-group-item > & {\n    float: right;\n  }\n\n  .list-group-item > & + & {\n    margin-right: 5px;\n  }\n\n  .nav-pills > li > a > & {\n    margin-left: 3px;\n  }\n}\n\n// Hover state, but only for links\na.badge {\n  &:hover,\n  &:focus {\n    color: $badge-link-hover-color;\n    text-decoration: none;\n    cursor: pointer;\n  }\n}\n", "//\n// Jumbotron\n// --------------------------------------------------\n\n\n.jumbotron {\n  padding-top:    $jumbotron-padding;\n  padding-bottom: $jumbotron-padding;\n  margin-bottom: $jumbotron-padding;\n  color: $jumbotron-color;\n  background-color: $jumbotron-bg;\n\n  h1,\n  .h1 {\n    color: $jumbotron-heading-color;\n  }\n\n  p {\n    margin-bottom: ($jumbotron-padding / 2);\n    font-size: $jumbotron-font-size;\n    font-weight: 200;\n  }\n\n  > hr {\n    border-top-color: darken($jumbotron-bg, 10%);\n  }\n\n  .container &,\n  .container-fluid & {\n    border-radius: $border-radius-large; // Only round corners at higher resolutions if contained in a container\n    padding-left:  ($grid-gutter-width / 2);\n    padding-right: ($grid-gutter-width / 2);\n  }\n\n  .container {\n    max-width: 100%;\n  }\n\n  @media screen and (min-width: $screen-sm-min) {\n    padding-top:    ($jumbotron-padding * 1.6);\n    padding-bottom: ($jumbotron-padding * 1.6);\n\n    .container &,\n    .container-fluid & {\n      padding-left:  ($jumbotron-padding * 2);\n      padding-right: ($jumbotron-padding * 2);\n    }\n\n    h1,\n    .h1 {\n      font-size: $jumbotron-heading-font-size;\n    }\n  }\n}\n", "//\n// Thumbnails\n// --------------------------------------------------\n\n\n// Mixin and adjust the regular image class\n.thumbnail {\n  display: block;\n  padding: $thumbnail-padding;\n  margin-bottom: $line-height-computed;\n  line-height: $line-height-base;\n  background-color: $thumbnail-bg;\n  border: 1px solid $thumbnail-border;\n  border-radius: $thumbnail-border-radius;\n  @include transition(border .2s ease-in-out);\n\n  > img,\n  a > img {\n    @include img-responsive;\n    margin-left: auto;\n    margin-right: auto;\n  }\n\n  // [converter] extracted a&:hover, a&:focus, a&.active to a.thumbnail:hover, a.thumbnail:focus, a.thumbnail.active\n\n  // Image captions\n  .caption {\n    padding: $thumbnail-caption-padding;\n    color: $thumbnail-caption-color;\n  }\n}\n\n// Add a hover state for linked versions only\na.thumbnail:hover,\na.thumbnail:focus,\na.thumbnail.active {\n  border-color: $link-color;\n}\n", "//\n// Alerts\n// --------------------------------------------------\n\n\n// Base styles\n// -------------------------\n\n.alert {\n  padding: $alert-padding;\n  margin-bottom: $line-height-computed;\n  border: 1px solid transparent;\n  border-radius: $alert-border-radius;\n\n  // Headings for larger alerts\n  h4 {\n    margin-top: 0;\n    // Specified for the h4 to prevent conflicts of changing $headings-color\n    color: inherit;\n  }\n\n  // Provide class for links that match alerts\n  .alert-link {\n    font-weight: $alert-link-font-weight;\n  }\n\n  // Improve alignment and spacing of inner content\n  > p,\n  > ul {\n    margin-bottom: 0;\n  }\n\n  > p + p {\n    margin-top: 5px;\n  }\n}\n\n// Dismissible alerts\n//\n// Expand the right padding and account for the close button's positioning.\n\n.alert-dismissable, // The misspelled .alert-dismissable was deprecated in 3.2.0.\n.alert-dismissible {\n  padding-right: ($alert-padding + 20);\n\n  // Adjust close link position\n  .close {\n    position: relative;\n    top: -2px;\n    right: -21px;\n    color: inherit;\n  }\n}\n\n// Alternate styles\n//\n// Generate contextual modifier classes for colorizing the alert.\n\n.alert-success {\n  @include alert-variant($alert-success-bg, $alert-success-border, $alert-success-text);\n}\n\n.alert-info {\n  @include alert-variant($alert-info-bg, $alert-info-border, $alert-info-text);\n}\n\n.alert-warning {\n  @include alert-variant($alert-warning-bg, $alert-warning-border, $alert-warning-text);\n}\n\n.alert-danger {\n  @include alert-variant($alert-danger-bg, $alert-danger-border, $alert-danger-text);\n}\n", "// Alerts\n\n@mixin alert-variant($background, $border, $text-color) {\n  background-color: $background;\n  border-color: $border;\n  color: $text-color;\n\n  hr {\n    border-top-color: darken($border, 5%);\n  }\n  .alert-link {\n    color: darken($text-color, 10%);\n  }\n}\n", "//\n// Progress bars\n// --------------------------------------------------\n\n\n// Bar animations\n// -------------------------\n\n// WebKit\n@-webkit-keyframes progress-bar-stripes {\n  from  { background-position: 40px 0; }\n  to    { background-position: 0 0; }\n}\n\n// Spec and IE10+\n@keyframes progress-bar-stripes {\n  from  { background-position: 40px 0; }\n  to    { background-position: 0 0; }\n}\n\n\n// Bar itself\n// -------------------------\n\n// Outer container\n.progress {\n  overflow: hidden;\n  height: $line-height-computed;\n  margin-bottom: $line-height-computed;\n  background-color: $progress-bg;\n  border-radius: $progress-border-radius;\n  @include box-shadow(inset 0 1px 2px rgba(0,0,0,.1));\n}\n\n// Bar of progress\n.progress-bar {\n  float: left;\n  width: 0%;\n  height: 100%;\n  font-size: $font-size-small;\n  line-height: $line-height-computed;\n  color: $progress-bar-color;\n  text-align: center;\n  background-color: $progress-bar-bg;\n  @include box-shadow(inset 0 -1px 0 rgba(0,0,0,.15));\n  @include transition(width .6s ease);\n}\n\n// Striped bars\n//\n// `.progress-striped .progress-bar` is deprecated as of v3.2.0 in favor of the\n// `.progress-bar-striped` class, which you just add to an existing\n// `.progress-bar`.\n.progress-striped .progress-bar,\n.progress-bar-striped {\n  @include gradient-striped;\n  background-size: 40px 40px;\n}\n\n// Call animation for the active one\n//\n// `.progress.active .progress-bar` is deprecated as of v3.2.0 in favor of the\n// `.progress-bar.active` approach.\n.progress.active .progress-bar,\n.progress-bar.active {\n  @include animation(progress-bar-stripes 2s linear infinite);\n}\n\n\n// Variations\n// -------------------------\n\n.progress-bar-success {\n  @include progress-bar-variant($progress-bar-success-bg);\n}\n\n.progress-bar-info {\n  @include progress-bar-variant($progress-bar-info-bg);\n}\n\n.progress-bar-warning {\n  @include progress-bar-variant($progress-bar-warning-bg);\n}\n\n.progress-bar-danger {\n  @include progress-bar-variant($progress-bar-danger-bg);\n}\n", "// Gradients\n\n\n\n// Horizontal gradient, from left to right\n//\n// Creates two color stops, start and end, by specifying a color and position for each color stop.\n// Color stops are not available in IE9 and below.\n@mixin gradient-horizontal($start-color: #555, $end-color: #333, $start-percent: 0%, $end-percent: 100%) {\n  background-image: -webkit-linear-gradient(left, $start-color $start-percent, $end-color $end-percent); // Safari 5.1-6, Chrome 10+\n  background-image: -o-linear-gradient(left, $start-color $start-percent, $end-color $end-percent); // Opera 12\n  background-image: linear-gradient(to right, $start-color $start-percent, $end-color $end-percent); // Standard, IE10, Firefox 16+, Opera 12.10+, Safari 7+, Chrome 26+\n  background-repeat: repeat-x;\n  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#{ie-hex-str($start-color)}', endColorstr='#{ie-hex-str($end-color)}', GradientType=1); // IE9 and down\n}\n\n// Vertical gradient, from top to bottom\n//\n// Creates two color stops, start and end, by specifying a color and position for each color stop.\n// Color stops are not available in IE9 and below.\n@mixin gradient-vertical($start-color: #555, $end-color: #333, $start-percent: 0%, $end-percent: 100%) {\n  background-image: -webkit-linear-gradient(top, $start-color $start-percent, $end-color $end-percent);  // Safari 5.1-6, Chrome 10+\n  background-image: -o-linear-gradient(top, $start-color $start-percent, $end-color $end-percent);  // Opera 12\n  background-image: linear-gradient(to bottom, $start-color $start-percent, $end-color $end-percent); // Standard, IE10, Firefox 16+, Opera 12.10+, Safari 7+, Chrome 26+\n  background-repeat: repeat-x;\n  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#{ie-hex-str($start-color)}', endColorstr='#{ie-hex-str($end-color)}', GradientType=0); // IE9 and down\n}\n\n@mixin gradient-directional($start-color: #555, $end-color: #333, $deg: 45deg) {\n  background-repeat: repeat-x;\n  background-image: -webkit-linear-gradient($deg, $start-color, $end-color); // Safari 5.1-6, Chrome 10+\n  background-image: -o-linear-gradient($deg, $start-color, $end-color); // Opera 12\n  background-image: linear-gradient($deg, $start-color, $end-color); // Standard, IE10, Firefox 16+, Opera 12.10+, Safari 7+, Chrome 26+\n}\n@mixin gradient-horizontal-three-colors($start-color: #00b3ee, $mid-color: #7a43b6, $color-stop: 50%, $end-color: #c3325f) {\n  background-image: -webkit-linear-gradient(left, $start-color, $mid-color $color-stop, $end-color);\n  background-image: -o-linear-gradient(left, $start-color, $mid-color $color-stop, $end-color);\n  background-image: linear-gradient(to right, $start-color, $mid-color $color-stop, $end-color);\n  background-repeat: no-repeat;\n  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#{ie-hex-str($start-color)}', endColorstr='#{ie-hex-str($end-color)}', GradientType=1); // IE9 and down, gets no color-stop at all for proper fallback\n}\n@mixin gradient-vertical-three-colors($start-color: #00b3ee, $mid-color: #7a43b6, $color-stop: 50%, $end-color: #c3325f) {\n  background-image: -webkit-linear-gradient($start-color, $mid-color $color-stop, $end-color);\n  background-image: -o-linear-gradient($start-color, $mid-color $color-stop, $end-color);\n  background-image: linear-gradient($start-color, $mid-color $color-stop, $end-color);\n  background-repeat: no-repeat;\n  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#{ie-hex-str($start-color)}', endColorstr='#{ie-hex-str($end-color)}', GradientType=0); // IE9 and down, gets no color-stop at all for proper fallback\n}\n@mixin gradient-radial($inner-color: #555, $outer-color: #333) {\n  background-image: -webkit-radial-gradient(circle, $inner-color, $outer-color);\n  background-image: radial-gradient(circle, $inner-color, $outer-color);\n  background-repeat: no-repeat;\n}\n@mixin gradient-striped($color: rgba(255,255,255,.15), $angle: 45deg) {\n  background-image: -webkit-linear-gradient($angle, $color 25%, transparent 25%, transparent 50%, $color 50%, $color 75%, transparent 75%, transparent);\n  background-image: -o-linear-gradient($angle, $color 25%, transparent 25%, transparent 50%, $color 50%, $color 75%, transparent 75%, transparent);\n  background-image: linear-gradient($angle, $color 25%, transparent 25%, transparent 50%, $color 50%, $color 75%, transparent 75%, transparent);\n}\n", "// Progress bars\n\n@mixin progress-bar-variant($color) {\n  background-color: $color;\n\n  // Deprecated parent class requirement as of v3.2.0\n  .progress-striped & {\n    @include gradient-striped;\n  }\n}\n", ".media {\n  // Proper spacing between instances of .media\n  margin-top: 15px;\n\n  &:first-child {\n    margin-top: 0;\n  }\n}\n\n.media,\n.media-body {\n  zoom: 1;\n  overflow: hidden;\n}\n\n.media-body {\n  width: 10000px;\n}\n\n.media-object {\n  display: block;\n\n  // Fix collapse in webkit from max-width: 100% and display: table-cell.\n  &.img-thumbnail {\n    max-width: none;\n  }\n}\n\n.media-right,\n.media > .pull-right {\n  padding-left: 10px;\n}\n\n.media-left,\n.media > .pull-left {\n  padding-right: 10px;\n}\n\n.media-left,\n.media-right,\n.media-body {\n  display: table-cell;\n  vertical-align: top;\n}\n\n.media-middle {\n  vertical-align: middle;\n}\n\n.media-bottom {\n  vertical-align: bottom;\n}\n\n// Reset margins on headings for tighter default spacing\n.media-heading {\n  margin-top: 0;\n  margin-bottom: 5px;\n}\n\n// Media list variation\n//\n// Undo default ul/ol styles\n.media-list {\n  padding-left: 0;\n  list-style: none;\n}\n", "//\n// List groups\n// --------------------------------------------------\n\n\n// Base class\n//\n// Easily usable on <ul>, <ol>, or <div>.\n\n.list-group {\n  // No need to set list-style: none; since .list-group-item is block level\n  margin-bottom: 20px;\n  padding-left: 0; // reset padding because ul and ol\n}\n\n\n// Individual list items\n//\n// Use on `li`s or `div`s within the `.list-group` parent.\n\n.list-group-item {\n  position: relative;\n  display: block;\n  padding: 10px 15px;\n  // Place the border on the list items and negative margin up for better styling\n  margin-bottom: -1px;\n  background-color: $list-group-bg;\n  border: 1px solid $list-group-border;\n\n  // Round the first and last items\n  &:first-child {\n    @include border-top-radius($list-group-border-radius);\n  }\n  &:last-child {\n    margin-bottom: 0;\n    @include border-bottom-radius($list-group-border-radius);\n  }\n}\n\n\n// Interactive list items\n//\n// Use anchor or button elements instead of `li`s or `div`s to create interactive items.\n// Includes an extra `.active` modifier class for showing selected items.\n\na.list-group-item,\nbutton.list-group-item {\n  color: $list-group-link-color;\n\n  .list-group-item-heading {\n    color: $list-group-link-heading-color;\n  }\n\n  // Hover state\n  &:hover,\n  &:focus {\n    text-decoration: none;\n    color: $list-group-link-hover-color;\n    background-color: $list-group-hover-bg;\n  }\n}\n\nbutton.list-group-item {\n  width: 100%;\n  text-align: left;\n}\n\n.list-group-item {\n  // Disabled state\n  &.disabled,\n  &.disabled:hover,\n  &.disabled:focus {\n    background-color: $list-group-disabled-bg;\n    color: $list-group-disabled-color;\n    cursor: $cursor-disabled;\n\n    // Force color to inherit for custom content\n    .list-group-item-heading {\n      color: inherit;\n    }\n    .list-group-item-text {\n      color: $list-group-disabled-text-color;\n    }\n  }\n\n  // Active class on item itself, not parent\n  &.active,\n  &.active:hover,\n  &.active:focus {\n    z-index: 2; // Place active items above their siblings for proper border styling\n    color: $list-group-active-color;\n    background-color: $list-group-active-bg;\n    border-color: $list-group-active-border;\n\n    // Force color to inherit for custom content\n    .list-group-item-heading,\n    .list-group-item-heading > small,\n    .list-group-item-heading > .small {\n      color: inherit;\n    }\n    .list-group-item-text {\n      color: $list-group-active-text-color;\n    }\n  }\n}\n\n\n// Contextual variants\n//\n// Add modifier classes to change text and background color on individual items.\n// Organizationally, this must come after the `:hover` states.\n\n@include list-group-item-variant(success, $state-success-bg, $state-success-text);\n@include list-group-item-variant(info, $state-info-bg, $state-info-text);\n@include list-group-item-variant(warning, $state-warning-bg, $state-warning-text);\n@include list-group-item-variant(danger, $state-danger-bg, $state-danger-text);\n\n\n// Custom content options\n//\n// Extra classes for creating well-formatted content within `.list-group-item`s.\n\n.list-group-item-heading {\n  margin-top: 0;\n  margin-bottom: 5px;\n}\n.list-group-item-text {\n  margin-bottom: 0;\n  line-height: 1.3;\n}\n", "// List Groups\n\n@mixin list-group-item-variant($state, $background, $color) {\n  .list-group-item-#{$state} {\n    color: $color;\n    background-color: $background;\n\n    // [converter] extracted a&, button& to a.list-group-item-#{$state}, button.list-group-item-#{$state}\n  }\n\n  a.list-group-item-#{$state},\n  button.list-group-item-#{$state} {\n    color: $color;\n\n    .list-group-item-heading {\n      color: inherit;\n    }\n\n    &:hover,\n    &:focus {\n      color: $color;\n      background-color: darken($background, 5%);\n    }\n    &.active,\n    &.active:hover,\n    &.active:focus {\n      color: #fff;\n      background-color: $color;\n      border-color: $color;\n    }\n  }\n}\n", "//\n// Panels\n// --------------------------------------------------\n\n\n// Base class\n.panel {\n  margin-bottom: $line-height-computed;\n  background-color: $panel-bg;\n  border: 1px solid transparent;\n  border-radius: $panel-border-radius;\n  @include box-shadow(0 1px 1px rgba(0,0,0,.05));\n}\n\n// Panel contents\n.panel-body {\n  padding: $panel-body-padding;\n  @include clearfix;\n}\n\n// Optional heading\n.panel-heading {\n  padding: $panel-heading-padding;\n  border-bottom: 1px solid transparent;\n  @include border-top-radius(($panel-border-radius - 1));\n\n  > .dropdown .dropdown-toggle {\n    color: inherit;\n  }\n}\n\n// Within heading, strip any `h*` tag of its default margins for spacing.\n.panel-title {\n  margin-top: 0;\n  margin-bottom: 0;\n  font-size: ceil(($font-size-base * 1.125));\n  color: inherit;\n\n  > a,\n  > small,\n  > .small,\n  > small > a,\n  > .small > a {\n    color: inherit;\n  }\n}\n\n// Optional footer (stays gray in every modifier class)\n.panel-footer {\n  padding: $panel-footer-padding;\n  background-color: $panel-footer-bg;\n  border-top: 1px solid $panel-inner-border;\n  @include border-bottom-radius(($panel-border-radius - 1));\n}\n\n\n// List groups in panels\n//\n// By default, space out list group content from panel headings to account for\n// any kind of custom content between the two.\n\n.panel {\n  > .list-group,\n  > .panel-collapse > .list-group {\n    margin-bottom: 0;\n\n    .list-group-item {\n      border-width: 1px 0;\n      border-radius: 0;\n    }\n\n    // Add border top radius for first one\n    &:first-child {\n      .list-group-item:first-child {\n        border-top: 0;\n        @include border-top-radius(($panel-border-radius - 1));\n      }\n    }\n\n    // Add border bottom radius for last one\n    &:last-child {\n      .list-group-item:last-child {\n        border-bottom: 0;\n        @include border-bottom-radius(($panel-border-radius - 1));\n      }\n    }\n  }\n  > .panel-heading + .panel-collapse > .list-group {\n    .list-group-item:first-child {\n      @include border-top-radius(0);\n    }\n  }\n}\n// Collapse space between when there's no additional content.\n.panel-heading + .list-group {\n  .list-group-item:first-child {\n    border-top-width: 0;\n  }\n}\n.list-group + .panel-footer {\n  border-top-width: 0;\n}\n\n// Tables in panels\n//\n// Place a non-bordered `.table` within a panel (not within a `.panel-body`) and\n// watch it go full width.\n\n.panel {\n  > .table,\n  > .table-responsive > .table,\n  > .panel-collapse > .table {\n    margin-bottom: 0;\n\n    caption {\n      padding-left: $panel-body-padding;\n      padding-right: $panel-body-padding;\n    }\n  }\n  // Add border top radius for first one\n  > .table:first-child,\n  > .table-responsive:first-child > .table:first-child {\n    @include border-top-radius(($panel-border-radius - 1));\n\n    > thead:first-child,\n    > tbody:first-child {\n      > tr:first-child {\n        border-top-left-radius: ($panel-border-radius - 1);\n        border-top-right-radius: ($panel-border-radius - 1);\n\n        td:first-child,\n        th:first-child {\n          border-top-left-radius: ($panel-border-radius - 1);\n        }\n        td:last-child,\n        th:last-child {\n          border-top-right-radius: ($panel-border-radius - 1);\n        }\n      }\n    }\n  }\n  // Add border bottom radius for last one\n  > .table:last-child,\n  > .table-responsive:last-child > .table:last-child {\n    @include border-bottom-radius(($panel-border-radius - 1));\n\n    > tbody:last-child,\n    > tfoot:last-child {\n      > tr:last-child {\n        border-bottom-left-radius: ($panel-border-radius - 1);\n        border-bottom-right-radius: ($panel-border-radius - 1);\n\n        td:first-child,\n        th:first-child {\n          border-bottom-left-radius: ($panel-border-radius - 1);\n        }\n        td:last-child,\n        th:last-child {\n          border-bottom-right-radius: ($panel-border-radius - 1);\n        }\n      }\n    }\n  }\n  > .panel-body + .table,\n  > .panel-body + .table-responsive,\n  > .table + .panel-body,\n  > .table-responsive + .panel-body {\n    border-top: 1px solid $table-border-color;\n  }\n  > .table > tbody:first-child > tr:first-child th,\n  > .table > tbody:first-child > tr:first-child td {\n    border-top: 0;\n  }\n  > .table-bordered,\n  > .table-responsive > .table-bordered {\n    border: 0;\n    > thead,\n    > tbody,\n    > tfoot {\n      > tr {\n        > th:first-child,\n        > td:first-child {\n          border-left: 0;\n        }\n        > th:last-child,\n        > td:last-child {\n          border-right: 0;\n        }\n      }\n    }\n    > thead,\n    > tbody {\n      > tr:first-child {\n        > td,\n        > th {\n          border-bottom: 0;\n        }\n      }\n    }\n    > tbody,\n    > tfoot {\n      > tr:last-child {\n        > td,\n        > th {\n          border-bottom: 0;\n        }\n      }\n    }\n  }\n  > .table-responsive {\n    border: 0;\n    margin-bottom: 0;\n  }\n}\n\n\n// Collapsible panels (aka, accordion)\n//\n// Wrap a series of panels in `.panel-group` to turn them into an accordion with\n// the help of our collapse JavaScript plugin.\n\n.panel-group {\n  margin-bottom: $line-height-computed;\n\n  // Tighten up margin so it's only between panels\n  .panel {\n    margin-bottom: 0;\n    border-radius: $panel-border-radius;\n\n    + .panel {\n      margin-top: 5px;\n    }\n  }\n\n  .panel-heading {\n    border-bottom: 0;\n\n    + .panel-collapse > .panel-body,\n    + .panel-collapse > .list-group {\n      border-top: 1px solid $panel-inner-border;\n    }\n  }\n\n  .panel-footer {\n    border-top: 0;\n    + .panel-collapse .panel-body {\n      border-bottom: 1px solid $panel-inner-border;\n    }\n  }\n}\n\n\n// Contextual variations\n.panel-default {\n  @include panel-variant($panel-default-border, $panel-default-text, $panel-default-heading-bg, $panel-default-border);\n}\n.panel-primary {\n  @include panel-variant($panel-primary-border, $panel-primary-text, $panel-primary-heading-bg, $panel-primary-border);\n}\n.panel-success {\n  @include panel-variant($panel-success-border, $panel-success-text, $panel-success-heading-bg, $panel-success-border);\n}\n.panel-info {\n  @include panel-variant($panel-info-border, $panel-info-text, $panel-info-heading-bg, $panel-info-border);\n}\n.panel-warning {\n  @include panel-variant($panel-warning-border, $panel-warning-text, $panel-warning-heading-bg, $panel-warning-border);\n}\n.panel-danger {\n  @include panel-variant($panel-danger-border, $panel-danger-text, $panel-danger-heading-bg, $panel-danger-border);\n}\n", "// Panels\n\n@mixin panel-variant($border, $heading-text-color, $heading-bg-color, $heading-border) {\n  border-color: $border;\n\n  & > .panel-heading {\n    color: $heading-text-color;\n    background-color: $heading-bg-color;\n    border-color: $heading-border;\n\n    + .panel-collapse > .panel-body {\n      border-top-color: $border;\n    }\n    .badge {\n      color: $heading-bg-color;\n      background-color: $heading-text-color;\n    }\n  }\n  & > .panel-footer {\n    + .panel-collapse > .panel-body {\n      border-bottom-color: $border;\n    }\n  }\n}\n", "// Embeds responsive\n//\n// Credit: <PERSON> and SUIT CSS.\n\n.embed-responsive {\n  position: relative;\n  display: block;\n  height: 0;\n  padding: 0;\n  overflow: hidden;\n\n  .embed-responsive-item,\n  iframe,\n  embed,\n  object,\n  video {\n    position: absolute;\n    top: 0;\n    left: 0;\n    bottom: 0;\n    height: 100%;\n    width: 100%;\n    border: 0;\n  }\n}\n\n// Modifier class for 16:9 aspect ratio\n.embed-responsive-16by9 {\n  padding-bottom: 56.25%;\n}\n\n// Modifier class for 4:3 aspect ratio\n.embed-responsive-4by3 {\n  padding-bottom: 75%;\n}\n", "//\n// Wells\n// --------------------------------------------------\n\n\n// Base class\n.well {\n  min-height: 20px;\n  padding: 19px;\n  margin-bottom: 20px;\n  background-color: $well-bg;\n  border: 1px solid $well-border;\n  border-radius: $border-radius-base;\n  @include box-shadow(inset 0 1px 1px rgba(0,0,0,.05));\n  blockquote {\n    border-color: #ddd;\n    border-color: rgba(0,0,0,.15);\n  }\n}\n\n// Sizes\n.well-lg {\n  padding: 24px;\n  border-radius: $border-radius-large;\n}\n.well-sm {\n  padding: 9px;\n  border-radius: $border-radius-small;\n}\n", "//\n// Close icons\n// --------------------------------------------------\n\n\n.close {\n  float: right;\n  font-size: ($font-size-base * 1.5);\n  font-weight: $close-font-weight;\n  line-height: 1;\n  color: $close-color;\n  text-shadow: $close-text-shadow;\n  @include opacity(.2);\n\n  &:hover,\n  &:focus {\n    color: $close-color;\n    text-decoration: none;\n    cursor: pointer;\n    @include opacity(.5);\n  }\n\n  // [converter] extracted button& to button.close\n}\n\n// Additional properties for button version\n// iOS requires the button element instead of an anchor tag.\n// If you want the anchor version, it requires `href=\"#\"`.\n// See https://developer.mozilla.org/en-US/docs/Web/Events/click#Safari_Mobile\nbutton.close {\n  padding: 0;\n  cursor: pointer;\n  background: transparent;\n  border: 0;\n  -webkit-appearance: none;\n}\n", "//\n// Modals\n// --------------------------------------------------\n\n// .modal-open      - body class for killing the scroll\n// .modal           - container to scroll within\n// .modal-dialog    - positioning shell for the actual modal\n// .modal-content   - actual modal w/ bg and corners and shit\n\n// Kill the scroll on the body\n.modal-open {\n  overflow: hidden;\n}\n\n// Container that the modal scrolls within\n.modal {\n  display: none;\n  overflow: hidden;\n  position: fixed;\n  top: 0;\n  right: 0;\n  bottom: 0;\n  left: 0;\n  z-index: $zindex-modal;\n  -webkit-overflow-scrolling: touch;\n\n  // Prevent Chrome on Windows from adding a focus outline. For details, see\n  // https://github.com/twbs/bootstrap/pull/10951.\n  outline: 0;\n\n  // When fading in the modal, animate it to slide down\n  &.fade .modal-dialog {\n    @include translate(0, -25%);\n    @include transition-transform(0.3s ease-out);\n  }\n  &.in .modal-dialog { @include translate(0, 0) }\n}\n.modal-open .modal {\n  overflow-x: hidden;\n  overflow-y: auto;\n}\n\n// Shell div to position the modal with bottom padding\n.modal-dialog {\n  position: relative;\n  width: auto;\n  margin: 10px;\n}\n\n// Actual modal\n.modal-content {\n  position: relative;\n  background-color: $modal-content-bg;\n  border: 1px solid $modal-content-fallback-border-color; //old browsers fallback (ie8 etc)\n  border: 1px solid $modal-content-border-color;\n  border-radius: $border-radius-large;\n  @include box-shadow(0 3px 9px rgba(0,0,0,.5));\n  background-clip: padding-box;\n  // Remove focus outline from opened modal\n  outline: 0;\n}\n\n// Modal background\n.modal-backdrop {\n  position: fixed;\n  top: 0;\n  right: 0;\n  bottom: 0;\n  left: 0;\n  z-index: $zindex-modal-background;\n  background-color: $modal-backdrop-bg;\n  // Fade for backdrop\n  &.fade { @include opacity(0); }\n  &.in { @include opacity($modal-backdrop-opacity); }\n}\n\n// Modal header\n// Top section of the modal w/ title and dismiss\n.modal-header {\n  padding: $modal-title-padding;\n  border-bottom: 1px solid $modal-header-border-color;\n  @include clearfix;\n}\n// Close icon\n.modal-header .close {\n  margin-top: -2px;\n}\n\n// Title text within header\n.modal-title {\n  margin: 0;\n  line-height: $modal-title-line-height;\n}\n\n// Modal body\n// Where all modal content resides (sibling of .modal-header and .modal-footer)\n.modal-body {\n  position: relative;\n  padding: $modal-inner-padding;\n}\n\n// Footer (for actions)\n.modal-footer {\n  padding: $modal-inner-padding;\n  text-align: right; // right align buttons\n  border-top: 1px solid $modal-footer-border-color;\n  @include clearfix; // clear it in case folks use .pull-* classes on buttons\n\n  // Properly space out buttons\n  .btn + .btn {\n    margin-left: 5px;\n    margin-bottom: 0; // account for input[type=\"submit\"] which gets the bottom margin like all other inputs\n  }\n  // but override that for button groups\n  .btn-group .btn + .btn {\n    margin-left: -1px;\n  }\n  // and override it for block buttons as well\n  .btn-block + .btn-block {\n    margin-left: 0;\n  }\n}\n\n// Measure scrollbar width for padding body during modal show/hide\n.modal-scrollbar-measure {\n  position: absolute;\n  top: -9999px;\n  width: 50px;\n  height: 50px;\n  overflow: scroll;\n}\n\n// Scale up the modal\n@media (min-width: $screen-sm-min) {\n  // Automatically set modal's width for larger viewports\n  .modal-dialog {\n    width: $modal-md;\n    margin: 30px auto;\n  }\n  .modal-content {\n    @include box-shadow(0 5px 15px rgba(0,0,0,.5));\n  }\n\n  // Modal sizes\n  .modal-sm { width: $modal-sm; }\n}\n\n@media (min-width: $screen-md-min) {\n  .modal-lg { width: $modal-lg; }\n}\n", "//\n// Tooltips\n// --------------------------------------------------\n\n\n// Base class\n.tooltip {\n  position: absolute;\n  z-index: $zindex-tooltip;\n  display: block;\n  // Our parent element can be arbitrary since tooltips are by default inserted as a sibling of their target element.\n  // So reset our font and text properties to avoid inheriting weird values.\n  @include reset-text;\n  font-size: $font-size-small;\n\n  @include opacity(0);\n\n  &.in     { @include opacity($tooltip-opacity); }\n  &.top    { margin-top:  -3px; padding: $tooltip-arrow-width 0; }\n  &.right  { margin-left:  3px; padding: 0 $tooltip-arrow-width; }\n  &.bottom { margin-top:   3px; padding: $tooltip-arrow-width 0; }\n  &.left   { margin-left: -3px; padding: 0 $tooltip-arrow-width; }\n}\n\n// Wrapper for the tooltip content\n.tooltip-inner {\n  max-width: $tooltip-max-width;\n  padding: 3px 8px;\n  color: $tooltip-color;\n  text-align: center;\n  background-color: $tooltip-bg;\n  border-radius: $border-radius-base;\n}\n\n// Arrows\n.tooltip-arrow {\n  position: absolute;\n  width: 0;\n  height: 0;\n  border-color: transparent;\n  border-style: solid;\n}\n// Note: Deprecated .top-left, .top-right, .bottom-left, and .bottom-right as of v3.3.1\n.tooltip {\n  &.top .tooltip-arrow {\n    bottom: 0;\n    left: 50%;\n    margin-left: -$tooltip-arrow-width;\n    border-width: $tooltip-arrow-width $tooltip-arrow-width 0;\n    border-top-color: $tooltip-arrow-color;\n  }\n  &.top-left .tooltip-arrow {\n    bottom: 0;\n    right: $tooltip-arrow-width;\n    margin-bottom: -$tooltip-arrow-width;\n    border-width: $tooltip-arrow-width $tooltip-arrow-width 0;\n    border-top-color: $tooltip-arrow-color;\n  }\n  &.top-right .tooltip-arrow {\n    bottom: 0;\n    left: $tooltip-arrow-width;\n    margin-bottom: -$tooltip-arrow-width;\n    border-width: $tooltip-arrow-width $tooltip-arrow-width 0;\n    border-top-color: $tooltip-arrow-color;\n  }\n  &.right .tooltip-arrow {\n    top: 50%;\n    left: 0;\n    margin-top: -$tooltip-arrow-width;\n    border-width: $tooltip-arrow-width $tooltip-arrow-width $tooltip-arrow-width 0;\n    border-right-color: $tooltip-arrow-color;\n  }\n  &.left .tooltip-arrow {\n    top: 50%;\n    right: 0;\n    margin-top: -$tooltip-arrow-width;\n    border-width: $tooltip-arrow-width 0 $tooltip-arrow-width $tooltip-arrow-width;\n    border-left-color: $tooltip-arrow-color;\n  }\n  &.bottom .tooltip-arrow {\n    top: 0;\n    left: 50%;\n    margin-left: -$tooltip-arrow-width;\n    border-width: 0 $tooltip-arrow-width $tooltip-arrow-width;\n    border-bottom-color: $tooltip-arrow-color;\n  }\n  &.bottom-left .tooltip-arrow {\n    top: 0;\n    right: $tooltip-arrow-width;\n    margin-top: -$tooltip-arrow-width;\n    border-width: 0 $tooltip-arrow-width $tooltip-arrow-width;\n    border-bottom-color: $tooltip-arrow-color;\n  }\n  &.bottom-right .tooltip-arrow {\n    top: 0;\n    left: $tooltip-arrow-width;\n    margin-top: -$tooltip-arrow-width;\n    border-width: 0 $tooltip-arrow-width $tooltip-arrow-width;\n    border-bottom-color: $tooltip-arrow-color;\n  }\n}\n", "@mixin reset-text() {\n  font-family: $font-family-base;\n  // We deliberately do NOT reset font-size.\n  font-style: normal;\n  font-weight: normal;\n  letter-spacing: normal;\n  line-break: auto;\n  line-height: $line-height-base;\n  text-align: left; // Fallback for where `start` is not supported\n  text-align: start;\n  text-decoration: none;\n  text-shadow: none;\n  text-transform: none;\n  white-space: normal;\n  word-break: normal;\n  word-spacing: normal;\n  word-wrap: normal;\n}\n", "//\n// Popovers\n// --------------------------------------------------\n\n\n.popover {\n  position: absolute;\n  top: 0;\n  left: 0;\n  z-index: $zindex-popover;\n  display: none;\n  max-width: $popover-max-width;\n  padding: 1px;\n  // Our parent element can be arbitrary since popovers are by default inserted as a sibling of their target element.\n  // So reset our font and text properties to avoid inheriting weird values.\n  @include reset-text;\n  font-size: $font-size-base;\n\n  background-color: $popover-bg;\n  background-clip: padding-box;\n  border: 1px solid $popover-fallback-border-color;\n  border: 1px solid $popover-border-color;\n  border-radius: $border-radius-large;\n  @include box-shadow(0 5px 10px rgba(0,0,0,.2));\n\n  // Offset the popover to account for the popover arrow\n  &.top     { margin-top: -$popover-arrow-width; }\n  &.right   { margin-left: $popover-arrow-width; }\n  &.bottom  { margin-top: $popover-arrow-width; }\n  &.left    { margin-left: -$popover-arrow-width; }\n}\n\n.popover-title {\n  margin: 0; // reset heading margin\n  padding: 8px 14px;\n  font-size: $font-size-base;\n  background-color: $popover-title-bg;\n  border-bottom: 1px solid darken($popover-title-bg, 5%);\n  border-radius: ($border-radius-large - 1) ($border-radius-large - 1) 0 0;\n}\n\n.popover-content {\n  padding: 9px 14px;\n}\n\n// Arrows\n//\n// .arrow is outer, .arrow:after is inner\n\n.popover > .arrow {\n  &,\n  &:after {\n    position: absolute;\n    display: block;\n    width: 0;\n    height: 0;\n    border-color: transparent;\n    border-style: solid;\n  }\n}\n.popover > .arrow {\n  border-width: $popover-arrow-outer-width;\n}\n.popover > .arrow:after {\n  border-width: $popover-arrow-width;\n  content: \"\";\n}\n\n.popover {\n  &.top > .arrow {\n    left: 50%;\n    margin-left: -$popover-arrow-outer-width;\n    border-bottom-width: 0;\n    border-top-color: $popover-arrow-outer-fallback-color; // IE8 fallback\n    border-top-color: $popover-arrow-outer-color;\n    bottom: -$popover-arrow-outer-width;\n    &:after {\n      content: \" \";\n      bottom: 1px;\n      margin-left: -$popover-arrow-width;\n      border-bottom-width: 0;\n      border-top-color: $popover-arrow-color;\n    }\n  }\n  &.right > .arrow {\n    top: 50%;\n    left: -$popover-arrow-outer-width;\n    margin-top: -$popover-arrow-outer-width;\n    border-left-width: 0;\n    border-right-color: $popover-arrow-outer-fallback-color; // IE8 fallback\n    border-right-color: $popover-arrow-outer-color;\n    &:after {\n      content: \" \";\n      left: 1px;\n      bottom: -$popover-arrow-width;\n      border-left-width: 0;\n      border-right-color: $popover-arrow-color;\n    }\n  }\n  &.bottom > .arrow {\n    left: 50%;\n    margin-left: -$popover-arrow-outer-width;\n    border-top-width: 0;\n    border-bottom-color: $popover-arrow-outer-fallback-color; // IE8 fallback\n    border-bottom-color: $popover-arrow-outer-color;\n    top: -$popover-arrow-outer-width;\n    &:after {\n      content: \" \";\n      top: 1px;\n      margin-left: -$popover-arrow-width;\n      border-top-width: 0;\n      border-bottom-color: $popover-arrow-color;\n    }\n  }\n\n  &.left > .arrow {\n    top: 50%;\n    right: -$popover-arrow-outer-width;\n    margin-top: -$popover-arrow-outer-width;\n    border-right-width: 0;\n    border-left-color: $popover-arrow-outer-fallback-color; // IE8 fallback\n    border-left-color: $popover-arrow-outer-color;\n    &:after {\n      content: \" \";\n      right: 1px;\n      border-right-width: 0;\n      border-left-color: $popover-arrow-color;\n      bottom: -$popover-arrow-width;\n    }\n  }\n}\n", "//\n// Carousel\n// --------------------------------------------------\n\n\n// Wrapper for the slide container and indicators\n.carousel {\n  position: relative;\n}\n\n.carousel-inner {\n  position: relative;\n  overflow: hidden;\n  width: 100%;\n\n  > .item {\n    display: none;\n    position: relative;\n    @include transition(.6s ease-in-out left);\n\n    // Account for jankitude on images\n    > img,\n    > a > img {\n      @include img-responsive;\n      line-height: 1;\n    }\n\n    // WebKit CSS3 transforms for supported devices\n    @media all and (transform-3d), (-webkit-transform-3d) {\n      @include transition-transform(0.6s ease-in-out);\n      @include backface-visibility(hidden);\n      @include perspective(1000px);\n\n      &.next,\n      &.active.right {\n        @include translate3d(100%, 0, 0);\n        left: 0;\n      }\n      &.prev,\n      &.active.left {\n        @include translate3d(-100%, 0, 0);\n        left: 0;\n      }\n      &.next.left,\n      &.prev.right,\n      &.active {\n        @include translate3d(0, 0, 0);\n        left: 0;\n      }\n    }\n  }\n\n  > .active,\n  > .next,\n  > .prev {\n    display: block;\n  }\n\n  > .active {\n    left: 0;\n  }\n\n  > .next,\n  > .prev {\n    position: absolute;\n    top: 0;\n    width: 100%;\n  }\n\n  > .next {\n    left: 100%;\n  }\n  > .prev {\n    left: -100%;\n  }\n  > .next.left,\n  > .prev.right {\n    left: 0;\n  }\n\n  > .active.left {\n    left: -100%;\n  }\n  > .active.right {\n    left: 100%;\n  }\n\n}\n\n// Left/right controls for nav\n// ---------------------------\n\n.carousel-control {\n  position: absolute;\n  top: 0;\n  left: 0;\n  bottom: 0;\n  width: $carousel-control-width;\n  @include opacity($carousel-control-opacity);\n  font-size: $carousel-control-font-size;\n  color: $carousel-control-color;\n  text-align: center;\n  text-shadow: $carousel-text-shadow;\n  background-color: rgba(0, 0, 0, 0); // Fix IE9 click-thru bug\n  // We can't have this transition here because WebKit cancels the carousel\n  // animation if you trip this while in the middle of another animation.\n\n  // Set gradients for backgrounds\n  &.left {\n    @include gradient-horizontal($start-color: rgba(0,0,0,.5), $end-color: rgba(0,0,0,.0001));\n  }\n  &.right {\n    left: auto;\n    right: 0;\n    @include gradient-horizontal($start-color: rgba(0,0,0,.0001), $end-color: rgba(0,0,0,.5));\n  }\n\n  // Hover/focus state\n  &:hover,\n  &:focus {\n    outline: 0;\n    color: $carousel-control-color;\n    text-decoration: none;\n    @include opacity(.9);\n  }\n\n  // Toggles\n  .icon-prev,\n  .icon-next,\n  .glyphicon-chevron-left,\n  .glyphicon-chevron-right {\n    position: absolute;\n    top: 50%;\n    margin-top: -10px;\n    z-index: 5;\n    display: inline-block;\n  }\n  .icon-prev,\n  .glyphicon-chevron-left {\n    left: 50%;\n    margin-left: -10px;\n  }\n  .icon-next,\n  .glyphicon-chevron-right {\n    right: 50%;\n    margin-right: -10px;\n  }\n  .icon-prev,\n  .icon-next {\n    width:  20px;\n    height: 20px;\n    line-height: 1;\n    font-family: serif;\n  }\n\n\n  .icon-prev {\n    &:before {\n      content: '\\2039';// SINGLE LEFT-POINTING ANGLE QUOTATION MARK (U+2039)\n    }\n  }\n  .icon-next {\n    &:before {\n      content: '\\203a';// SINGLE RIGHT-POINTING ANGLE QUOTATION MARK (U+203A)\n    }\n  }\n}\n\n// Optional indicator pips\n//\n// Add an unordered list with the following class and add a list item for each\n// slide your carousel holds.\n\n.carousel-indicators {\n  position: absolute;\n  bottom: 10px;\n  left: 50%;\n  z-index: 15;\n  width: 60%;\n  margin-left: -30%;\n  padding-left: 0;\n  list-style: none;\n  text-align: center;\n\n  li {\n    display: inline-block;\n    width:  10px;\n    height: 10px;\n    margin: 1px;\n    text-indent: -999px;\n    border: 1px solid $carousel-indicator-border-color;\n    border-radius: 10px;\n    cursor: pointer;\n\n    // IE8-9 hack for event handling\n    //\n    // Internet Explorer 8-9 does not support clicks on elements without a set\n    // `background-color`. We cannot use `filter` since that's not viewed as a\n    // background color by the browser. Thus, a hack is needed.\n    // See https://developer.mozilla.org/en-US/docs/Web/Events/click#Internet_Explorer\n    //\n    // For IE8, we set solid black as it doesn't support `rgba()`. For IE9, we\n    // set alpha transparency for the best results possible.\n    background-color: #000 \\9; // IE8\n    background-color: rgba(0,0,0,0); // IE9\n  }\n  .active {\n    margin: 0;\n    width:  12px;\n    height: 12px;\n    background-color: $carousel-indicator-active-bg;\n  }\n}\n\n// Optional captions\n// -----------------------------\n// Hidden by default for smaller viewports\n.carousel-caption {\n  position: absolute;\n  left: 15%;\n  right: 15%;\n  bottom: 20px;\n  z-index: 10;\n  padding-top: 20px;\n  padding-bottom: 20px;\n  color: $carousel-caption-color;\n  text-align: center;\n  text-shadow: $carousel-text-shadow;\n  & .btn {\n    text-shadow: none; // No shadow for button elements in carousel-caption\n  }\n}\n\n\n// Scale up controls for tablets and up\n@media screen and (min-width: $screen-sm-min) {\n\n  // Scale up the controls a smidge\n  .carousel-control {\n    .glyphicon-chevron-left,\n    .glyphicon-chevron-right,\n    .icon-prev,\n    .icon-next {\n      width: ($carousel-control-font-size * 1.5);\n      height: ($carousel-control-font-size * 1.5);\n      margin-top: ($carousel-control-font-size / -2);\n      font-size: ($carousel-control-font-size * 1.5);\n    }\n    .glyphicon-chevron-left,\n    .icon-prev {\n      margin-left: ($carousel-control-font-size / -2);\n    }\n    .glyphicon-chevron-right,\n    .icon-next {\n      margin-right: ($carousel-control-font-size / -2);\n    }\n  }\n\n  // Show and left align the captions\n  .carousel-caption {\n    left: 20%;\n    right: 20%;\n    padding-bottom: 30px;\n  }\n\n  // Move up the indicators\n  .carousel-indicators {\n    bottom: 20px;\n  }\n}\n", "//\n// Utility classes\n// --------------------------------------------------\n\n\n// Floats\n// -------------------------\n\n.clearfix {\n  @include clearfix;\n}\n.center-block {\n  @include center-block;\n}\n.pull-right {\n  float: right !important;\n}\n.pull-left {\n  float: left !important;\n}\n\n\n// Toggling content\n// -------------------------\n\n// Note: Deprecated .hide in favor of .hidden or .sr-only (as appropriate) in v3.0.1\n.hide {\n  display: none !important;\n}\n.show {\n  display: block !important;\n}\n.invisible {\n  visibility: hidden;\n}\n.text-hide {\n  @include text-hide;\n}\n\n\n// Hide from screenreaders and browsers\n//\n// Credit: HTML5 Boilerplate\n\n.hidden {\n  display: none !important;\n}\n\n\n// For Affix plugin\n// -------------------------\n\n.affix {\n  position: fixed;\n}\n", "// Center-align a block level element\n\n@mixin center-block() {\n  display: block;\n  margin-left: auto;\n  margin-right: auto;\n}\n", "// CSS image replacement\n//\n// Heads up! v3 launched with only `.hide-text()`, but per our pattern for\n// mixins being reused as classes with the same name, this doesn't hold up. As\n// of v3.0.1 we have added `.text-hide()` and deprecated `.hide-text()`.\n//\n// Source: https://github.com/h5bp/html5-boilerplate/commit/aa0396eae757\n\n// Deprecated as of v3.0.1 (has been removed in v4)\n@mixin hide-text() {\n  font: 0/0 a;\n  color: transparent;\n  text-shadow: none;\n  background-color: transparent;\n  border: 0;\n}\n\n// New mixin to use as of v3.0.1\n@mixin text-hide() {\n  @include hide-text;\n}\n", "//\n// Responsive: Utility classes\n// --------------------------------------------------\n\n\n// IE10 in Windows (Phone) 8\n//\n// Support for responsive views via media queries is kind of borked in IE10, for\n// Surface/desktop in split view and for Windows Phone 8. This particular fix\n// must be accompanied by a snippet of JavaScript to sniff the user agent and\n// apply some conditional CSS to *only* the Surface/desktop Windows 8. Look at\n// our Getting Started page for more information on this bug.\n//\n// For more information, see the following:\n//\n// Issue: https://github.com/twbs/bootstrap/issues/10497\n// Docs: http://getbootstrap.com/getting-started/#support-ie10-width\n// Source: http://timkadlec.com/2013/01/windows-phone-8-and-device-width/\n// Source: http://timkadlec.com/2012/10/ie10-snap-mode-and-responsive-design/\n\n@at-root {\n  @-ms-viewport {\n    width: device-width;\n  }\n}\n\n\n// Visibility utilities\n// Note: Deprecated .visible-xs, .visible-sm, .visible-md, and .visible-lg as of v3.2.0\n\n@include responsive-invisibility('.visible-xs');\n@include responsive-invisibility('.visible-sm');\n@include responsive-invisibility('.visible-md');\n@include responsive-invisibility('.visible-lg');\n\n.visible-xs-block,\n.visible-xs-inline,\n.visible-xs-inline-block,\n.visible-sm-block,\n.visible-sm-inline,\n.visible-sm-inline-block,\n.visible-md-block,\n.visible-md-inline,\n.visible-md-inline-block,\n.visible-lg-block,\n.visible-lg-inline,\n.visible-lg-inline-block {\n  display: none !important;\n}\n\n@media (max-width: $screen-xs-max) {\n  @include responsive-visibility('.visible-xs');\n}\n.visible-xs-block {\n  @media (max-width: $screen-xs-max) {\n    display: block !important;\n  }\n}\n.visible-xs-inline {\n  @media (max-width: $screen-xs-max) {\n    display: inline !important;\n  }\n}\n.visible-xs-inline-block {\n  @media (max-width: $screen-xs-max) {\n    display: inline-block !important;\n  }\n}\n\n@media (min-width: $screen-sm-min) and (max-width: $screen-sm-max) {\n  @include responsive-visibility('.visible-sm');\n}\n.visible-sm-block {\n  @media (min-width: $screen-sm-min) and (max-width: $screen-sm-max) {\n    display: block !important;\n  }\n}\n.visible-sm-inline {\n  @media (min-width: $screen-sm-min) and (max-width: $screen-sm-max) {\n    display: inline !important;\n  }\n}\n.visible-sm-inline-block {\n  @media (min-width: $screen-sm-min) and (max-width: $screen-sm-max) {\n    display: inline-block !important;\n  }\n}\n\n@media (min-width: $screen-md-min) and (max-width: $screen-md-max) {\n  @include responsive-visibility('.visible-md');\n}\n.visible-md-block {\n  @media (min-width: $screen-md-min) and (max-width: $screen-md-max) {\n    display: block !important;\n  }\n}\n.visible-md-inline {\n  @media (min-width: $screen-md-min) and (max-width: $screen-md-max) {\n    display: inline !important;\n  }\n}\n.visible-md-inline-block {\n  @media (min-width: $screen-md-min) and (max-width: $screen-md-max) {\n    display: inline-block !important;\n  }\n}\n\n@media (min-width: $screen-lg-min) {\n  @include responsive-visibility('.visible-lg');\n}\n.visible-lg-block {\n  @media (min-width: $screen-lg-min) {\n    display: block !important;\n  }\n}\n.visible-lg-inline {\n  @media (min-width: $screen-lg-min) {\n    display: inline !important;\n  }\n}\n.visible-lg-inline-block {\n  @media (min-width: $screen-lg-min) {\n    display: inline-block !important;\n  }\n}\n\n@media (max-width: $screen-xs-max) {\n  @include responsive-invisibility('.hidden-xs');\n}\n\n@media (min-width: $screen-sm-min) and (max-width: $screen-sm-max) {\n  @include responsive-invisibility('.hidden-sm');\n}\n\n@media (min-width: $screen-md-min) and (max-width: $screen-md-max) {\n  @include responsive-invisibility('.hidden-md');\n}\n\n@media (min-width: $screen-lg-min) {\n  @include responsive-invisibility('.hidden-lg');\n}\n\n\n// Print utilities\n//\n// Media queries are placed on the inside to be mixin-friendly.\n\n// Note: Deprecated .visible-print as of v3.2.0\n\n@include responsive-invisibility('.visible-print');\n\n@media print {\n  @include responsive-visibility('.visible-print');\n}\n.visible-print-block {\n  display: none !important;\n\n  @media print {\n    display: block !important;\n  }\n}\n.visible-print-inline {\n  display: none !important;\n\n  @media print {\n    display: inline !important;\n  }\n}\n.visible-print-inline-block {\n  display: none !important;\n\n  @media print {\n    display: inline-block !important;\n  }\n}\n\n@media print {\n  @include responsive-invisibility('.hidden-print');\n}\n", "// Responsive utilities\n\n//\n// More easily include all the states for responsive-utilities.less.\n// [converter] $parent hack\n@mixin responsive-visibility($parent) {\n  #{$parent} {\n    display: block !important;\n  }\n  table#{$parent}  { display: table !important; }\n  tr#{$parent}     { display: table-row !important; }\n  th#{$parent},\n  td#{$parent}     { display: table-cell !important; }\n}\n\n// [converter] $parent hack\n@mixin responsive-invisibility($parent) {\n  #{$parent} {\n    display: none !important;\n  }\n}\n", "\n// Body\n$body-bg: #f5f8fa;\n\n// Borders\n$laravel-border-color: darken($body-bg, 10%);\n$list-group-border: $laravel-border-color;\n$navbar-default-border: $laravel-border-color;\n$panel-default-border: $laravel-border-color;\n$panel-inner-border: $laravel-border-color;\n\n// Brands\n$brand-primary: #e66531;\n$brand-info: #8eb4cb;\n$brand-success: #2ab27b;\n$brand-warning: #cbb956;\n$brand-danger: #bf5329;\n\n// Typography\n$font-family-sans-serif: \"Raleway\", sans-serif;\n$font-size-base: 14px;\n$line-height-base: 1.6;\n$text-color: #636b6f;\n\n// Navbar\n$navbar-default-bg: #fff;\n\n// Buttons\n$btn-default-color: $text-color;\n\n// Inputs\n$input-border: lighten($text-color, 40%);\n$input-border-focus: lighten($brand-primary, 25%);\n$input-color-placeholder: lighten($text-color, 30%);\n\n// Panels\n$panel-default-heading-bg: #fff;\n"]}