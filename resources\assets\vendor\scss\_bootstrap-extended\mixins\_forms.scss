// * Form controls
// *******************************************************************************

// Form control
@mixin template-form-control-theme($color) {
  .form-control:focus,
  .form-select:focus {
    border-color: $color !important;
    .form-floating-outline & {
      border-color: $color !important;
    }
  }

  // Using :focus-within to apply focus border color to default and merged input-group
  .input-group:not(.input-group-floating) {
    &:focus-within {
      .form-control,
      .input-group-text {
        border-color: $color;
      }
    }
  }
}

// Float labels
@mixin template-float-label-theme($color) {
  .form-floating {
    > .form-control:focus,
    > .form-control:focus:not(:placeholder-shown),
    > .form-select:focus,
    > .form-select:focus:not(:placeholder-shown) {
      ~ label {
        color: $color;
      }
    }
  }

  .form-floating-outline {
    :not(select):focus + {
      label,
      span {
        color: $color;
      }
    }
    label,
    > span {
      &::after {
        // Set $card-bg to floating label bg line
        background: $card-bg;
      }
    }

    // !FIX: using floating input label with bg-body
    label.bg-body,
    > span.bg-body {
      &::after {
        // Set $card-bg to floating label bg line
        background: $body-bg !important;
      }
    }
  }
}

// Form Switch
@mixin template-form-switch-theme($background) {
  $focus-color: $background;
  $focus-bg-image: str-replace(
    str-replace($form-switch-focus-bg-image, '#{$form-switch-focus-color}', $focus-color),
    '#',
    '%23'
  );

  $checked-color: $component-active-color;
  $checked-bg-image: str-replace(
    str-replace($form-switch-checked-bg-image, '#{$form-switch-checked-color}', $checked-color),
    '#',
    '%23'
  );

  .form-switch {
    .form-check-input {
      &:focus {
        background-image: escape-svg($focus-bg-image);
      }

      &:checked {
        @if $enable-gradients {
          background-image: escape-svg($checked-bg-image), var(--#{$variable-prefix}gradient);
        } @else {
          background-image: escape-svg($checked-bg-image);
        }
      }
    }
  }
}

// File Input
@mixin template-file-input-theme($color) {
  .form-control:focus ~ .form-label {
    border-color: $color;

    &::after {
      border-color: inherit;
    }
  }
}

// Form range variant
@mixin template-material-form-range-variant($parent, $background) {
  #{$parent} .form-range {
    // Chrome specific
    &::-webkit-slider-thumb {
      background-color: $background;
      &:hover {
        box-shadow: 0 0 0 8px rgba($background, 0.15), $floating-component-shadow;
      }
      &:active {
        background-color: $background;
        box-shadow: 0 0 0 10px rgba($background, 0.2), $floating-component-shadow;
      }
    }

    // Mozilla specific
    &::-moz-range-thumb {
      // background-color: $background;
      &:hover {
        box-shadow: 0 0 0 8px rgba($background, 0.15), $floating-component-shadow;
      }
      &:active {
        // background-color: $background;
        box-shadow: 0 0 0 10px rgba($background, 0.2), $floating-component-shadow;
      }
    }

    &::-webkit-slider-runnable-track {
      background-color: $background;
    }

    &::-moz-range-track {
      background-color: $background;
    }
  }
}

// Form Check
@mixin template-material-form-check-variant($parent, $background, $color: null) {
  $color: if($color, $color, color-contrast($background));
  $focus-border: $background;
  $focus-color: 0 0 $input-btn-focus-blur $input-focus-width rgba($color, $input-btn-focus-color-opacity);

  #{$parent} .form-check-input {
    &:focus {
      border-color: $focus-border;
    }

    &:active {
      border-color: $focus-border;
    }
    &:hover {
      &::after {
        background: $gray-50;
      }
    }
    &:checked {
      background-color: $background;
      border-color: $background;
      &::after {
        background: rgba($background, 0.08) !important;
      }
    }

    &[type='checkbox']:indeterminate {
      background-color: $background;
      border-color: $background;
    }
  }
}

@mixin template-form-check-theme($background, $color: null) {
  @include template-material-form-check-variant('', $background, $color);
}
