<?php

namespace App\Http\Controllers;
use Illuminate\Http\Request;
use App\Project;
use App\Sprint;
use App\UserStory;
use App\UserStoryGeneralNfr;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Maatwebsite\Excel\Facades\Excel;
use App\Exports\CIGReportExport;

class CIGController extends Controller
{
    public function index()
    {
        $user = Auth::user();
        $pro = \App\TeamMapping::getProjectsByUser($user->username); // use whereIn() to retrieve the projects that have a team_name value in the array
        return view('cig.index')
        ->with('title', 'CIG List')
            ->with('pros', $pro);
    }
    
    public function getRadarData($proj_id, $sprint_id = null)
    {
        // Fetch user stories based on project and optional sprint filter
        $userStoriesQuery = UserStory::where('proj_id', $proj_id);
    
        // If no sprint is selected, get data for all sprints
        if ($sprint_id) {
            $userStoriesQuery->where('sprint_id', $sprint_id);
        }
    
        // Get user stories with their related NFRs
        $userStories = $userStoriesQuery->with(['nfrs.generalNfr'])->get();
    
        // Initialize an array to count NFR occurrences for completeness calculation
        $nfrCounts = [];
        $userStoriesWithNfrs = []; // To track user stories that are linked to NFRs
    
        // Loop through each user story and its associated NFRs
        foreach ($userStories as $userStory) {
            foreach ($userStory->nfrs as $userStoryNfr) {
                $generalNfr = $userStoryNfr->generalNfr->general_nfr;
    
                // Track which user stories are linked to NFRs
                if (!isset($userStoriesWithNfrs[$userStory->u_id])) {
                    $userStoriesWithNfrs[$userStory->u_id] = true;
                }
    
                // Count NFR occurrences for each general NFR
                if (!isset($nfrCounts[$generalNfr])) {
                    $nfrCounts[$generalNfr] = 0;
                }
                $nfrCounts[$generalNfr]++;
            }
        }
    
        // Calculate the total number of distinct user stories that are linked to NFRs
        $totalUserStoriesWithNfrs = count($userStoriesWithNfrs);
    
        // Calculate completeness for each NFR
        $completeness = [];
        foreach ($nfrCounts as $generalNfr => $count) {
            $completeness[$generalNfr] = $totalUserStoriesWithNfrs > 0 ? round($count / $totalUserStoriesWithNfrs, 2) : 0;
        }
    
        // Add total user stories as a key to completeness
        $completeness['Total User Stories'] = 1;
    
        // Return the response with completeness and NFR types
        return response()->json([
            'completeness' => $completeness,
            'nfrTypes' => array_keys($nfrCounts) // Get the general NFR types
        ]);
    }
    

    
    public function create($proj_id)
    {
        // Get the project and related sprints using Eloquent
        $project = Project::getProjectById($proj_id);
        $sprints = Sprint::getSprintsByProjectName($project->proj_name);
    
        // Get the selected sprint ID from the request
        $selectedSprintId = request('filter_sprint_id', ''); // Default to all sprints
    
        // Fetch radar data based on selected sprint
        $radarData = $this->getRadarData($proj_id, $selectedSprintId);
    
        $paginatedUserStories = empty($selectedSprintId)
        ? UserStory::getUserStoriesWithNFRs($proj_id)
        : UserStory::getUserStoriesWithNFRsBySprint($proj_id, $selectedSprintId);
    
        $userStories = $this->displayUserStoriesWithNFRs($paginatedUserStories);
    
        return view('cig.create', [
            'title' => 'View CIG for ' . $project->proj_name,
            'proj_id' => $proj_id,
            'sprints' => $sprints,
            'userStories' => $userStories,
            'paginatedUserStories' => $paginatedUserStories, // Original paginated data for pagination links
            'selectedSprintId' => $selectedSprintId, // Pass selected sprint to the view
            'radarData' => $radarData, // Pass radar data to the view
        ]);
    }
    private function displayUserStoriesWithNFRs($paginatedUserStories)
    {
        return $paginatedUserStories->map(function ($story) {
            $nfrs = $story->nfrs()->with('generalNfr', 'specificNfr')->get();

            if ($nfrs->isEmpty()) {
                return null;
            }

            return [
                'u_id' => $story->u_id,
                'user_story' => $story->user_story,
                'sprint_id' => $story->sprint_id,
                'general_nfrs' => $nfrs->pluck('generalNfr.general_nfr')->toArray(),
                'specific_nfrs' => $nfrs->pluck('specificNfr.specific_nfr')->toArray(),
            ];
        })->filter();
    }
    
    public function generateXlsxReport($proj_id)
    {
        $project = Project::getProjectById($proj_id);
        $filterSprintId = request('filter_sprint_id', null);
        $sprintName = '';
        if ($filterSprintId) {
            $sprint = Sprint::getSprintById($filterSprintId);
            $sprintName = $sprint ? $sprint->sprint_name : 'UnknownSprint';
        }
        $filename = $project->proj_name . '_' . $sprintName . '_CIG_report.xlsx';
        return Excel::download(new CIGReportExport($proj_id,$filterSprintId), $filename);
    }

    public function uploadRadarChart(Request $request)
    {
        $base64Image = $request->input('image');
        $fileName = $request->input('fileName', 'radar_chart.png'); // Default to radar_chart.png if fileName is not provided
        
        $imageData = base64_decode(preg_replace('#^data:image/\w+;base64,#i', '', $base64Image));
        $imagePath = public_path('storage/' . $fileName);
        file_put_contents($imagePath, $imageData);
        
        return response()->json(['imagePath' => 'storage/' . $fileName]);
    }
}

