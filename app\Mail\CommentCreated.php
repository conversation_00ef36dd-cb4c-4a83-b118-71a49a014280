<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;

class CommentCreated extends Mailable
{
    use Queueable, SerializesModels;
    public $comment;
    public $task;
    public $project;
    public $sprint;
    public $subject;

    public function __construct($comment, $task, $project, $sprint, $subject)
    {
        $this->comment = $comment;
        $this->task = $task;
        $this->project = $project;
        $this->sprint = $sprint;
        $this->subject = $subject;
    }

    public function build()
    {
        return $this->subject($this->subject)
                    ->view('emails.commentCreated')
                    ->with([
                        'comment' => $this->comment,
                        'task' => $this->task,
                        'project' => $this->project,
                        'sprint' => $this->sprint,
                    ]);
    }
}