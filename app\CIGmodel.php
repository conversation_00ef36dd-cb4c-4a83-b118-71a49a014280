<?php

namespace App;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class CIGModel extends Model
{
    use HasFactory;

    protected $table = 'cig';

    protected $fillable = [
        'proj_id', 'sprint_id', 'u_id', 'general_nfr_id', 'specific_nfr_id',
    ];

    // Define the relationships
    public function userStory()
    {
        return $this->belongsTo(UserStory::class, 'u_id');
    }

    public function sprint()
    {
        return $this->belongsTo(Sprint::class, 'sprint_id');
    }

    public function generalNfr()
    {
        return $this->belongsTo(NFRmodel::class, 'general_nfr_id');
    }

    public function specificNfr()
    {
        return $this->belongsTo(NFR::class, 'specific_nfr_id');
    }

    public function project()
    {
        return $this->belongsTo(Project::class, 'proj_id');
    }
}