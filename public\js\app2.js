//CIG Radar Chart
document.addEventListener('DOMContentLoaded', function () {
    const sprintDropdown = document.getElementById('sprint');
    const projectId = sprintDropdown.getAttribute('data-project-id');
    const radarChartCanvas = document.getElementById('nfrRadarChart');
    let radarChart;

    // Function to extract URL parameters
    function getUrlParameter(name) {
        const urlParams = new URLSearchParams(window.location.search);
        return urlParams.get(name);
    }

    // Function to fetch radar data from backend
    async function fetchRadarData(projId, sprintId = '') {
        try {
            const response = await fetch(`/radar-data/${projId}/${sprintId}`);
            const data = await response.json();
            return data; // Return the complete response
        } catch (error) {
            console.error('Error fetching radar data:', error);
            return { completeness: {}, nfrTypes: [] }; // Return default values if there's an error
        }
    }

    // Function to initialize or update radar chart
    function initializeRadarChart(labels, data) {
        if (radarChart) {
            radarChart.data.labels = labels; // Update labels dynamically
            radarChart.data.datasets[0].data = data; // Update data dynamically
            radarChart.update(); // Update the chart with new data
        } else {
            // Initialize the radar chart if it hasn't been created yet
            radarChart = new Chart(radarChartCanvas.getContext('2d'), {
                type: 'radar',
                data: {
                    labels: labels,
                    datasets: [{
                        label: 'NFR Completeness',
                        data: data,
                        backgroundColor: 'rgba(54, 162, 235, 0.2)', // Light blue background
                        borderColor: 'rgba(54, 162, 235, 1)', // Blue border
                        borderWidth: 1
                    }]
                },
                options: {
                    scales: {
                        r: {
                            beginAtZero: true
                        }
                    },animation: {
                        onComplete: async () => {
                            // Convert the chart to a base64 image once rendering is complete
                            const base64Image = radarChart.toBase64Image();
                            await uploadRadarChartImage(base64Image);
                        }
                    }
                }
            });
        }
    }

    // Get the selected sprint ID from URL or default to empty string
    const selectedSprintId = getUrlParameter('filter_sprint_id') || '';

    // If there is a sprint ID in the URL, set it in the dropdown
    if (selectedSprintId && sprintDropdown) {
        sprintDropdown.value = selectedSprintId;
    }

    // Event listener for dropdown changes
    sprintDropdown.addEventListener('change', async function () {
        const selectedSprintId = sprintDropdown.value;
        const radarDataResponse = await fetchRadarData(projectId, selectedSprintId);
        const labels = [...radarDataResponse.nfrTypes, 'Total User Stories']; // Dynamic labels
        const data = labels.map(label => radarDataResponse.completeness[label] || 0); // Match data with labels
        initializeRadarChart(labels, data);
    });

    // Initial radar chart load for the first time or when the page is loaded
    (async function () {
        const radarDataResponse = await fetchRadarData(projectId, selectedSprintId); // Fetch data based on URL parameter
        
        // If no sprint filter is applied, ensure all NFRs for all sprints are included
        const labels = radarDataResponse.nfrTypes && radarDataResponse.nfrTypes.length > 0
            ? [...radarDataResponse.nfrTypes, 'Total User Stories']
            : ['No NFR Data']; // Fallback if no NFR data is returned
        
        const data = labels.map(label => radarDataResponse.completeness[label] || 0); // Default to 0 if no completeness data
    
        // Initialize radar chart with labels and data
        initializeRadarChart(labels, data);
    })();
    

    async function uploadRadarChartImage(base64Image) {
    try {
        const csrfToken = document.querySelector('meta[name="csrf-token"]').getAttribute('content'); // Get CSRF token
        
        const response = await fetch('/upload-radar-chart', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': csrfToken,  // Include CSRF token in headers
            },
            body: JSON.stringify({ image: base64Image })
        });

        if (response.ok) {
            console.log('Radar chart image uploaded successfully!');
        } else {
            console.error('Error uploading radar chart image.');
        }
    } catch (error) {
        console.error('Error sending radar chart image:', error);
    }
}

});
