<?php

namespace App;

use Illuminate\Database\Eloquent\Model;
use Carbon\Carbon;

class SequenceDiagram extends Model
{
    protected $fillable = [
        'name',
        'description',
        'project_id',
        'sprint_id',
        'user_id',
        'diagram_data',
        'lifelines',
        'objects_identified'
    ];

    protected $casts = [
        'diagram_data' => 'array',
        'lifelines' => 'array',
        'objects_identified' => 'boolean'
    ];

    /**
     * Get the project that owns the sequence diagram.
     */
    public function project()
    {
        return $this->belongsTo(Project::class);
    }

    /**
     * Get the sprint that owns the sequence diagram.
     */
    public function sprint()
    {
        return $this->belongsTo(Sprint::class);
    }

    /**
     * Get the user that created the sequence diagram.
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the objects for the sequence diagram.
     */
    public function objects()
    {
        return $this->hasMany(SequenceDiagramObject::class);
    }

    /**
     * Check if the diagram has lifelines.
     */
    public function hasLifelines()
    {
        return $this->lifelines && is_array($this->lifelines) && count($this->lifelines) > 0;
    }

    /**
     * Get formatted created at date.
     */
    public function getFormattedCreatedAtAttribute()
    {
        return $this->created_at->format('M d, Y H:i');
    }

    /**
     * Get count of identified objects.
     */
    public function getIdentifiedObjectsCountAttribute()
    {
        return $this->objects()->count();
    }

    /**
     * Get count of unknown objects.
     */
    public function getUnknownObjectsCountAttribute()
    {
        return $this->objects()->where('role', 'unknown')->count();
    }

    /**
     * Get available roles for objects.
     */
    public static function getAvailableRoles()
    {
        return [
            'controller' => 'Controller',
            'model' => 'Model',
            'view' => 'View',
            'service' => 'Service',
            'actor' => 'Actor',
            'entity' => 'Entity',
            'repository' => 'Repository',
            'helper' => 'Helper',
            'unknown' => 'Unknown'
        ];
    }

    /**
     * Get role color.
     */
    public static function getRoleColor($role)
    {
        $colors = [
            'controller' => '#007bff',
            'model' => '#28a745',
            'view' => '#ffc107',
            'service' => '#17a2b8',
            'actor' => '#6f42c1',
            'entity' => '#fd7e14',
            'repository' => '#20c997',
            'helper' => '#6c757d',
            'unknown' => '#dc3545'
        ];

        return $colors[$role] ?? '#6c757d';
    }

    /**
     * Get role icon.
     */
    public static function getRoleIcon($role)
    {
        $icons = [
            'controller' => 'mdi-cog',
            'model' => 'mdi-database',
            'view' => 'mdi-monitor',
            'service' => 'mdi-cloud',
            'actor' => 'mdi-account',
            'entity' => 'mdi-cube',
            'repository' => 'mdi-archive',
            'helper' => 'mdi-tools',
            'unknown' => 'mdi-help-circle'
        ];

        return $icons[$role] ?? 'mdi-help-circle';
    }
}
