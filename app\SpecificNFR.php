<?php

namespace App;

use Illuminate\Database\Eloquent\Model;

class SpecificNFR extends Model
{
    // Define the table name explicitly if it's not the plural form of the model name
    protected $table = 'nfr';

    // If you are using custom primary key, you can specify it
    protected $primaryKey = 'nfr_id';

    protected $fillable = [
        'general_nfr_id',
        'specific_nfr',
        'specific_nfr_desc',
        'created_at',
        'updated_at',
        'created_by',
    ];

    public function userStoryGeneralNfr()
    {
        return $this->hasMany(UserStoryGeneralNfr::class, 'general_nfr_id', 'general_nfr_id');
    }
    public static function getNFRData()
    {
        $grouped = [];
        $ids = [];

        $generalIds = GeneralNFR::pluck('general_nfr_id')->toArray();

        foreach ($generalIds as $generalId) {
            $grouped[$generalId] = self::where('general_nfr_id', $generalId)
                ->pluck('specific_nfr')
                ->toArray();

            $ids[$generalId] = self::where('general_nfr_id', $generalId)
                ->pluck('nfr_id')
                ->toArray();
        }

        return ['specificNFRs' => $grouped, 'specificNFRIds' => $ids];
    }
    
    public static function getByGeneralNfrId($general_nfr_id, $perPage = 8)
    {
        return self::where('general_nfr_id', $general_nfr_id)->paginate($perPage);
    }

    public static function findSpecificNFRById($specific_nfr_id)
    {
        return self::where('nfr_id', $specific_nfr_id)->first();
    }

    public static function getSpecificNFRList()
    {
        return self::pluck('specific_nfr', 'nfr_id')->toArray();
    }
    // If your timestamps are not being used, disable them (optional)
    public $timestamps = true;
}