<?php

namespace App\Exports;

use App\UserStory;
use App\Sprint;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithStyles;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use PhpOffice\PhpSpreadsheet\Style\Alignment;
use PhpOffice\PhpSpreadsheet\Style\Border;
use Illuminate\Support\Facades\DB;
use Maatwebsite\Excel\Concerns\WithDrawings;
use PhpOffice\PhpSpreadsheet\Worksheet\Drawing;

class CIGReportExport implements FromCollection, WithStyles, ShouldAutoSize, WithDrawings
{
    protected $proj_id;
    protected $filter_sprint_id;
    protected $radarChartImagePath;

    public function __construct($proj_id, $filter_sprint_id, $radarChartFileName = 'radar_chart.png')
    {
        $this->proj_id = $proj_id;
        $this->filter_sprint_id = $filter_sprint_id;
        $this->radarChartImagePath = public_path("storage/{$radarChartFileName}");

    }

    public function collection()
    {
        $emptyRows = array_fill(0, 21, ['', '', '']);
        $header = [['Sprint', 'User Story', 'Linked General NFR', 'Linked Specific NFR']];
        // Filter user stories by project ID and sprint ID
        $query = UserStory::where('proj_id', $this->proj_id);
    
        if (!empty($this->filter_sprint_id)) {
            $query->where('sprint_id', $this->filter_sprint_id); // Apply sprint filter
        }
    
        $userStories = $query->get();
        $data = [];
    
        // Loop through each user story and fetch related NFRs
        foreach ($userStories as $userStory) {
            $sprintName = Sprint::where('sprint_id', $userStory->sprint_id)->value('sprint_name') ?? 'N/A'; // Get sprint name
            $nfrs = DB::table('user_story_general_nfr')
                ->where('user_story_id', $userStory->u_id)
                ->join('generalnfr', 'user_story_general_nfr.general_nfr_id', '=', 'generalnfr.general_nfr_id')
                ->join('nfr', 'user_story_general_nfr.specific_nfr_id', '=', 'nfr.nfr_id')
                ->select('generalnfr.general_nfr', 'nfr.specific_nfr')
                ->get();
    
                if ($nfrs->isEmpty()) {
                    continue; // Skip user stories without linked NFRs
                }
                foreach ($nfrs as $index => $nfr) {
                    $data[] = [
                        $index === 0 ? $sprintName : '',
                        $index === 0 ? $userStory->user_story : '',
                        $nfr->general_nfr,
                        $nfr->specific_nfr,
                    ];
                }
        }
    
        return collect(array_merge($emptyRows, $header, $data));
    }

    public function styles(\PhpOffice\PhpSpreadsheet\Worksheet\Worksheet $sheet)
    {
        $query = UserStory::where('proj_id', $this->proj_id);

        if (!empty($this->filter_sprint_id)) {
            $query->where('sprint_id', $this->filter_sprint_id);
        }

        $userStories = $query->get();
        $startRow = 23; // Data starts from row 23 (after 21 empty rows + 1 header)
        
        foreach ($userStories as $userStory) {
            $nfrs = DB::table('user_story_general_nfr')
                ->where('user_story_id', $userStory->u_id)
                ->join('generalnfr', 'user_story_general_nfr.general_nfr_id', '=', 'generalnfr.general_nfr_id')
                ->join('nfr', 'user_story_general_nfr.specific_nfr_id', '=', 'nfr.nfr_id')
                ->select('generalnfr.general_nfr', 'nfr.specific_nfr')
                ->get();
            if ($nfrs->isEmpty()) {
                continue; // Skip user stories without linked NFRs
            }
            $rowCount = $nfrs->count();
            if ($rowCount > 0) {
             $endRow = $startRow + $rowCount - 1;

                // Ensure valid range
                if ($startRow < $endRow) {
                    $sheet->mergeCells("A{$startRow}:A{$endRow}"); // Merge Sprint column
                    $sheet->mergeCells("B{$startRow}:B{$endRow}"); // Merge User Story column
                }
            }

            $startRow += $rowCount ?: 1;
        }

        // Set specific column widths
        $sheet->getColumnDimension('A')->setWidth(20); // Sprint column
        $sheet->getColumnDimension('B')->setWidth(80); // User Story column
        $sheet->getColumnDimension('C')->setWidth(30); // General NFR column
        $sheet->getColumnDimension('D')->setWidth(30); // Specific NFR column
        $sheet->getStyle('A22:D' . $sheet->getHighestRow())->getAlignment()->setWrapText(true);
        
        // Apply styles
        $sheet->getStyle('A22:D22')->getFont()->setBold(true);
        $sheet->getStyle('A22:D22')->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER);
        $sheet->getStyle('A22:D' . $sheet->getHighestRow())->applyFromArray([
            'borders' => [
                'allBorders' => ['borderStyle' => Border::BORDER_THIN],
            ],
        ]);
        
        $sheet->getColumnDimension('A')->setAutoSize(false); // Disable auto size for column A
        $sheet->getColumnDimension('B')->setAutoSize(false); // Disable auto size for column B
        $sheet->getColumnDimension('C')->setAutoSize(false); // Disable auto size for column C
        $sheet->getColumnDimension('D')->setAutoSize(false); // Disable auto size for column D

        return [];
    }
    public function drawings()
    {
        $drawing = new Drawing();
        $drawing->setPath(public_path('storage/radar_chart.png'));
        $drawing->setHeight(400);
        $drawing->setCoordinates('B1');

        return $drawing;
    }    
}
