<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Project;
use App\Sprint;
use App\GeneralNFR;
use App\SpecificNFR;
use App\UserStoryGeneralNfr;
use App\TeamMapping;
use Illuminate\Support\Facades\DB;

class TVTController extends Controller
{
    public function index()
    {
        $user = \Auth::user();
        $projects = TeamMapping::getProjectsByUser($user->username);

        return view('tvt.index')
            ->with('title', 'TVT: List of Projects')
            ->with('pros', $projects);
    }

    public function show(Request $request, $proj_id)
{
    // Fetch the project details
    $project = Project::getProjectById($proj_id);

    // Prepare dropdown/filter options
    $sprints = Sprint::getSprintsByProjectId($proj_id);
    $generalNfrs = GeneralNFR::getGeneralNFRList();
    $specificNfrs = SpecificNFR::getSpecificNFRList();

    // Build filters from request
    $filters = [
        'sprint' => $request->input('sprint'),
        'general_nfr' => $request->input('general_nfr'),
        'nfr' => $request->input('nfr'),
    ];

    // Fetch filtered TVT results using user-defined function
    $results = UserStoryGeneralNfr::getFilteredTVT($proj_id, $filters, 8);

    // Return the view with data
    return view('tvt.view', [
        'title' => 'View TVT for ' . $project->proj_name,
        'project' => $project,
        'results' => $results,
        'sprints' => $sprints,
        'generalNfrs' => $generalNfrs,
        'specificNfrs' => $specificNfrs,
    ]);
}
}