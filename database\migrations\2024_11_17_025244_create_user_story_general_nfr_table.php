<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateUserStoryGeneralNfrTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    // database/migrations/YYYY_MM_DD_create_user_story_general_nfr_table.php
public function up()
{
    Schema::create('user_story_general_nfr', function (Blueprint $table) {
        $table->id();
        $table->unsignedBigInteger('user_story_id');
        $table->unsignedBigInteger('general_nfr_id');
        $table->BigInteger('specific_nfr_id');
        $table->timestamps();

        // Foreign keys
        $table->foreign('user_story_id')->references('u_id')->on('user_stories')->onDelete('cascade');
        $table->foreign('general_nfr_id')->references('general_nfr_id')->on('generalnfr')->onDelete('cascade');
        $table->foreign('specific_nfr_id')->references('nfr_id')->on('nfr')->onDelete('cascade');
    });
}


    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('user_story_general_nfr', function (Blueprint $table) {
            $table->dropColumn(['created_at', 'updated_at']);
        });
    }
}
