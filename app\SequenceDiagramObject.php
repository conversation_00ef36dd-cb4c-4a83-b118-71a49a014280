<?php

namespace App;

use Illuminate\Database\Eloquent\Model;
use Carbon\Carbon;

class SequenceDiagramObject extends Model
{
    protected $fillable = [
        'sequence_diagram_id',
        'name',
        'role',
        'position_x',
        'position_y',
        'identified_at'
    ];

    protected $casts = [
        'identified_at' => 'datetime'
    ];

    /**
     * Get the sequence diagram that owns the object.
     */
    public function sequenceDiagram()
    {
        return $this->belongsTo(SequenceDiagram::class);
    }

    /**
     * Get the role color.
     */
    public function getRoleColorAttribute()
    {
        return SequenceDiagram::getRoleColor($this->role);
    }

    /**
     * Get the role icon.
     */
    public function getRoleIconAttribute()
    {
        return SequenceDiagram::getRoleIcon($this->role);
    }

    /**
     * Check if the object role is unknown.
     */
    public function isUnknown()
    {
        return $this->role === 'unknown';
    }

    /**
     * Get formatted identified at date.
     */
    public function getFormattedIdentifiedAtAttribute()
    {
        return $this->identified_at ? $this->identified_at->format('M d, Y H:i') : null;
    }
}
