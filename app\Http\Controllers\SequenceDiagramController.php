<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\SequenceDiagram;
use App\SequenceDiagramObject;

class SequenceDiagramController extends Controller
{
    /**
     * Display a listing of sequence diagrams.
     */
    public function index()
    {
        $sequenceDiagrams = SequenceDiagram::with(['project', 'sprint', 'objects'])
            ->orderBy('created_at', 'desc')
            ->get();

        return view('sequence-diagram.index', [
            'title' => 'Sequence Diagrams',
            'sequenceDiagrams' => $sequenceDiagrams
        ]);
    }

    /**
     * Display the specified sequence diagram.
     */
    public function show($id)
    {
        $sequenceDiagram = SequenceDiagram::with(['project', 'sprint', 'objects'])
            ->findOrFail($id);

        $availableRoles = SequenceDiagram::getAvailableRoles();

        return view('sequence-diagram.show', [
            'title' => $sequenceDiagram->name,
            'sequenceDiagram' => $sequenceDiagram,
            'availableRoles' => $availableRoles
        ]);
    }

    /**
     * Identify objects in the sequence diagram.
     */
    public function identifyObjects(Request $request, $id)
    {
        $sequenceDiagram = SequenceDiagram::findOrFail($id);

        // Check if objects are already identified
        if ($sequenceDiagram->objects_identified) {
            return response()->json([
                'success' => false,
                'message' => 'Objects have already been identified for this diagram.'
            ]);
        }

        // Check if diagram has lifelines
        if (!$sequenceDiagram->hasLifelines()) {
            return response()->json([
                'success' => false,
                'message' => 'No lifelines found in this sequence diagram.'
            ]);
        }

        $identifiedCount = 0;
        $unknownCount = 0;

        // Process each lifeline
        foreach ($sequenceDiagram->lifelines as $index => $lifeline) {
            $objectName = $lifeline['name'];
            
            // Skip if object already exists
            if ($sequenceDiagram->objects()->where('name', $objectName)->exists()) {
                continue;
            }

            // Identify role based on naming patterns
            $role = $this->identifyRoleFromName($objectName);
            
            if ($role === 'unknown') {
                $unknownCount++;
            } else {
                $identifiedCount++;
            }

            // Create the object
            SequenceDiagramObject::create([
                'sequence_diagram_id' => $sequenceDiagram->id,
                'name' => $objectName,
                'role' => $role,
                'position_x' => $lifeline['position_x'] ?? ($index * 120 + 50),
                'position_y' => $lifeline['position_y'] ?? 50,
                'identified_at' => now()
            ]);
        }

        // Mark diagram as having objects identified
        $sequenceDiagram->update(['objects_identified' => true]);

        $message = "Object identification completed successfully. {$identifiedCount} objects identified.";
        $warning = $unknownCount > 0;

        return response()->json([
            'success' => true,
            'message' => $message,
            'warning' => $warning,
            'identified_count' => $identifiedCount,
            'unknown_count' => $unknownCount
        ]);
    }

    /**
     * Update the role of a specific object.
     */
    public function updateRole(Request $request, $id)
    {
        $request->validate([
            'object_id' => 'required|integer',
            'role' => 'required|string|in:controller,model,view,service,actor,entity,repository,helper,unknown'
        ]);

        $sequenceDiagram = SequenceDiagram::findOrFail($id);
        $object = SequenceDiagramObject::where('sequence_diagram_id', $sequenceDiagram->id)
            ->where('id', $request->object_id)
            ->firstOrFail();

        $oldRole = $object->role;
        $object->update(['role' => $request->role]);

        return response()->json([
            'success' => true,
            'message' => "Role updated from '{$oldRole}' to '{$request->role}' for object '{$object->name}'."
        ]);
    }

    /**
     * Identify role based on object name patterns.
     */
    private function identifyRoleFromName($name)
    {
        $name = strtolower($name);

        // Controller patterns
        if (str_contains($name, 'controller') || str_ends_with($name, 'ctrl')) {
            return 'controller';
        }

        // Model patterns
        if (str_contains($name, 'model') || str_contains($name, 'entity') || 
            str_ends_with($name, 'model') || str_ends_with($name, 'entity')) {
            return 'model';
        }

        // View patterns
        if (str_contains($name, 'view') || str_contains($name, 'page') || 
            str_contains($name, 'form') || str_contains($name, 'ui')) {
            return 'view';
        }

        // Service patterns
        if (str_contains($name, 'service') || str_contains($name, 'manager') || 
            str_ends_with($name, 'service') || str_ends_with($name, 'manager')) {
            return 'service';
        }

        // Repository patterns
        if (str_contains($name, 'repository') || str_contains($name, 'repo') || 
            str_ends_with($name, 'repository') || str_ends_with($name, 'repo')) {
            return 'repository';
        }

        // Helper patterns
        if (str_contains($name, 'helper') || str_contains($name, 'util') || 
            str_contains($name, 'utility') || str_ends_with($name, 'helper')) {
            return 'helper';
        }

        // Actor patterns (users, external systems)
        if (str_contains($name, 'user') || str_contains($name, 'admin') || 
            str_contains($name, 'client') || str_contains($name, 'actor') ||
            str_contains($name, 'system') || str_contains($name, 'external')) {
            return 'actor';
        }

        // Entity patterns (data objects)
        if (str_contains($name, 'data') || str_contains($name, 'object') || 
            str_contains($name, 'dto') || str_contains($name, 'vo')) {
            return 'entity';
        }

        // Default to unknown if no pattern matches
        return 'unknown';
    }
}
