
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sequence Diagram Object Identification - Demo</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Material Design Icons -->
    <link href="https://cdn.jsdelivr.net/npm/@mdi/font@7.2.96/css/materialdesignicons.min.css" rel="stylesheet">
    
    <!-- Custom CSS -->
    <style>
        /* Sequence Diagram Styles */
        .sequence-diagram-container {
            min-height: 400px;
            background: #f8f9fa;
            border-radius: 8px;
            padding: 20px;
            position: relative;
            overflow-x: auto;
        }

        .lifelines-container {
            position: relative;
            min-height: 300px;
            width: 100%;
            overflow-x: auto;
        }

        .lifeline {
            position: absolute;
            top: 0;
            width: 100px;
            text-align: center;
        }

        .lifeline-header {
            background: white;
            border: 2px solid #dee2e6;
            border-radius: 8px;
            padding: 10px 8px;
            margin-bottom: 10px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
        }

        .lifeline-header:hover {
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
            transform: translateY(-2px);
        }

        .lifeline-name {
            font-weight: 600;
            font-size: 12px;
            color: #495057;
            margin-bottom: 5px;
            word-wrap: break-word;
        }

        .lifeline-role {
            font-size: 10px;
            color: white;
            padding: 2px 6px;
            border-radius: 12px;
            display: inline-flex;
            align-items: center;
            font-weight: 500;
            text-shadow: 0 1px 2px rgba(0,0,0,0.2);
        }

        .lifeline-line {
            width: 2px;
            height: 250px;
            background: #dee2e6;
            margin: 0 auto;
            border-radius: 1px;
            position: relative;
        }

        .lifeline-line::before {
            content: '';
            position: absolute;
            top: 0;
            left: -1px;
            width: 4px;
            height: 100%;
            background: linear-gradient(to bottom, #007bff, #6c757d);
            border-radius: 2px;
            opacity: 0.3;
        }

        .interactions-container {
            background: white;
            border-radius: 8px;
            padding: 15px;
            border: 1px solid #dee2e6;
        }

        .interaction-item {
            display: flex;
            align-items: center;
            padding: 8px 0;
            border-bottom: 1px solid #f1f3f4;
            font-size: 13px;
        }

        .interaction-item:last-child {
            border-bottom: none;
        }

        .interaction-from,
        .interaction-to {
            font-weight: 600;
            color: #495057;
            background: #e9ecef;
            padding: 2px 8px;
            border-radius: 4px;
            font-size: 11px;
        }

        .interaction-message {
            color: #6c757d;
            font-style: italic;
            margin-left: 8px;
        }

        .object-item {
            transition: all 0.3s ease;
            background: #f8f9fa;
        }

        .object-item:hover {
            background: #e9ecef;
            transform: translateY(-1px);
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .object-item.border-warning {
            border-color: #ffc107 !important;
            background: #fff3cd;
        }

        .pulse-animation {
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% { transform: scale(1); opacity: 1; }
            50% { transform: scale(1.05); opacity: 0.8; }
            100% { transform: scale(1); opacity: 1; }
        }

        .card-icon .badge {
            width: 3rem;
            height: 3rem;
            display: flex;
            align-items: center;
            justify-content: center;
        }
    </style>
</head>
<body>
    <div class="container-fluid py-4">
        <!-- Header -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <div>
                            <h5 class="mb-1">
                                <i class="mdi mdi-chart-timeline-variant me-2"></i>
                                User Login Sequence Diagram
                            </h5>
                            <div class="d-flex gap-2 mt-2">
                                <span class="badge bg-label-primary">SAgile Project</span>
                                <span class="badge bg-label-info">Sprint 1</span>
                                <span class="badge bg-success">
                                    <i class="mdi mdi-check me-1"></i>
                                    Objects Identified
                                </span>
                            </div>
                        </div>
                        <div class="d-flex gap-2">
                            <button class="btn btn-outline-secondary btn-sm">
                                <i class="mdi mdi-arrow-left me-1"></i>
                                Back to List
                            </button>
                            <button class="btn btn-primary btn-sm" id="identifyObjectsBtn">
                                <i class="mdi mdi-auto-fix me-1"></i>
                                Re-identify Objects
                            </button>
                        </div>
                    </div>
                    <div class="card-body pt-0">
                        <p class="text-muted mb-0">Sequence diagram showing the user authentication process flow</p>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <!-- Sequence Diagram Display -->
            <div class="col-lg-8 col-12 mb-4">
                <div class="card h-100">
                    <div class="card-header">
                        <h6 class="card-title mb-0">Sequence Diagram</h6>
                    </div>
                    <div class="card-body">
                        <div class="sequence-diagram-container">
                            <!-- Lifelines Display -->
                            <div class="lifelines-container">
                                <div class="lifeline" style="left: 50px;">
                                    <div class="lifeline-header">
                                        <div class="lifeline-name">UserController</div>
                                        <div class="lifeline-role" style="background-color: #007bff;">
                                            <i class="mdi mdi-cog me-1"></i>
                                            Controller
                                        </div>
                                    </div>
                                    <div class="lifeline-line"></div>
                                </div>
                                
                                <div class="lifeline" style="left: 200px;">
                                    <div class="lifeline-header">
                                        <div class="lifeline-name">AuthService</div>
                                        <div class="lifeline-role" style="background-color: #17a2b8;">
                                            <i class="mdi mdi-cloud me-1"></i>
                                            Service
                                        </div>
                                    </div>
                                    <div class="lifeline-line"></div>
                                </div>
                                
                                <div class="lifeline" style="left: 350px;">
                                    <div class="lifeline-header">
                                        <div class="lifeline-name">UserModel</div>
                                        <div class="lifeline-role" style="background-color: #28a745;">
                                            <i class="mdi mdi-database me-1"></i>
                                            Model
                                        </div>
                                    </div>
                                    <div class="lifeline-line"></div>
                                </div>
                                
                                <div class="lifeline" style="left: 500px;">
                                    <div class="lifeline-header">
                                        <div class="lifeline-name">LoginView</div>
                                        <div class="lifeline-role" style="background-color: #ffc107; color: #212529;">
                                            <i class="mdi mdi-monitor me-1"></i>
                                            View
                                        </div>
                                    </div>
                                    <div class="lifeline-line"></div>
                                </div>
                                
                                <div class="lifeline" style="left: 650px;">
                                    <div class="lifeline-header">
                                        <div class="lifeline-name">User</div>
                                        <div class="lifeline-role" style="background-color: #6f42c1;">
                                            <i class="mdi mdi-account me-1"></i>
                                            Actor
                                        </div>
                                    </div>
                                    <div class="lifeline-line"></div>
                                </div>
                            </div>

                            <!-- Interactions Display -->
                            <div class="interactions-container mt-4">
                                <h6 class="mb-3">Interactions</h6>
                                <div class="interactions-list">
                                    <div class="interaction-item">
                                        <span class="interaction-from">User</span>
                                        <i class="mdi mdi-arrow-right mx-2"></i>
                                        <span class="interaction-to">LoginView</span>
                                        <span class="interaction-message">: Enter credentials</span>
                                    </div>
                                    <div class="interaction-item">
                                        <span class="interaction-from">LoginView</span>
                                        <i class="mdi mdi-arrow-right mx-2"></i>
                                        <span class="interaction-to">UserController</span>
                                        <span class="interaction-message">: login(username, password)</span>
                                    </div>
                                    <div class="interaction-item">
                                        <span class="interaction-from">UserController</span>
                                        <i class="mdi mdi-arrow-right mx-2"></i>
                                        <span class="interaction-to">AuthService</span>
                                        <span class="interaction-message">: authenticate(credentials)</span>
                                    </div>
                                    <div class="interaction-item">
                                        <span class="interaction-from">AuthService</span>
                                        <i class="mdi mdi-arrow-right mx-2"></i>
                                        <span class="interaction-to">UserModel</span>
                                        <span class="interaction-message">: findUser(username)</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Objects Panel -->
            <div class="col-lg-4 col-12 mb-4">
                <div class="card h-100">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h6 class="card-title mb-0">Identified Objects</h6>
                        <span class="badge bg-label-success">5 Objects</span>
                    </div>
                    <div class="card-body">
                        <div class="objects-list">
                            <div class="object-item mb-3 p-3 border rounded">
                                <div class="d-flex justify-content-between align-items-start mb-2">
                                    <div>
                                        <h6 class="mb-1">UserController</h6>
                                        <div class="d-flex align-items-center">
                                            <span class="badge me-2" style="background-color: #007bff; color: white;">
                                                <i class="mdi mdi-cog me-1"></i>
                                                Controller
                                            </span>
                                        </div>
                                    </div>
                                    <div class="dropdown">
                                        <button type="button" class="btn btn-sm btn-outline-secondary dropdown-toggle" 
                                                data-bs-toggle="dropdown">
                                            <i class="mdi mdi-pencil"></i>
                                        </button>
                                        <ul class="dropdown-menu">
                                            <li><h6 class="dropdown-header">Update Role</h6></li>
                                            <li><a class="dropdown-item" href="#"><i class="mdi mdi-cog me-2"></i>Controller</a></li>
                                            <li><a class="dropdown-item" href="#"><i class="mdi mdi-database me-2"></i>Model</a></li>
                                            <li><a class="dropdown-item" href="#"><i class="mdi mdi-monitor me-2"></i>View</a></li>
                                        </ul>
                                    </div>
                                </div>
                                <small class="text-muted">
                                    <i class="mdi mdi-clock-outline me-1"></i>
                                    Identified: Dec 25, 2024 14:30
                                </small>
                            </div>

                            <div class="object-item mb-3 p-3 border rounded">
                                <div class="d-flex justify-content-between align-items-start mb-2">
                                    <div>
                                        <h6 class="mb-1">AuthService</h6>
                                        <div class="d-flex align-items-center">
                                            <span class="badge me-2" style="background-color: #17a2b8; color: white;">
                                                <i class="mdi mdi-cloud me-1"></i>
                                                Service
                                            </span>
                                        </div>
                                    </div>
                                    <div class="dropdown">
                                        <button type="button" class="btn btn-sm btn-outline-secondary dropdown-toggle" 
                                                data-bs-toggle="dropdown">
                                            <i class="mdi mdi-pencil"></i>
                                        </button>
                                        <ul class="dropdown-menu">
                                            <li><h6 class="dropdown-header">Update Role</h6></li>
                                            <li><a class="dropdown-item" href="#"><i class="mdi mdi-cloud me-2"></i>Service</a></li>
                                            <li><a class="dropdown-item" href="#"><i class="mdi mdi-cog me-2"></i>Controller</a></li>
                                        </ul>
                                    </div>
                                </div>
                                <small class="text-muted">
                                    <i class="mdi mdi-clock-outline me-1"></i>
                                    Identified: Dec 25, 2024 14:30
                                </small>
                            </div>

                            <div class="object-item mb-3 p-3 border rounded border-warning">
                                <div class="d-flex justify-content-between align-items-start mb-2">
                                    <div>
                                        <h6 class="mb-1">SomeHelper</h6>
                                        <div class="d-flex align-items-center">
                                            <span class="badge me-2 pulse-animation" style="background-color: #dc3545; color: white;">
                                                <i class="mdi mdi-help-circle me-1"></i>
                                                Unknown
                                            </span>
                                            <small class="text-warning">
                                                <i class="mdi mdi-alert-circle-outline me-1"></i>
                                                Needs Review
                                            </small>
                                        </div>
                                    </div>
                                    <div class="dropdown">
                                        <button type="button" class="btn btn-sm btn-outline-warning dropdown-toggle" 
                                                data-bs-toggle="dropdown">
                                            <i class="mdi mdi-pencil"></i>
                                        </button>
                                        <ul class="dropdown-menu">
                                            <li><h6 class="dropdown-header">Update Role</h6></li>
                                            <li><a class="dropdown-item" href="#"><i class="mdi mdi-tools me-2"></i>Helper</a></li>
                                            <li><a class="dropdown-item" href="#"><i class="mdi mdi-cloud me-2"></i>Service</a></li>
                                            <li><a class="dropdown-item" href="#"><i class="mdi mdi-help-circle me-2"></i>Unknown</a></li>
                                        </ul>
                                    </div>
                                </div>
                                <small class="text-muted">
                                    <i class="mdi mdi-clock-outline me-1"></i>
                                    Identified: Dec 25, 2024 14:30
                                </small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Confirmation Modal -->
    <div class="modal fade" id="identifyObjectsModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="mdi mdi-auto-fix me-2"></i>
                        Identify Objects
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="text-center mb-3">
                        <i class="mdi mdi-help-circle-outline" style="font-size: 48px; color: #007bff;"></i>
                    </div>
                    <h6 class="text-center mb-3">Are you sure you want to proceed with identifying objects?</h6>
                    <p class="text-muted text-center">
                        The system will automatically analyze the lifelines in this sequence diagram and assign appropriate roles 
                        based on naming patterns and contextual data.
                    </p>
                    <div class="alert alert-info">
                        <i class="mdi mdi-information-outline me-2"></i>
                        <strong>Note:</strong> You can manually update any role assignments after the automatic identification is complete.
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-primary" id="confirmIdentify">
                        <i class="mdi mdi-check me-1"></i>
                        OK, Proceed
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        document.getElementById('identifyObjectsBtn').addEventListener('click', function() {
            const modal = new bootstrap.Modal(document.getElementById('identifyObjectsModal'));
            modal.show();
        });

        document.getElementById('confirmIdentify').addEventListener('click', function() {
            const modal = bootstrap.Modal.getInstance(document.getElementById('identifyObjectsModal'));
            modal.hide();
            
            // Show success notification
            const notification = document.createElement('div');
            notification.className = 'alert alert-success alert-dismissible fade show position-fixed';
            notification.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
            notification.innerHTML = `
                <i class="mdi mdi-check-circle me-2"></i>
                Object identification completed successfully.
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;
            document.body.appendChild(notification);
            
            setTimeout(() => {
                notification.remove();
            }, 5000);
        });
    </script>
</body>
</html>
