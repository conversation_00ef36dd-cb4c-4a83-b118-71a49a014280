{"__meta": {"id": "X4c80e295a64717f10b67f869fd3d67ec", "datetime": "2025-06-25 18:46:43", "utime": 1750848403.401285, "method": "GET", "uri": "/login", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 1, "messages": [{"message": "[18:46:43] LOG.warning: Callables of the form [\"Swift_SmtpTransport\", \"Swift_Transport_EsmtpTransport::__construct\"] are deprecated in C:\\xampp\\htdocs\\SAgilePMT_UTM-1\\vendor\\swiftmailer\\swiftmailer\\lib\\classes\\Swift\\SmtpTransport.php on line 36", "message_html": null, "is_string": false, "label": "warning", "time": 1750848403.266011, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1750848402.74981, "end": 1750848403.40131, "duration": 0.6514999866485596, "duration_str": "651ms", "measures": [{"label": "Booting", "start": 1750848402.74981, "relative_start": 0, "end": 1750848403.230297, "relative_end": 1750848403.230297, "duration": 0.4804871082305908, "duration_str": "480ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}, {"label": "Application", "start": 1750848403.230309, "relative_start": 0.4804990291595459, "end": 1750848403.401313, "relative_end": 3.0994415283203125e-06, "duration": 0.171004056930542, "duration_str": "171ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}]}, "memory": {"peak_usage": 22579448, "peak_usage_str": "22MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 7, "templates": [{"name": "auth.login (\\resources\\views\\auth\\login.blade.php)", "param_count": 0, "params": [], "type": "blade"}, {"name": "inc.page-auth (\\resources\\views\\inc\\page-auth.blade.php)", "param_count": 4, "params": ["__env", "app", "menuData", "errors"], "type": "blade"}, {"name": "layouts.blankLayout (\\resources\\views\\layouts\\blankLayout.blade.php)", "param_count": 4, "params": ["__env", "app", "menuData", "errors"], "type": "blade"}, {"name": "layouts.commonMaster (\\resources\\views\\layouts\\commonMaster.blade.php)", "param_count": 4, "params": ["__env", "app", "menuData", "errors"], "type": "blade"}, {"name": "layouts.sections.styles (\\resources\\views\\layouts\\sections\\styles.blade.php)", "param_count": 4, "params": ["__env", "app", "menuData", "errors"], "type": "blade"}, {"name": "layouts.sections.scriptsIncludes (\\resources\\views\\layouts\\sections\\scriptsIncludes.blade.php)", "param_count": 4, "params": ["__env", "app", "menuData", "errors"], "type": "blade"}, {"name": "layouts.sections.scripts (\\resources\\views\\layouts\\sections\\scripts.blade.php)", "param_count": 4, "params": ["__env", "app", "menuData", "errors"], "type": "blade"}]}, "route": {"uri": "GET login", "middleware": "web", "controller": "App\\Http\\Controllers\\Auth\\LoginController@showLoginForm", "namespace": "App\\Http\\Controllers", "prefix": "", "where": [], "as": "login", "file": "<a href=\"phpstorm://open?file=C:\\xampp\\htdocs\\SAgilePMT_UTM-1\\vendor\\laravel\\ui\\auth-backend\\AuthenticatesUsers.php&line=19\">\\vendor\\laravel\\ui\\auth-backend\\AuthenticatesUsers.php:19-22</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "statements": []}, "models": {"data": [], "count": 0}, "swiftmailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "y14k494OFB2AtvVlW7RSEPpPORuXDFhwMnY2Ipx9", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/login\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/login", "status_code": "<pre class=sf-dump id=sf-dump-33503497 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-33503497\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-1764006151 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1764006151\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1032521308 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1032521308\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-830956755 data-indent-pad=\"  \"><span class=sf-dump-note>array:17</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"68 characters\">en-US,en-GB;q=0.9,en;q=0.8,ms;q=0.7,zh-GB;q=0.6,zh;q=0.5,zh-CN;q=0.4</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6ImdZWlZ5b21QSEJPdjRvYVduQnpMbFE9PSIsInZhbHVlIjoiWmhtMmNDNmtRWkVTZjV3SkNEV2hoVldiZnh5UWFubUsyS3c3TDh2U0R1dzhhNFI5cUlrN0JXNjhIUWVBa3p2WWhLUVp1L0FBVGxnbUJCOEVsaWtLSGdOUXVFZXNkeE11dzNMN1BTNEt2UzBMalh2UG10cmhzUWJaSXNWK3FyRnQiLCJtYWMiOiJmODRlYzIzNmNmMDI2MWVkYTY0NzBmYzZhNTcxZmI2YzViN2I0YmQ0ZWVlODRmMDcyYjk2OGIzY2M0Njk2MDc1IiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6IkNUdnpJZFNkV05wMURYQ2s5b0dMNGc9PSIsInZhbHVlIjoiMFV2amlyNkZkU0hORHUrMWpxMTlzL1cvWnBLQVpwQkpTQ0pzOHU1WVBBZy9oMlJHejEwYjRYWVl4MUJ3cEUvTDZVeVN5d2hndHVJTnpJellUUHZQNUsvaUFvTnRZc2Fac1JwVTNFT1hrMXNLUDJ5OWI3RzA1SE9BSnNubnAwd28iLCJtYWMiOiJmNTE0ZGY4NTdhM2E0Y2U1YTI5NjMzMzE1YmVmNGU2YTdmYzhmYjBlZmYyMzFhYTBjMmFhMzFlNWY3ZjQyNTU2IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-830956755\", {\"maxDepth\":0})</script>\n", "request_server": "<pre class=sf-dump id=sf-dump-594044889 data-indent-pad=\"  \"><span class=sf-dump-note>array:32</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"38 characters\">C:\\xampp\\htdocs\\SAgilePMT_UTM-1\\public</span>\"\n  \"<span class=sf-dump-key>REMOTE_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>REMOTE_PORT</span>\" => \"<span class=sf-dump-str title=\"5 characters\">56604</span>\"\n  \"<span class=sf-dump-key>SERVER_SOFTWARE</span>\" => \"<span class=sf-dump-str title=\"29 characters\">PHP 8.2.12 Development Server</span>\"\n  \"<span class=sf-dump-key>SERVER_PROTOCOL</span>\" => \"<span class=sf-dump-str title=\"8 characters\">HTTP/1.1</span>\"\n  \"<span class=sf-dump-key>SERVER_NAME</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PORT</span>\" => \"<span class=sf-dump-str title=\"4 characters\">8000</span>\"\n  \"<span class=sf-dump-key>REQUEST_URI</span>\" => \"<span class=sf-dump-str title=\"6 characters\">/login</span>\"\n  \"<span class=sf-dump-key>REQUEST_METHOD</span>\" => \"<span class=sf-dump-str title=\"3 characters\">GET</span>\"\n  \"<span class=sf-dump-key>SCRIPT_NAME</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>SCRIPT_FILENAME</span>\" => \"<span class=sf-dump-str title=\"48 characters\">C:\\xampp\\htdocs\\SAgilePMT_UTM-1\\public\\index.php</span>\"\n  \"<span class=sf-dump-key>PATH_INFO</span>\" => \"<span class=sf-dump-str title=\"6 characters\">/login</span>\"\n  \"<span class=sf-dump-key>PHP_SELF</span>\" => \"<span class=sf-dump-str title=\"16 characters\">/index.php/login</span>\"\n  \"<span class=sf-dump-key>HTTP_HOST</span>\" => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  \"<span class=sf-dump-key>HTTP_CONNECTION</span>\" => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  \"<span class=sf-dump-key>HTTP_CACHE_CONTROL</span>\" => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA</span>\" => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_MOBILE</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_PLATFORM</span>\" => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_UPGRADE_INSECURE_REQUESTS</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>HTTP_USER_AGENT</span>\" => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT</span>\" => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_SITE</span>\" => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_MODE</span>\" => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_USER</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_DEST</span>\" => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  \"<span class=sf-dump-key>HTTP_REFERER</span>\" => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_ENCODING</span>\" => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_LANGUAGE</span>\" => \"<span class=sf-dump-str title=\"68 characters\">en-US,en-GB;q=0.9,en;q=0.8,ms;q=0.7,zh-GB;q=0.6,zh;q=0.5,zh-CN;q=0.4</span>\"\n  \"<span class=sf-dump-key>HTTP_COOKIE</span>\" => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6ImdZWlZ5b21QSEJPdjRvYVduQnpMbFE9PSIsInZhbHVlIjoiWmhtMmNDNmtRWkVTZjV3SkNEV2hoVldiZnh5UWFubUsyS3c3TDh2U0R1dzhhNFI5cUlrN0JXNjhIUWVBa3p2WWhLUVp1L0FBVGxnbUJCOEVsaWtLSGdOUXVFZXNkeE11dzNMN1BTNEt2UzBMalh2UG10cmhzUWJaSXNWK3FyRnQiLCJtYWMiOiJmODRlYzIzNmNmMDI2MWVkYTY0NzBmYzZhNTcxZmI2YzViN2I0YmQ0ZWVlODRmMDcyYjk2OGIzY2M0Njk2MDc1IiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6IkNUdnpJZFNkV05wMURYQ2s5b0dMNGc9PSIsInZhbHVlIjoiMFV2amlyNkZkU0hORHUrMWpxMTlzL1cvWnBLQVpwQkpTQ0pzOHU1WVBBZy9oMlJHejEwYjRYWVl4MUJ3cEUvTDZVeVN5d2hndHVJTnpJellUUHZQNUsvaUFvTnRZc2Fac1JwVTNFT1hrMXNLUDJ5OWI3RzA1SE9BSnNubnAwd28iLCJtYWMiOiJmNTE0ZGY4NTdhM2E0Y2U1YTI5NjMzMzE1YmVmNGU2YTdmYzhmYjBlZmYyMzFhYTBjMmFhMzFlNWY3ZjQyNTU2IiwidGFnIjoiIn0%3D</span>\"\n  \"<span class=sf-dump-key>REQUEST_TIME_FLOAT</span>\" => <span class=sf-dump-num>1750848402.7498</span>\n  \"<span class=sf-dump-key>REQUEST_TIME</span>\" => <span class=sf-dump-num>1750848402</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-594044889\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-625978282 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">y14k494OFB2AtvVlW7RSEPpPORuXDFhwMnY2Ipx9</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">b28fWvQao3fEJvt8R6gldAa336X5X6bvmOLGcQbm</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-625978282\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1752028929 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 25 Jun 2025 10:46:43 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6IlpZRU9YdFUrQWlqM3pnRGZ4ZS9WL2c9PSIsInZhbHVlIjoiclE4SWx3NUdBQkJJQlZCMGFtSHdOYUZCMFpBM3loZ2hicUxPRytCMC9Wc21ReTgzc2dlcVk1MTFhck0rYzlmRTFCaFRDRHVzOGpaa1JsaldVV01VMk0wcFgyTUtSZEZSMUZMUVpNQlhIUmZ1Ynd5eVo0Wm5Mdmd4YlpoL1pIUkEiLCJtYWMiOiJmNGUyODEwMmZhNDI5Yjk2ZmFmZWUwYjlmMTY0OWRjM2EyYTFmMjdiZGNlMjA4OGI3ZGM1MjliNjg4ODljYTJhIiwidGFnIjoiIn0%3D; expires=Wed, 25-Jun-2025 12:46:43 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"443 characters\">laravel_session=eyJpdiI6IlkvSzBZMmRLcVBSR2xrNmpsUzAyeVE9PSIsInZhbHVlIjoiM29GSWJvS1hWa0k5N1g1UStaeU9RRjVIc09JT29zYkx0bUh0VFZRREpSZHgzMDRuVythSWVEOTh3Y3dhdUlNYXovVVUzdjZKaXJvRjRaelQ2TVc4eS9DemFvc0diRzVKclNJdkJrM3c1b2MvZCtiVVlSRkNoUUdPLzVNSTNVamsiLCJtYWMiOiIwODQzZGNkMDVhMjI5YmUyNzdmN2M1MTNhM2Y5YzFlMzY1NGM5ODAwNzUwYmEwMmMxZjcwYTRkZGNmYzJhOWMzIiwidGFnIjoiIn0%3D; expires=Wed, 25-Jun-2025 12:46:43 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6IlpZRU9YdFUrQWlqM3pnRGZ4ZS9WL2c9PSIsInZhbHVlIjoiclE4SWx3NUdBQkJJQlZCMGFtSHdOYUZCMFpBM3loZ2hicUxPRytCMC9Wc21ReTgzc2dlcVk1MTFhck0rYzlmRTFCaFRDRHVzOGpaa1JsaldVV01VMk0wcFgyTUtSZEZSMUZMUVpNQlhIUmZ1Ynd5eVo0Wm5Mdmd4YlpoL1pIUkEiLCJtYWMiOiJmNGUyODEwMmZhNDI5Yjk2ZmFmZWUwYjlmMTY0OWRjM2EyYTFmMjdiZGNlMjA4OGI3ZGM1MjliNjg4ODljYTJhIiwidGFnIjoiIn0%3D; expires=Wed, 25-Jun-2025 12:46:43 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"415 characters\">laravel_session=eyJpdiI6IlkvSzBZMmRLcVBSR2xrNmpsUzAyeVE9PSIsInZhbHVlIjoiM29GSWJvS1hWa0k5N1g1UStaeU9RRjVIc09JT29zYkx0bUh0VFZRREpSZHgzMDRuVythSWVEOTh3Y3dhdUlNYXovVVUzdjZKaXJvRjRaelQ2TVc4eS9DemFvc0diRzVKclNJdkJrM3c1b2MvZCtiVVlSRkNoUUdPLzVNSTNVamsiLCJtYWMiOiIwODQzZGNkMDVhMjI5YmUyNzdmN2M1MTNhM2Y5YzFlMzY1NGM5ODAwNzUwYmEwMmMxZjcwYTRkZGNmYzJhOWMzIiwidGFnIjoiIn0%3D; expires=Wed, 25-Jun-2025 12:46:43 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1752028929\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-599335193 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">y14k494OFB2AtvVlW7RSEPpPORuXDFhwMnY2Ipx9</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"27 characters\">http://127.0.0.1:8000/login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-599335193\", {\"maxDepth\":0})</script>\n"}}