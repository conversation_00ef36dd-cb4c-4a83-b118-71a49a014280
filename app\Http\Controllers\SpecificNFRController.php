<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\GeneralNFR;
use App\SpecificNFR;
use App\Project;
use App\TeamMapping;
use App\UserStoryGeneralNfr;
use App\Sprint;
use Illuminate\Support\Facades\Auth;
use Illuminate\Validation\Rule;
use Illuminate\Support\Facades\DB;

class SpecificNFRController extends Controller
{
    /**
     * Display a listing of Specific NFRs.
     */

    public function viewSpecific($general_nfr_id, $specific_nfr_id)
    {
    $user = Auth::user();

    $isAdmin = $user->user_type == 1;
    $isProjectManager = TeamMapping::where('username', $user->username)
    ->get()
    ->contains(function ($mapping) {
        return $mapping->isProjectManager();
    });

    if (!($isAdmin || $isProjectManager)) {
        return redirect()->route('home')->with('error', 'Only Admins and Project Managers can access the NFR Management.');
    }
    // Get the user's projects
    if ($isAdmin) {
            $pro = Project::all();
    } else {
        $pro = TeamMapping::getProjectsByUser($user->username);
    }

    // Fetch the specific NFR using the provided nfr_id
    $specificNFR = SpecificNFR::findSpecificNFRById($specific_nfr_id);

    if (!$specificNFR || $specificNFR->general_nfr_id != $general_nfr_id) {
        return redirect()->route('nfr.show')->with('error', 'Specific NFR not found or does not match the General NFR.');
    }
    //$projectIds = Project::getProjectIDs($pro); //for filtering
    // Get the project filter and sprint filter from the request
    $projectFilter = request('project_filter');
    $sprintFilter = request('sprint_filter');
    $sprints = Sprint::getFilteredSprints($isAdmin, $projectFilter, $pro);
    // Fetch linked user stories with the option to filter by project and sprint
    $linkedUserStories = UserStoryGeneralNfr::getLinkedUserStoriesByNFR(
    $specific_nfr_id,
    'specific',
    $projectFilter,
    $sprintFilter,
    //$projectIds
    );
    return view('nfr.viewSpecific', compact('specificNFR', 'pro','user', 'linkedUserStories', 'sprints'))
        ->with('title', 'Specific NFR Details - ' . $specificNFR->specific_nfr)
        ->with('pros', $pro);
    }

    public function createSpecific($general_nfr_id)
    {
        $user = Auth::user();
        $generalNFR = GeneralNFR::findGeneralNFRById($general_nfr_id);
        if (!$user->user_type == 1) {
         return redirect()->route('home')->with('error', 'Only Admins can create NFR.');
        }else {
            return view('nfr.createSpecific', compact('general_nfr_id', 'user'))->with('title', 'Create Specific NFR - ' . $generalNFR->general_nfr);
        }
    }
    
    public function storeSpecific(Request $request, $general_nfr_id)
    {
    // Validate the incoming data, ensuring specific_nfr is unique within the general_nfr_id scope
    $request->validate([
        'specific_nfr' => [
            'required', 
            'string', 
            'max:255',
            'regex:/^[a-zA-Z\s]+$/', // Alphabetic characters only
            Rule::unique('nfr')->where(function ($query) use ($general_nfr_id) {
                return $query->where('general_nfr_id', $general_nfr_id);
            }),
        ],
        'specific_nfr_desc' => 'nullable|string|max:255',
    ], [
        'specific_nfr.unique' => 'This specific NFR already exists for the selected General NFR.',
        'specific_nfr.required' => 'The specific NFR field is required.',
        'specific_nfr.regex' => 'The specific NFR field must contain only alphabetic characters.',
    ]);

    // Create the Specific NFR associated with the General NFR ID
    SpecificNFR::create([
        'general_nfr_id' => $general_nfr_id,
        'specific_nfr' => $request->specific_nfr,
        'specific_nfr_desc' => $request->specific_nfr_desc,
        'created_by' => Auth::user()->id,
        'created_at' => now(),
        'updated_at' => now(),
    ]);

    // Redirect to the show page with a success message
    return redirect()->route('nfr.show', ['general_nfr_id' => $general_nfr_id])
        ->with('success', 'Specific NFR added successfully!');
    }

    /**
     * Display the specified NFR.
     */
    public function show($general_nfr_id)
    {
        $user = Auth::user();
        
        // Retrieve user's role
        $roles = TeamMapping::where('username', $user->username)->pluck('role_name')->toArray();
        $isAdmin = $user->user_type == 1;
        $isProjectManager = TeamMapping::where('username', $user->username)
            ->get()
            ->contains(function ($mapping) {
                return $mapping->isProjectManager();
            });

        if (!($isAdmin || $isProjectManager)) {
            return redirect()->route('home')->with('error', 'Only Admins and Project Managers can access the NFR Management.');
        }
        // Get the NFRs that have the same general_nfr_id as $id
        $generalNFR = GeneralNFR::findGeneralNFRById($general_nfr_id);
        $nfrs = SpecificNFR::getByGeneralNfrId($general_nfr_id);

        return view('nfr.show', compact('nfrs','user','roles'))
            ->with('title', 'Specific NFR List - ' . $generalNFR->general_nfr)
            ->with('general_nfr_id', $general_nfr_id); // Pass the ID separately
    }

    public function editSpecific($general_nfr_id, $specific_nfr_id)
    {
    
    $user = Auth::user();
        $isAdmin = $user->user_type == 1;
        if (!$isAdmin) {
            return redirect()->route('home')->with('error', 'Only Admins can edit NFR.');
        }
    
    // Find the specific NFR by ID
    $specificNFR = SpecificNFR::findSpecificNFRById($specific_nfr_id);
    $generalNFR = GeneralNFR::findGeneralNFRById($general_nfr_id);

    return view('nfr.editSpecific', compact('specificNFR', 'generalNFR','user'))
        ->with('title', 'Edit Specific NFR - ' . $specificNFR->specific_nfr);

    }

    public function updateSpecific(Request $request, $general_nfr_id, $specific_nfr_id)
    {
    $request->validate([
        'specific_nfr' => [
            'required', 
            'string', 
            'max:255',
            'regex:/^[a-zA-Z\s]+$/', // Alphabetic characters only
            Rule::unique('nfr')->where(function ($query) use ($general_nfr_id, $specific_nfr_id) {
                return $query->where('general_nfr_id', $general_nfr_id)
                             ->where('nfr_id', '!=', $specific_nfr_id);
            }),
        ],
        'specific_nfr_desc' => 'nullable|string|max:255',
    ], [
        'specific_nfr.unique' => 'This specific NFR already exists for the selected General NFR.',
        'specific_nfr.required' => 'The specific NFR field is required.',
        'specific_nfr.regex' => 'The specific NFR field must contain only alphabetic characters.',
    ]);

    $specificNFR = SpecificNFR::findSpecificNFRById($specific_nfr_id);
    $specificNFR->specific_nfr = $request->specific_nfr;
    $specificNFR->specific_nfr_desc = $request->specific_nfr_desc;
    $specificNFR->created_at = now();
    $specificNFR->updated_at = now();
    $specificNFR->created_by = Auth::user()->id;
    $specificNFR->save();

    return redirect()->route('nfr.show', ['general_nfr_id' => $general_nfr_id])
        ->with('success', 'Specific NFR updated successfully.');
    }

    public function destroySpecific($general_nfr_id, $specific_nfr_id)
    {
    $specificNFR = SpecificNFR::findSpecificNFRById($specific_nfr_id);

    if ($specificNFR) {
        $specificNFR->delete();
    }

    return redirect()->route('nfr.show', ['general_nfr_id' => $general_nfr_id])
        ->with('success', 'Specific NFR deleted successfully.');
    }
}