@extends('layouts.app2')
@include('inc.success')
@include('inc.style')

@section('navbar')
  @include('inc.navbar')
@endsection

@section('content')
@include('inc.title')

<br>
@if ($errors->any())
    <div class="alert alert-danger">
        <ul>
            @foreach ($errors->all() as $error)
                <li>{{ $error }}</li>
            @endforeach
        </ul>
    </div>
@endif
<script>
    function confirmAction(event) {
        event.preventDefault();
        if (confirm("Are you sure you want to proceed?")) {
            event.target.closest('form').submit();
        }
    }
</script>

@if ($user->user_type == 1) 
<form action="{{ route('nfr.store') }}" method="post" enctype="multipart/form-data" onsubmit="confirmAction(event)">
    @csrf
    <br><br>
        <label for="general_nfr">General Requirement:</label>
        <input type="text" name="general_nfr"  class="form-control" required>
        <br><br>
        <label for="general_nfr_desc">Description:</label>
        <input type="text" name="general_nfr_desc"  class="form-control">
        <br><br>

    <button type="submit" class="btn btn-success">Submit</button>
    <a href="{{ route('nfr.index') }}" class="btn btn-secondary">Cancel</a>
</form>

@else
<div>Only System Admin can access this page.</div>
@endif

@endsection
