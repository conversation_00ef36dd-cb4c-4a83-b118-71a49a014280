@extends('layouts.app2')

@include('inc.style')

@include('inc.success')
@include('inc.dashboard')
@include('inc.navbar')

@section('content')
    @include('inc.title',['title' => 'Team'])
    <br>
    <table>
        @if (session('success'))
            <div class="alert alert-success">
                {{ session('success') }}
            </div>
        @endif

        <tr>
            <th>Team Name</th>
            <th>View Members</th>
            <th>Delete</th>
        </tr>

        @forelse($teams as $team)
            <tr>
                <td>{{ $team->team_name }}</td>
                <td>
                    <a href="{{ action('TeamMappingController@index', $team['team_name']) }}" class="btn btn-primary">View</a>
                </td>
                <td>
                    <a href="{{ route('teams.destroy', $team) }}" class="btn btn-danger" onclick="return confirm('Are you sure you want to delete this team?');">Delete</a>
                </td>
            </tr>
        @empty
            <tr>
                <td colspan="4">No teams found!</td>
            </tr>
        @endforelse
    </table>
    <br><br><br>

    <a href="{{ route('teams.create') }}" class="btn btn-success">Add Team</a>
@endsection