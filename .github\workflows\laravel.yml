name: <PERSON><PERSON> Test

on:
  push:
    branches:
      - main
  pull_request:
    branches:
      - main

jobs:
  test:
    runs-on: windows-latest

    steps:
      - name: Checkout code
        uses: actions/checkout@v2

      - name: Set up PHP
        uses: shivammathur/setup-php@v2
        with:
          php-version: '8.0' # Adjust PHP version as per your application's requirements

      - name: Install dependencies
        run: composer install --no-ansi --no-interaction --no-progress --prefer-dist

      - name: Prepare environment file
        run: copy .env.example .env

      - name: Generate application key
        run: php artisan key:generate

      - name: Run tests
        run: vendor\bin\phpunit

      - name: Check for coding standards
        run: vendor\bin\phpcs

      # Additional steps can be added here based on your specific requirements

