<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;


class CheckNFRAccess
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure(\Illuminate\Http\Request): (\Illuminate\Http\Response|\Illuminate\Http\RedirectResponse)  $next
     * @return \Illuminate\Http\Response|\Illuminate\Http\RedirectResponse
     */
    public function handle(Request $request, Closure $next)
    {
        $user = Auth::user();

        // Check if the user is a project manager and has user_type == 1
        $roleId = DB::table('user_role')->where('user_id', $user->id)->value('role_id');
        $role = DB::table('roles')->where('role_id', $roleId)->value('role_name');

        if ($role !== 'project manager' || $user->user_type != 1) {
            // Redirect if not authorized with an error message
            return redirect()->route('home')->with('error', 'You do not have access to this page.');
        }
    }
}
