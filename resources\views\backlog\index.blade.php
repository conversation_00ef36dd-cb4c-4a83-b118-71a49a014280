<!--Main Backlog Page-->
@extends('layouts.app2')
@include('inc.success')
@include('inc.style')

@include('inc.dashboard')

@include('inc.navbar')

@section('content')
@include('inc.title')
<br><br>

    @csrf
    <table id=backlog>
        <tr>
            <th>Backlog</th>
            <th>Performance</th>
            <th>Security</th>
            <th>Tasks</th>
            <th>Edit</th> 
            <th>Delete</th>
        </tr>

      @forelse($userstories as $userstory)
        <tr> 
            <th>
              {{$userstory->user_story}}
            </th>
           
            <!--If the perfeature_id and secfeature_id does not contain anything, it will store as the string 'null'
                This condition here displays 'None selected if the features are empty'
                The false condition is displaying the features if it is not empty-->            
            <th>
              {{ $userstory->perfeature_id == 'null' ? 'None selected' : $userstory->perfeature_id }}
            </th>

            <th>
              {{ $userstory->secfeature_id == 'null' ? 'None selected' : $userstory->secfeature_id }}
            </th>

            <th>
              <a href="{{action('TaskController@index', $userstory['u_id'])}}" class="btn btn-primary">View</a>
            </th>
            
            <th>
              <a href="{{route('backlog.edit', [$userstory->u_id])}}" class="btn btn-secondary">Edit</a>
            </th>

            <th>
              <a href="{{route('backlog.destroy', $userstory)}}" class="btn btn-danger" onclick="return confirm('Are you sure you want to delete this Backlog?');">Delete</a>
            </th>
        </tr>

      @empty
      <tr>
        <td colspan="5">No backlog added yet</td>
      </tr>

        @endforelse
      </table>

        <br><br><br>
        <a href="{{ route('backlog.create', $project->id) }}" class="btn btn-success">Create Backlog</a>
       <br><br>
      
@endsection