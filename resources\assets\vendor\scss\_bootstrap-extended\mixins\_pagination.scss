// Pagination
// *******************************************************************************

// Basic Pagination
@mixin template-pagination-variant($parent, $background, $color: null) {
  #{$parent} .page-item.active .page-link,
  #{$parent}.pagination li.active > a:not(.page-link) {
    &,
    &:hover,
    &:focus {
      border-color: $background;
      background-color: $background;
      // @include gradient-bg($pagination-active-bg);
      color: if($color, $color, color-contrast($background));
    }

    &.waves-effect {
      .waves-ripple {
        background: radial-gradient(
          rgba($white, 0.2) 0,
          rgba($white, 0.3) 40%,
          rgba($white, 0.4) 50%,
          rgba($white, 0.5) 60%,
          rgba($white, 0) 70%
        );
      }
    }
  }
}

// Pagination Outline Variant
@mixin template-pagination-outline-variant($parent, $background, $color: null) {
  #{$parent} .page-item.active .page-link,
  #{$parent}.pagination li.active > a:not(.page-link) {
    &,
    &:hover,
    &:focus {
      border-color: rgba-to-hex(rgba($background, 0.5), $rgba-to-hex-bg);
      color: $background;
      background-color: rgba-to-hex(rgba($background, 0.08), $rgba-to-hex-bg);
    }
    &.waves-effect {
      .waves-ripple {
        background: radial-gradient(
          rgba($background, 0.2) 0,
          rgba($background, 0.3) 40%,
          rgba($background, 0.4) 50%,
          rgba($background, 0.5) 60%,
          rgba($black, 0) 70%
        );
      }
    }
  }
}

@mixin template-pagination-theme($background, $color: null) {
  @include template-pagination-variant('', $background, $color);
}
