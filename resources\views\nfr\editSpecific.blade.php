@extends('layouts.app2')
@include('inc.success')
@include('inc.style')

@section('navbar')
    @include('inc.navbar')
@endsection

@section('content')
@include('inc.title')
<br>
@if ($errors->any())
    <div class="alert alert-danger">
        <ul>
            @foreach ($errors->all() as $error)
                <li>{{ $error }}</li>
            @endforeach
        </ul>
    </div>
@endif
<script>
    function confirmAction(event) {
        event.preventDefault();
        if (confirm("Are you sure you want to proceed?")) {
            event.target.closest('form').submit();
        }
    }
</script>
@if ($user->user_type == 1 && $specificNFR->created_by == $user->id) 
    <form action="{{ route('nfr.updateSpecific', ['general_nfr_id' => $generalNFR->general_nfr_id, 'nfr_id' => $specificNFR->nfr_id]) }}" method="POST" onsubmit="confirmAction(event)">
        @csrf
        @method('PUT')

        <div class="form-group">
            <label for="specific_nfr">Specific NFR</label>
            <input type="text" name="specific_nfr" class="form-control" value="{{ old('specific_nfr', $specificNFR->specific_nfr) }}" required>
        </div>

        <div class="form-group">
            <label for="specific_nfr_desc">Description</label>
            <input type="text" name="specific_nfr_desc" class="form-control" value="{{ old('specific_nfr_desc', $specificNFR->specific_nfr_desc) }}">
        </div>
        <br><br>
        <button type="submit" class="btn btn-success">Update</button>
        <a href="{{ route('nfr.show', ['general_nfr_id' => $generalNFR->general_nfr_id]) }}" class="btn btn-secondary">Cancel</a>
    </form>
@else
    <div>You do not have access to this page.</div>
@endif
@endsection
