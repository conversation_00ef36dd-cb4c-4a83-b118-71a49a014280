@extends('layouts.app2')
@include('inc.style')
@include('inc.navbar')

@section('content')
@include('inc.title')

<br><br>
<form action="{{ route('userstory.store') }}" method="post" enctype="multipart/form-data">
    @csrf

    <input type="hidden" name="sprint_id" value="{{ $sprint_id }}">

    <div>
        <label for="role">Role : As a</label>
        <select name="role" id="role" class="form-control" onchange="updateUserStory()">
            <option value="" selected disabled>Select</option>
            @foreach($roles as $role)
                <option value="{{ $role }}"> {{ $role }}</option>
            @endforeach
        </select>
        <div class="error"><font color="red" size="2">{{ $errors->first('role') }}</font></div>
    </div>
    <br>

    Means : I am able to <input type="text" name="means" id="means" style="margin-left:2.6em" onchange="updateUserStory()">
    <div class="error"><font color="red" size="2">{{ $errors->first('means') }}</font></div>
    <br>

    Ends : so that I can <input type="text" name="ends" id="ends" style="margin-left:2.6em" onchange="updateUserStory()"> (optional)
    <br><br>

    User Story : <input type="text" name="user_story" id="user_story" style="margin-left:2.5em; width: 1000px; height:50px">
    <div class="error"><font color="red" size="2">{{ $errors->first('user_story') }}</font></div>
    <br>

    <div>
        <label for="status_id">Status :</label>
        <select name="status_id" id="status_id" class="form-control">
            <option value="" selected disabled>Select</option>
            @foreach($statuses as $status)
                <option value="{{ $status->id }}">{{ $status->title }}</option>
            @endforeach
        </select>
        <div class="error"><font color="red" size="2">{{ $errors->first('status_id') }}</font></div>
    </div>
    <br><br>

    Assigned to:
    <select name="user_names[]" multiple>
        @foreach($teamlist as $teammember)
            <option value="{{ $teammember['username'] }}">{{ $teammember['username'] }} (Team: {{ $teammember['team_name'] }})</option>
        @endforeach
    </select>
    <br><br>


    <h3>General NFRs and Their Specific Requirements</h3>
    @foreach($generalNFRIds as $index => $generalNFR)
        <div class="card mb-3">
            <div class="card-header"><h3>{{ $generalNFRNames[$index] }}</h3></div>
            <div class="card-body">
                @if(isset($specificNFRs[$generalNFR]) && isset($specificNFRIds[$generalNFR]))
                    @foreach($specificNFRs[$generalNFR] as $specificIndex => $specificNFR)
                        <label class="checkbox-inline">
                            <!-- Use the specific NFR ID as the value -->
                            <input type="checkbox" name="selected_nfrs[{{ $generalNFR }}][]" value="{{ $specificNFRIds[$generalNFR][$specificIndex] }}">
                            {{ $specificNFR }} <!-- Display the name -->
                        </label><br>
                    @endforeach
                @else
                    <p>No specific requirements available.</p>
                @endif
            </div>
        </div>
    @endforeach


    <br><br>
    <input type="submit" value="Add Story">
</form>
<script>
    function updateUserStory() {
        var role = document.getElementById("role").value;
        var means = document.getElementById("means").value;
        var ends = document.getElementById("ends").value;
        var userStory = "As a " + role + ", I am able to " + means;
        if (ends.trim() !== "") {
            userStory += " so that I can " + ends;
        }
        document.getElementById("user_story").value = userStory;
    }
</script>

@endsection
