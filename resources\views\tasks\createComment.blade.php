@extends('layouts.app2')
@include('inc.success')
@include('inc.style')
@include('inc.navbar')
@section('content')
@include('inc.title')
<br><br>
@if (!$isSprintOverdue && !$isTaskOverdue)
<div class="container">
    <h3>Create Comment</h3>
    <form action="{{ route('tasks.storeComment', ['task_id' => $task->id]) }}" method="POST">
        @if ($errors->any())
            <div class="alert alert-danger">
                <ul>
                    @foreach ($errors->all() as $error)
                        <li>{{ $error }}</li>
                    @endforeach
                </ul>
            </div>
        @endif
        @csrf
        @method('PUT')

        <div class="form-group">
            <label for="comment">Comment</label>
            <textarea id="comment" name="comment" class="form-control" rows="4" required></textarea>
        </div>

        <br>
        <button type="submit" class="btn btn-primary">Create Comment</button>
        <a href="{{ route('tasks.viewCommentList', ['task_id' => $task->id, 'sprint_id' => $task->sprint_id]) }}" class="btn btn-secondary">Cancel</a>
    </form>
</div>
@else
    <span class="text-muted">Sprint or Task Overdue</span>
@endif

@endsection