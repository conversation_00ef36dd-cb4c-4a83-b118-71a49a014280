<!-- Main Task Page -->
@extends('layouts.app2')
@include('inc.style')
@include('inc.success')
@include('inc.dashboard')
@include('inc.navbar')

@section('content')
@include('inc.title')
<br>
    <table>
        <tr>
            <th>Task</th>
            <th>Description</th>
            <th>Assigned To</th>
            <th>Status</th>   
            <th>Comment</th>
            <th>Edit</th> <!--Not Done-->
            <th>Delete</th>
        </tr>

      @forelse($tasks as $task)
        <tr> 
            <th>
              {{$task->title}}
            </th>
            <th>
              {{ $task->description }}
            </th>
            <th>
              @php
                  // Ensure user_names is sanitized by trimming extra quotes and then decoding
                  $userNames = json_decode(trim($task->user_names, '"'), true);
              @endphp

              @if (is_array($userNames))
                  @foreach($userNames as $user_show)
                     - {{ $user_show }}<br>
                  @endforeach
              @else
                  <p>No valid users found.</p>
              @endif
            </th>
            <th>

              <?php
                $status = $statuses->firstWhere('id', $task->status_id);
              ?>
  
              {{ $status->title }}
            </th>
            <th>
              <a href="{{ route('tasks.viewCommentList', [$task->id, $task->sprint_id]) }}" class="btn btn-secondary">Comment</a>
            </th>
            <th>
              <a href="{{route('tasks.edit', [$task->id])}}" class="btn btn-secondary">Edit</a>
            </th>
            <th>
              <a href="{{route('tasks.destroy', $task)}}" class="btn btn-danger" onclick="return confirm('Are you sure you want to delete this task?');">Delete</a>
            </th>

        </tr>
        @empty
        <tr>
          <td colspan="6">No task added yet</td>
        </tr>
        @endforelse

          
      </table>

      <br><br><br>

      <a href="{{route('tasks.create', $userstory_id)}}" class="btn btn-success">Add Task</a>

      <a href="{{route('tasks.calendarTask', $userstory_id)}}" class="btn btn-success">Task Calendar</a>
      
      <br><br>
      
@endsection