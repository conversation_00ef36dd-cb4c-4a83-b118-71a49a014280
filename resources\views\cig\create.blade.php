@extends('layouts.app2')
@include('inc.success')
@include('inc.style')

@section('navbar')
    @include('inc.navbar')
@endsection

@section('content')
@include('inc.title')
<link rel="stylesheet" href="{{ asset('css/app2.css') }}">

<br>

<!-- Sprint Filter Form -->
<form method="GET" action="{{ route('cig.create', ['proj_id' => $proj_id]) }}">
    <div class="form-group row">
        <label for="sprint" class="col-sm-2 col-form-label">Sprint:</label>
        <div class="col-sm-10">
            <select 
                name="filter_sprint_id"
                id="sprint" 
                class="form-control"
                data-project-id="{{ $proj_id }}"
                onchange="this.form.submit()"
            >
                <option value="" {{ $selectedSprintId == '' ? 'selected' : '' }}>All Sprints</option>
                @foreach($sprints as $sprint)
                    <option 
                        value="{{ $sprint->sprint_id }}" 
                        {{ $selectedSprintId == $sprint->sprint_id ? 'selected' : '' }}
                    >
                        {{ $sprint->sprint_name }}
                    </option>
                @endforeach
            </select>
        </div>
    </div>
</form>

@if (count($userStories) == 0)
    <div class="alert alert-warning mt-3">No user stories available for the selected sprint.</div>
@else
    <!-- Radar Chart -->
    <div id="chart-container" class="mt-4">
        <canvas id="nfrRadarChart"></canvas>
    </div>
    <form method="GET" action="{{ route('generate.xlsx.report', ['proj_id' => $proj_id]) }}">
    <input type="hidden" name="filter_sprint_id" value="{{ $selectedSprintId }}">
    <button type="submit" class="btn btn-primary" 
            onclick="return confirm('Are you sure you want to download the report for the selected sprint?')">
        Download Excel Report
    </button>
    </form>

    <!-- User Story Table -->
    <div id="user-story-nfr-table" class="mt-3">
        <table class="table table-bordered table-hover">
            <thead>
                <tr>
                    <th>User Story</th>
                    <th>Linked General NFR</th>
                    <th>Linked Specific NFR</th>
                </tr>
            </thead>
            <tbody>
                @foreach($userStories as $userStory)
                    @foreach($userStory['general_nfrs'] as $nfrIndex => $generalNfr)
                        <tr>
                            @if ($nfrIndex === 0)
                                <td rowspan="{{ count($userStory['general_nfrs']) }}">{{ $userStory['user_story'] }}</td>
                            @endif
                            <td>{{ $generalNfr }}</td>
                            <td>{{ $userStory['specific_nfrs'][$nfrIndex] ?? 'N/A' }}</td>
                        </tr>
                    @endforeach
                @endforeach
            </tbody>
        </table>
    </div>

    <!-- Pagination Links -->
    <div class="pagination-container mt-3">
        {{ $paginatedUserStories->appends(['filter_sprint_id' => $selectedSprintId])->links('pagination::bootstrap-4') }}
    </div>
@endif
<script src="https://cdnjs.cloudflare.com/ajax/libs/Chart.js/3.7.0/chart.min.js"></script>
<script src="{{ asset('js/app2.js') }}"></script>
@endsection
