@extends('layouts.app2')
@include('inc.success')
@include('inc.style')

@section('navbar')
  @include('inc.navbar')
@endsection

@section('content')
@include('inc.title')
<br>

<table>
    <tr>
        <th>Diagram Name</th>
        <th>Project</th>
        <th>Sprint</th>
        <th>Objects Status</th>
        <th>Created Date</th>
        <th>Actions</th>
    </tr>

    @forelse($sequenceDiagrams as $diagram)
    <tr>
        <td>{{ $diagram->name }}</td>
        <td>
            @if($diagram->project)
                {{ $diagram->project->proj_name }}
            @else
                No Project
            @endif
        </td>
        <td>
            @if($diagram->sprint)
                {{ $diagram->sprint->sprint_name }}
            @else
                No Sprint
            @endif
        </td>
        <td>
            @if($diagram->objects_identified)
                <span style="color: green; font-weight: bold;">✓ Identified</span>
            @else
                <span style="color: orange; font-weight: bold;">⏳ Pending</span>
            @endif
        </td>
        <td>{{ $diagram->created_at->format('M d, Y') }}</td>
        <td>
            <a href="{{ route('sequence-diagram.show', $diagram->id) }}" class="btn btn-secondary">View</a>
            @if(!$diagram->objects_identified)
                <a href="{{ route('sequence-diagram.show', $diagram->id) }}" class="btn btn-success">Identify Objects</a>
            @endif
        </td>
    </tr>
    @empty
    <tr>
        <td colspan="6">No sequence diagrams found</td>
    </tr>
    @endforelse
</table>

<br><br>

<a href="#" class="btn btn-success">Create New Diagram</a>

<br><br>

@endsection
