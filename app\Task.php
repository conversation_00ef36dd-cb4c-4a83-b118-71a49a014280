<?php

namespace App;

use Illuminate\Database\Eloquent\Model;

class Task extends Model
{
    protected $fillable = ['title', 'description','user_names', 'order', 'start_date','end_date', 'proj_id', 'newTask_update'];

    public $primaryKey = 'id';

    public $foreignKey = ['userstory_id','sprint_id','status_id'];
    
    public $timestamps = true;

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function status()
    {
        return $this->belongsTo(Status::class);
    }
    public function userStories()
    {
        return $this->belongsTo(UserStory::class, 'userstory_id');
    }

    public function comments()
    {
        return $this->hasMany(TaskComment::class);
    }

     public function sprint()
    {
        return $this->belongsTo(Sprint::class, 'sprint_id');
    }

    public static function getTaskById($taskId)
    {
        return self::find($taskId);
    }

    public static function getTasksByProjectAndSprintID($proj_id, $sprint_id)
    {
        return self::where('proj_id', $proj_id)
            ->where('sprint_id', $sprint_id)
            ->get();
    }
}
