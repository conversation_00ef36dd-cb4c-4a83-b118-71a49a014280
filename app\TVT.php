<?php

namespace App;

use Illuminate\Database\Eloquent\Model;

class TVT extends Model
{
    protected $table = 'tvt';
    protected $primaryKey = 'tvt_id';
    protected $fillable = [
        'sprint', // changed from 'iteration' to 'sprint'
        'backlog_panel',
        'user_story_panel',
        'fr',
        'nfr',
        'qaw',
        'project_id'
    ];

    public function project()
    {
        return $this->belongsTo(Project::class, 'project_id');
    }

    /**
     * Define the relationship between TVT and UserStory (assuming a many-to-many relationship)
     */
    public function userStories()
    {
        return $this->belongsToMany(UserStory::class, 'tvt_user_story', 'tvt_id', 'user_story_id');
    }

    /**
     * Define the relationship between TVT and Sprint
     */
    public function sprints()
    {
        return $this->belongsToMany(Sprint::class, 'tvt_sprint', 'tvt_id', 'sprint_id');
    }
}
