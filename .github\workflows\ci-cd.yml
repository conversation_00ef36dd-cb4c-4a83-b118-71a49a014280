name: <PERSON><PERSON>I/CD

on: [push, pull_request]

jobs:
  laravel-tests:

    runs-on: ubuntu-latest

    services:
      mysql:
        image: mysql:5.7
        env:
          MYSQL_ROOT_PASSWORD: password
          MYSQL_DATABASE: test
        ports:
          - 3306:3306
        options: --health-cmd="mysqladmin ping --silent" --health-interval=10s --health-timeout=5s --health-retries=3

    steps:
    - name: Checkout code
      uses: actions/checkout@v2

    - name: Set up PHP
      uses: shivammathur/setup-php@v2
      with:
        php-version: '7.4'
        extensions: mbstring, pdo, pdo_mysql, pcntl
        ini-values: post_max_size=256M, max_execution_time=300
        coverage: none

    - name: Install Composer dependencies
      run: composer install --prefer-dist --no-progress --no-suggest --no-interaction

    - name: Copy .env
      run: cp .env.example .env

    - name: Generate application key
      run: php artisan key:generate

    - name: Run migrations
      run: php artisan migrate --force

    - name: Run tests
      run: php artisan test

    - name: Deploy to Production
      if: github.ref == 'refs/heads/main'
      run: |
        echo "Deploying to production server..."
        ssh user@your-server "cd /path/to/your/app && git pull origin main && composer install --no-dev && php artisan migrate --force && php artisan config:cache && php artisan route:cache"
