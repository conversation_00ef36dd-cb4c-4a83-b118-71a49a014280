/* ---- RESET/BASIC STYLING ---- */

* {
    padding: 0;
    margin: 0;
    box-sizing: border-box;
    font-family: sans-serif;
    -ms-overflow-style: none;
    /* IE and Edge */
    scrollbar-width: none;
    /* Firefox */
}

*::-webkit-scrollbar {
    display: none;
}

.board {
    width: 100%;
    height: 100vh;
    overflow: scroll;
    /* background-image: url(https://images.unsplash.com/photo-1519681393784-d120267933ba?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=1470&q=80); */
    background-position: center;
    background-size: cover;
}

#add-lane-btn {
    margin: 10px;
    padding: 8px 12px;
    background-color: #4caf50;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 16px;
    margin-left: 35px;
    margin-top: 20px;
}

#add-lane-btn:hover {
    background-color: #45a049;
}


/* ---- BOARD ---- */

.lanes {
    display: flex;
    align-items: flex-start;
    justify-content: start;
    gap: 16px;
    padding: 24px 32px;
    overflow: scroll;
    height: 100%;
}

.heading {
    font-size: 22px;
    font-weight: bold;
    margin-bottom: 8px;
}

.swim-lane {
    display: flex;
    flex-direction: column;
    gap: 12px;
    background: #f4f4f4;
    box-shadow: 0px 5px 15px rgba(0, 0, 0, 0.25);
    padding: 12px;
    border-radius: 4px;
    width: 225px;
    min-height: 120px;
    flex-shrink: 0;
}

.task {
    background: white;
    color: black;
    box-shadow: 0px 5px 15px rgba(0, 0, 0, 0.15);
    padding: 12px;
    border-radius: 4px;
    font-size: 16px;
    cursor: move;
}

.is-dragging {
    scale: 1.05;
    box-shadow: 0px 5px 15px rgba(0, 0, 0, 0.25);
    background: rgb(50, 50, 50);
    color: white;
}


/* Style for the "Rename" buttons */

.swim-lane button {
    margin: 5px;
    padding: 5px 8px;
    background-color: #3498db;
    color: white;
    border: none;
    border-radius: 3px;
    cursor: pointer;
    font-size: 14px;
}

.swim-lane button:hover {
    background-color: #2980b9;
}

.swim-lane form {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-top: 10px;
}

.swim-lane input {
    padding: 12px;
    width: 150px;
    border: none;
    box-shadow: 0px 5px 15px rgba(0, 0, 0, 0.25);
    background: white;
    outline: none;
}