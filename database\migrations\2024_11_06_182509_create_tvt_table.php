<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateTvtTable extends Migration
{
   
    public function up()
    {
        Schema::create('tvt', function (Blueprint $table) {
            $table->id('tvt_id');
            $table->integer('iteration');
            $table->string('backlog_panel');
            $table->string('user_story_panel');
            $table->string('fr');
            $table->string('nfr');
            $table->string('qaw');
            $table->unsignedInteger('project_id');
            $table->timestamps();
        });
    }

    public function down()
    {
        Schema::dropIfExists('tvt');
    }
}
