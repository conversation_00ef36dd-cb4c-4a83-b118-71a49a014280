@extends('layouts.app2')
@include('inc.style')
@include('inc.navbar')

@section('content')
@include('inc.title')

<br>

<!-- Filter Form -->
<form method="GET" action="{{ route('tvt.show', $project->id) }}" class="mb-3">
    <div class="row">
        <!-- Sprint Filter -->
        <div class="col-md-4">
            <div class="form-group">
                <label for="sprint">Filter by Sprint</label>
                <select name="sprint" id="sprint" class="form-control" onchange="this.form.submit()">
                    <option value="">All</option>
                    @foreach($sprints as $sprint)
                        <option value="{{ $sprint }}" {{ request()->input('sprint') == $sprint ? 'selected' : '' }}>
                            {{ $sprint }}
                        </option>
                    @endforeach
                </select>
            </div>
        </div>

        <!-- Specific NFR Filter -->
        <div class="col-md-4">
            <div class="form-group">
                <label for="nfr">Filter by NFR</label>
                <select name="nfr" id="nfr" class="form-control" onchange="this.form.submit()">
                    <option value="">All</option>
                    @foreach($specificNfrs as $specificNfrId => $specificNfrName)
                        <option value="{{ $specificNfrId }}" {{ request()->input('nfr') == $specificNfrId ? 'selected' : '' }}>
                            {{ $specificNfrName }}
                        </option>
                    @endforeach
                </select>
            </div>
        </div>

        <!-- General NFR Filter -->
        <div class="col-md-4">
            <div class="form-group">
                <label for="general_nfr">Filter by QAW</label>
                <select name="general_nfr" id="general_nfr" class="form-control" onchange="this.form.submit()">
                    <option value="">All</option>
                    @foreach($generalNfrs as $generalNfrId => $generalNfrName)
                        <option value="{{ $generalNfrId }}" {{ request()->input('general_nfr') == $generalNfrId ? 'selected' : '' }}>
                            {{ $generalNfrName }}
                        </option>
                    @endforeach
                </select>
            </div>
        </div>
    </div>
</form>

<!-- Data Table -->
<table id="userStoriesTable" class="table table-striped mt-3">
    <thead>
        <tr>
            <th>Sprint</th>
            <th>Backlog Panel</th>
            <th>User Story Panel</th>
            <th>FR->NFR</th>
            <th>QAW</th>
        </tr>
    </thead>
    <tbody>
        @forelse($results as $result)
        <tr>
            <!-- Check if sprint is not null before displaying sprint_name -->
            <td>{{ $result->userStory->sprint ? $result->userStory->sprint->sprint_name : 'No Sprint' }}</td>
            <td>
                @php
                    $sprintNumber = $result->userStory->sprint ? preg_replace('/\D/', '', $result->userStory->sprint->sprint_name) : ''; // Extract sprint number if sprint exists
                @endphp
                {{ $sprintNumber ? 'BG' . $sprintNumber : 'No Backlog' }} <!-- Display formatted backlog panel -->
            </td>
            <td>{{ $result->userStory->user_story }}</td>
            <td>
                 @if(isset($result->userStory->means)) 
                    <strong>{{ $result->userStory->means }}</strong> <!-- Display means in bold -->
                @endif
                <br>
                {{ $result->specificNfr->specific_nfr }} <!-- Display specific NFR -->
            </td>
            <td>{{ $result->generalNfr->general_nfr }}</td>
        </tr>
        @empty
        <tr>
            <td colspan="5" class="text-center">No data found.</td>
        </tr>
        @endforelse
    </tbody>
</table>

<!-- Pagination -->
<div class="pagination-container">
    <ul class="pagination justify-content-center">
        {{ $results->appends(request()->query())->links('pagination::bootstrap-4') }}
    </ul>
</div>
@endsection
