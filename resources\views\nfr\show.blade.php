@extends('layouts.app2')
@include('inc.success')
@include('inc.style')

@section('navbar')
    @include('inc.navbar')
@endsection

@section('content')
@include('inc.title')

<br>

@if($user->user_type == 1 || in_array('Project Manager', $roles))
<table>
    <tr>
        <th>Specific NFR</th>
        <th>Description</th>
        <th>View</th>
        <th>Edit</th>
        <th>Delete</th>
    </tr>
    @foreach ($nfrs as $nfr)
    <tr>
        <td>{{ $nfr->specific_nfr }}</td>
        <td>{{ $nfr->specific_nfr_desc }}</td>
        <td><a href="{{ route('nfr.viewSpecific', ['general_nfr_id' => $nfr->general_nfr_id, 'nfr_id' => $nfr->nfr_id]) }}"  class="btn btn-primary">View</a></td>
        @if($user->user_type == 1 && $nfr->created_by == $user->id)
        <td><a href="{{ route('nfr.editSpecific', ['general_nfr_id' => $nfr->general_nfr_id, 'nfr_id' => $nfr->nfr_id]) }}" class="btn btn-warning">Edit</a></td>
        <td>
            <br>
            <form action="{{ route('nfr.destroySpecific', ['general_nfr_id' => $nfr->general_nfr_id, 'nfr_id' => $nfr->nfr_id]) }}" method="post">
                @csrf
                @method('DELETE')
                <button type="submit" class="btn btn-danger" onclick="return confirm('Are you sure want to delete this non-functional requirement?')">Delete</button>
            </form>
        </td>
        @else
        <td colspan="2">Only creator can edit or delete</td>
        @endif
    </tr>
@endforeach

</table>

<!-- Pagination Links -->
<div class="pagination-container">
    {{ $nfrs->links('pagination::bootstrap-4') }}
</div>

<br><br>
@if($user->user_type == 1)
<a href="{{ route('nfr.createSpecific', ['general_nfr_id' => $general_nfr_id]) }}" class="btn btn-success">Add Specific NFR</a>
@endif
  @else
        <p>Only Project Managers and Admins can view this page.</p>
@endif

@endsection