@extends('layouts.app2')
@include('inc.success')
@include('inc.style')
@include('inc.dashboard')
@include('inc.navbar')

@section('content')
@include('inc.title')
<br><br>

    @csrf
    <table id="userstories" class="table table-bordered">
        <thead>
            <tr>
                <th>User Story</th>
                <th>Assigned To</th>
                <th>Status</th>
                <th>NFR Details</th>
                <th>Task</th>
                <th>Edit</th> 
                <th>Delete</th>
            </tr>
        </thead>
        <tbody>
            @forelse($userstories as $userstory)
                @php
                    // Check if the user story has NFRs linked
                    $hasNfr = isset($nfrData[$userstory->u_id]) && count($nfrData[$userstory->u_id]) > 0;
                @endphp
                <tr class="{{ $hasNfr ? 'table-success' : 'table-danger' }}"> 
                    <td>{{ $userstory->user_story }}</td>
                    <td>
                        @php
                            // Decode user_names and sanitize
                            $userNames = json_decode(trim($userstory->user_names, '"'), true);
                        @endphp

                        @if (is_array($userNames))
                            @foreach($userNames as $user_show)
                                - {{ $user_show }}<br>
                            @endforeach
                        @else
                            <p>-</p>
                        @endif
                    </td>
                    <td>
                        @php
                            $status = $statuses->firstWhere('id', $userstory->status_id);
                        @endphp
                        {{ $status->title }}
                    </td>
                    <td>
                        @if ($hasNfr)
                            <a href="{{ route('userstory.viewDetails', [$userstory->u_id]) }}" class="btn btn-secondary">View Details</a>
                        @else
                            <p>No NFRs assigned</p>
                        @endif
                    </td>
                    <td>
                        <a href="{{ action('TaskController@index', $userstory['u_id']) }}" class="btn btn-primary">View</a>
                    </td>
                    <td>
                        <a href="{{ route('userstory.edit', [$userstory->u_id]) }}" class="btn btn-secondary">Edit</a>
                    </td>
                    <td>
                        <a href="{{ route('userstory.destroy', $userstory) }}" class="btn btn-danger" 
                           onclick="return confirm('Are you sure you want to delete this User Story?');">
                            Delete
                        </a>
                    </td>
                </tr>
            @empty
                <tr>
                    <td colspan="7">No user stories added yet</td>
                </tr>
            @endforelse
        </tbody>
    </table>

    <br><br><br>
    <a href="{{ route('userstory.create', $sprint_id) }}" class="btn btn-success">Create User Story</a>
    <a href="{{ route('userstory.backlog', $sprint_id) }}" class="btn btn-success">Assign User Story from Backlog</a>
    <br><br>
    <a href="{{ route('ucd.index', ['sprint_id' => $sprint_id]) }}" class="btn btn-success">UCD</a>
@endsection
