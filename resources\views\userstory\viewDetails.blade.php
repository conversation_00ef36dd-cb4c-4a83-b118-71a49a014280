@extends('layouts.app2')
@section('dashboard')
@include('inc.style')
@include('inc.navbar')

@section('content')
@include('inc.title')
<br>

<div class="container">
<h3>NFR Details</h3>
<div class="form-group">
    <label for="general_nfr">General NFR:</label>
    <select id="general_nfr" class="form-control">
        <option value="all">All</option>
        @foreach($generalNFRs as $id => $general)
            <option value="{{ $id }}">{{ $general }}</option>
        @endforeach
    </select>
</div>

<table class="table table-bordered">
    <thead>
        <tr>
            <th>General NFR</th>
            <th>Specific NFR</th>
        </tr>
    </thead>
    <tbody id="nfrTableBody">
        @foreach($nfrDetails as $nfr)
            <tr>
                <td>{{ $nfr['general'] }}</td>
                <td>{{ $nfr['specific'] }}</td>
            </tr>
        @endforeach
    </tbody>
</table>

<!-- Pagination Links -->
<div id="paginationLinks">
    {!! $pagination !!}
</div>

<hr>
    <a href="{{ route('userstory.edit', $userstory->u_id) }}" class="btn btn-primary">Edit User Story</a>
    <a href="{{ route('profeature.index3', ['sprint_id' => $userstory->sprint_id]) }}" class="btn btn-secondary">Back to Sprint</a>
</div>

<script>
    document.getElementById('general_nfr').addEventListener('change', function () {
        const generalNfrId = this.value;
        fetchNFRs(generalNfrId, 1);
    });

    function fetchNFRs(generalNfrId, page) {
        fetch(`{{ route('userstory.viewDetails', $userstory->u_id) }}?general_nfr_id=${generalNfrId}&page=${page}`, {
            headers: {
                'X-Requested-With': 'XMLHttpRequest',
            }
        })
        .then(response => response.json())
        .then(data => {
            const tableBody = document.getElementById('nfrTableBody');
            const paginationLinks = document.getElementById('paginationLinks');
            tableBody.innerHTML = '';

            if (data.nfrDetails && data.nfrDetails.length > 0) {
                data.nfrDetails.forEach(nfr => {
                    const row = document.createElement('tr');
                    row.innerHTML = `<td>${nfr.general}</td><td>${nfr.specific}</td>`;
                    tableBody.appendChild(row);
                });
            } else {
                const row = document.createElement('tr');
                row.innerHTML = '<td colspan="2">No NFRs linked.</td>';
                tableBody.appendChild(row);
            }

            paginationLinks.innerHTML = data.pagination;
            attachPaginationLinks(generalNfrId);
        })
        .catch(error => console.error('Error fetching filtered NFRs:', error));
    }

    function attachPaginationLinks(generalNfrId) {
        document.querySelectorAll('#paginationLinks a').forEach(link => {
            link.addEventListener('click', function (e) {
                e.preventDefault();
                const url = new URL(this.href);
                const page = url.searchParams.get('page');
                fetchNFRs(generalNfrId, page);
            });
        });
    }

    // Attach pagination links on page load
    document.addEventListener('DOMContentLoaded', function () {
        attachPaginationLinks('all');
    });
</script>

@endsection