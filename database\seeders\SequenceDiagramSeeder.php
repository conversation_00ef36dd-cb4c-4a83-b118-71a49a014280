<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\SequenceDiagram;
use App\Project;
use App\Sprint;
use App\User;

class SequenceDiagramSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        // Get first project, sprint, and user for relationships
        $project = Project::first();
        $sprint = Sprint::first();
        $user = User::first();

        // Create sample sequence diagrams
        SequenceDiagram::create([
            'name' => 'User Login Sequence',
            'description' => 'Sequence diagram showing the user authentication process',
            'project_id' => $project ? $project->id : null,
            'sprint_id' => $sprint ? $sprint->id : null,
            'user_id' => $user ? $user->id : null,
            'lifelines' => [
                ['name' => 'UserController', 'position_x' => 50, 'type' => 'Controller'],
                ['name' => 'AuthService', 'position_x' => 200, 'type' => 'Service'],
                ['name' => 'UserModel', 'position_x' => 350, 'type' => 'Model'],
                ['name' => 'LoginView', 'position_x' => 500, 'type' => 'View'],
                ['name' => 'User', 'position_x' => 650, 'type' => 'Actor']
            ],
            'diagram_data' => [
                'interactions' => [
                    ['from' => 'User', 'to' => 'LoginView', 'message' => 'Enter credentials'],
                    ['from' => 'LoginView', 'to' => 'UserController', 'message' => 'login(username, password)'],
                    ['from' => 'UserController', 'to' => 'AuthService', 'message' => 'authenticate(credentials)'],
                    ['from' => 'AuthService', 'to' => 'UserModel', 'message' => 'findUser(username)']
                ]
            ],
            'objects_identified' => false
        ]);

        SequenceDiagram::create([
            'name' => 'Task Creation Workflow',
            'description' => 'Sequence diagram for creating new tasks in the system',
            'project_id' => $project ? $project->id : null,
            'sprint_id' => $sprint ? $sprint->id : null,
            'user_id' => $user ? $user->id : null,
            'lifelines' => [
                ['name' => 'TaskController', 'position_x' => 50, 'type' => 'Controller'],
                ['name' => 'TaskService', 'position_x' => 200, 'type' => 'Service'],
                ['name' => 'TaskModel', 'position_x' => 350, 'type' => 'Model'],
                ['name' => 'NotificationHelper', 'position_x' => 500, 'type' => 'Helper'],
                ['name' => 'ProjectManager', 'position_x' => 650, 'type' => 'Actor']
            ],
            'diagram_data' => [
                'interactions' => [
                    ['from' => 'ProjectManager', 'to' => 'TaskController', 'message' => 'createTask(data)'],
                    ['from' => 'TaskController', 'to' => 'TaskService', 'message' => 'validateAndCreate(data)'],
                    ['from' => 'TaskService', 'to' => 'TaskModel', 'message' => 'save(task)'],
                    ['from' => 'TaskService', 'to' => 'NotificationHelper', 'message' => 'sendNotification(task)']
                ]
            ],
            'objects_identified' => false
        ]);

        SequenceDiagram::create([
            'name' => 'Bug Tracking System',
            'description' => 'Sequence diagram for bug reporting and tracking workflow',
            'project_id' => $project ? $project->id : null,
            'sprint_id' => $sprint ? $sprint->id : null,
            'user_id' => $user ? $user->id : null,
            'lifelines' => [
                ['name' => 'BugController', 'position_x' => 50, 'type' => 'Controller'],
                ['name' => 'BugRepository', 'position_x' => 200, 'type' => 'Repository'],
                ['name' => 'BugEntity', 'position_x' => 350, 'type' => 'Entity'],
                ['name' => 'EmailService', 'position_x' => 500, 'type' => 'Service'],
                ['name' => 'Developer', 'position_x' => 650, 'type' => 'Actor']
            ],
            'diagram_data' => [
                'interactions' => [
                    ['from' => 'Developer', 'to' => 'BugController', 'message' => 'reportBug(details)'],
                    ['from' => 'BugController', 'to' => 'BugRepository', 'message' => 'create(bugData)'],
                    ['from' => 'BugRepository', 'to' => 'BugEntity', 'message' => 'new Bug(data)'],
                    ['from' => 'BugController', 'to' => 'EmailService', 'message' => 'notifyAssignee(bug)']
                ]
            ],
            'objects_identified' => false
        ]);
    }
}
