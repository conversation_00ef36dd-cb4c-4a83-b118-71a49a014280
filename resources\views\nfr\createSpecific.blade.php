@extends('layouts.app2')
@include('inc.success')
@include('inc.style')

@section('navbar')
  @include('inc.navbar')
@endsection

@section('content')
@include('inc.title')

<br>
@if ($errors->any())
    <div class="alert alert-danger">
        <ul>
            @foreach ($errors->all() as $error)
                <li>{{ $error }}</li>
            @endforeach
        </ul>
    </div>
@endif
<script>
    function confirmAction(event) {
        event.preventDefault();
        if (confirm("Are you sure you want to proceed?")) {
            event.target.closest('form').submit();
        }
    }
</script>
@if ($user->user_type == 1) 
<form action="{{ route('nfr.storeSpecific', ['general_nfr_id' => $general_nfr_id]) }}" method="POST" onsubmit="confirmAction(event)">
    @csrf
    <label for="specific_nfr">Specific Requirement:</label>
    <input type="text" name="specific_nfr"  class="form-control" required>
    <br><br>
    
    <label for="specific_nfr_desc">Description:</label>
    <input type="text" name="specific_nfr_desc"  class="form-control">
    <br><br>

    <button type="submit" class="btn btn-success">Submit</button>
    <a href="{{ route('nfr.show', ['general_nfr_id' => $general_nfr_id]) }}" class="btn btn-secondary">Cancel</a>
</form>
@else
<div>You do not have access to this page.</div>
@endif

@endsection
