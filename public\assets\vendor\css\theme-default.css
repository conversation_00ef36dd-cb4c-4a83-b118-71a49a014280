body {
  background: #f4f5fa;
}

.bg-body {
  background: #f4f5fa !important;
}

.text-primary {
  color: #1572c4 !important;
}

.text-body[href]:hover {
  color: #1367b0 !important;
}

.bg-primary {
  background-color: #1572c4 !important;
}

a.bg-primary:hover, a.bg-primary:focus {
  background-color: #146cba !important;
}

.dropdown-notifications-item:not(.mark-as-read) .dropdown-notifications-read span {
  background-color: #1572c4;
}

.bg-label-primary {
  background-color: #dceaf6 !important;
  color: #1572c4 !important;
}

.page-item.active .page-link, .page-item.active .page-link:hover, .page-item.active .page-link:focus,
.pagination li.active > a:not(.page-link),
.pagination li.active > a:not(.page-link):hover,
.pagination li.active > a:not(.page-link):focus {
  border-color: #1572c4;
  background-color: #1572c4;
  color: #fff;
}

.page-item.active .page-link.waves-effect .waves-ripple,
.pagination li.active > a:not(.page-link).waves-effect .waves-ripple {
  background: radial-gradient(rgba(255, 255, 255, 0.2) 0, rgba(255, 255, 255, 0.3) 40%, rgba(255, 255, 255, 0.4) 50%, rgba(255, 255, 255, 0.5) 60%, rgba(255, 255, 255, 0) 70%);
}

.pagination-outline-primary .page-item.active .page-link, .pagination-outline-primary .page-item.active .page-link:hover, .pagination-outline-primary .page-item.active .page-link:focus,
.pagination-outline-primary.pagination li.active > a:not(.page-link),
.pagination-outline-primary.pagination li.active > a:not(.page-link):hover,
.pagination-outline-primary.pagination li.active > a:not(.page-link):focus {
  border-color: #8ab9e2;
  color: #1572c4;
  background-color: #ecf4fa;
}

.pagination-outline-primary .page-item.active .page-link.waves-effect .waves-ripple,
.pagination-outline-primary.pagination li.active > a:not(.page-link).waves-effect .waves-ripple {
  background: radial-gradient(rgba(21, 114, 196, 0.2) 0, rgba(21, 114, 196, 0.3) 40%, rgba(21, 114, 196, 0.4) 50%, rgba(21, 114, 196, 0.5) 60%, rgba(58, 53, 65, 0) 70%);
}

.progress-bar {
  background-color: #1572c4;
}

.list-group-item-primary {
  border-color: #2c80ca;
  background-color: #e3eef8;
  color: #1367b0 !important;
}

a.list-group-item-primary,
button.list-group-item-primary {
  color: #1367b0;
}

a.list-group-item-primary:hover, a.list-group-item-primary:focus,
button.list-group-item-primary:hover,
button.list-group-item-primary:focus {
  border-color: #2c80ca;
  background-color: #d8e2ec;
  color: #1367b0;
}

a.list-group-item-primary.active,
button.list-group-item-primary.active {
  border-color: #1572c4 !important;
  background-color: #1572c4 !important;
  color: #fff !important;
}

.list-group-item.active {
  background-color: #ecf4fa;
  color: #544f5a;
}

.list-group-item.active.waves-effect .waves-ripple {
  background: radial-gradient(rgba(21, 114, 196, 0.2) 0, rgba(21, 114, 196, 0.3) 40%, rgba(21, 114, 196, 0.4) 50%, rgba(21, 114, 196, 0.5) 60%, rgba(58, 53, 65, 0) 70%);
}

.alert-primary {
  background-color: #e3eef8;
  border-color: #e3eef8;
  color: #1367b0;
}

.alert-primary .btn-close {
  background-image: url("data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='%231367b0'><path d='M.293.293a1 1 0 0 1 1.414 0L8 6.586 14.293.293a1 1 0 1 1 1.414 1.414L9.414 8l6.293 6.293a1 1 0 0 1-1.414 1.414L8 9.414l-6.293 6.293a1 1 0 0 1-1.414-1.414L6.586 8 .293 1.707a1 1 0 0 1 0-1.414z'/></svg>");
}

.alert-primary .alert-link {
  color: #1367b0;
}

.alert-primary hr {
  background-color: #1367b0 !important;
}

.table-primary {
  --bs-table-bg: #d0e3f3;
  --bs-table-striped-bg: #c9daea;
  --bs-table-striped-color: #3a3541;
  --bs-table-active-bg: #c4d5e5;
  --bs-table-active-color: #3a3541;
  --bs-table-hover-bg: #cadcec;
  --bs-table-hover-color: #3a3541;
  color: #3a3541;
  border-color: #becede;
}

.table-primary .btn-icon {
  color: #3a3541;
}

.btn-primary {
  color: #fff;
  background-color: #1572c4;
  border-color: #1572c4;
}

.btn-primary:hover {
  color: #fff !important;
  background-color: #1365ae !important;
  border-color: #1365ae !important;
}

.btn-check:focus + .btn-primary, .btn-primary:focus, .btn-primary.focus {
  color: #fff;
  background-color: #1365ae;
  border-color: #1365ae;
  box-shadow: none;
}

.btn-check:checked + .btn-primary, .btn-check:active + .btn-primary, .btn-primary:active, .btn-primary.active, .btn-primary.show.dropdown-toggle, .show > .btn-primary.dropdown-toggle {
  color: #fff !important;
  background-color: #115b9d !important;
  border-color: #115b9d !important;
}

.btn-check:checked + .btn-primary:focus, .btn-check:active + .btn-primary:focus, .btn-primary:active:focus, .btn-primary.active:focus, .btn-primary.show.dropdown-toggle:focus, .show > .btn-primary.dropdown-toggle:focus {
  box-shadow: none;
}

.btn-group .btn-primary,
.input-group .btn-primary {
  border-right: 1px solid #115b9d;
  border-left: 1px solid #115b9d;
}

.btn-group-vertical .btn-primary {
  border-top: 1px solid #115b9d;
  border-bottom: 1px solid #115b9d;
}

.btn-outline-primary {
  color: #1572c4;
  border-color: #8ab9e2;
  background: transparent;
}

.btn-outline-primary.waves-effect .waves-ripple {
  background: radial-gradient(rgba(21, 114, 196, 0.2) 0, rgba(21, 114, 196, 0.3) 40%, rgba(21, 114, 196, 0.4) 50%, rgba(21, 114, 196, 0.5) 60%, rgba(255, 255, 255, 0) 70%);
}

.btn-outline-primary:hover {
  color: #1572c4 !important;
  background-color: #eff5fb !important;
  border-color: #8ab9e2 !important;
}

.btn-check:focus + .btn-outline-primary, .btn-outline-primary:focus {
  color: #1572c4;
  background-color: #c7ddf1;
  border-color: #8ab9e2;
}

.btn-check:checked + .btn-outline-primary, .btn-check:active + .btn-outline-primary, .btn-outline-primary:active, .btn-outline-primary.active, .btn-outline-primary.dropdown-toggle.show {
  color: #1572c4 !important;
  background-color: #bdd8ee !important;
  border-color: #8ab9e2 !important;
}

.btn-outline-primary .badge {
  background: #1572c4;
  border-color: #1572c4;
  color: #fff;
}

.btn-outline-primary:hover .badge,
.btn-outline-primary:focus:hover .badge,
.btn-outline-primary:active .badge,
.btn-outline-primary.active .badge,
.show > .btn-outline-primary.dropdown-toggle .badge {
  background: #1572c4;
  border-color: #1572c4;
  color: #fff;
}

.dropdown-item.waves-effect .waves-ripple {
  background: radial-gradient(rgba(21, 114, 196, 0.2) 0, rgba(21, 114, 196, 0.3) 40%, rgba(21, 114, 196, 0.4) 50%, rgba(21, 114, 196, 0.5) 60%, rgba(255, 255, 255, 0) 70%);
}

.dropdown-item:not(.disabled).active, .dropdown-item:not(.disabled):active {
  background-color: rgba(21, 114, 196, 0.1);
  color: #1572c4 !important;
}

.dropdown-menu > li:not(.disabled) > a:not(.dropdown-item):active,
.dropdown-menu > li.active:not(.disabled) > a:not(.dropdown-item) {
  background-color: rgba(21, 114, 196, 0.1);
  color: #1572c4 !important;
}

.dropdown-menu > li:not(.disabled) > a:not(.dropdown-item):active.btn,
.dropdown-menu > li.active:not(.disabled) > a:not(.dropdown-item).btn {
  color: #fff !important;
}

.nav .nav-link:hover, .nav .nav-link:focus {
  color: #1367b0;
}

.nav-pills .nav-link.active, .nav-pills .nav-link.active:hover, .nav-pills .nav-link.active:focus {
  background-color: #1572c4;
  color: #fff;
}

.nav-tabs .nav-link.active, .nav-tabs .nav-link.active:hover, .nav-tabs .nav-link.active:focus {
  color: #1572c4;
}

.nav-tabs .nav-link.waves-effect .waves-ripple {
  background: radial-gradient(rgba(21, 114, 196, 0.2) 0, rgba(21, 114, 196, 0.3) 40%, rgba(21, 114, 196, 0.4) 50%, rgba(21, 114, 196, 0.5) 60%, rgba(255, 255, 255, 0) 70%);
}

.nav-tabs .tab-slider {
  background-color: #1572c4;
}

.form-control:focus,
.form-select:focus {
  border-color: #1572c4 !important;
}

.form-floating-outline .form-control:focus,
.form-floating-outline .form-select:focus {
  border-color: #1572c4 !important;
}

.input-group:not(.input-group-floating):focus-within .form-control,
.input-group:not(.input-group-floating):focus-within .input-group-text {
  border-color: #1572c4;
}

.form-check-input:focus {
  border-color: #1572c4;
}

.form-check-input:active {
  border-color: #1572c4;
}

.form-check-input:hover::after {
  background: rgba(58, 53, 65, 0.04);
}

.form-check-input:checked {
  background-color: #1572c4;
  border-color: #1572c4;
}

.form-check-input:checked::after {
  background: rgba(21, 114, 196, 0.08) !important;
}

.form-check-input[type=checkbox]:indeterminate {
  background-color: #1572c4;
  border-color: #1572c4;
}

.form-switch .form-check-input:focus {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3e%3ccircle r='3' fill='%231572c4'/%3e%3c/svg%3e");
}

.form-switch .form-check-input:checked {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3e%3ccircle r='3' fill='%23fff'/%3e%3c/svg%3e");
}

.form-control:focus ~ .form-label {
  border-color: #1572c4;
}

.form-control:focus ~ .form-label::after {
  border-color: inherit;
}

.form-range::-webkit-slider-thumb {
  background-color: #1572c4;
}

.form-range::-webkit-slider-thumb:hover {
  box-shadow: 0 0 0 8px rgba(21, 114, 196, 0.15), 0px 3px 14px 0px rgba(58, 53, 65, 0.14);
}

.form-range::-webkit-slider-thumb:active {
  background-color: #1572c4;
  box-shadow: 0 0 0 10px rgba(21, 114, 196, 0.2), 0px 3px 14px 0px rgba(58, 53, 65, 0.14);
}

.form-range::-moz-range-thumb:hover {
  box-shadow: 0 0 0 8px rgba(21, 114, 196, 0.15), 0px 3px 14px 0px rgba(58, 53, 65, 0.14);
}

.form-range::-moz-range-thumb:active {
  box-shadow: 0 0 0 10px rgba(21, 114, 196, 0.2), 0px 3px 14px 0px rgba(58, 53, 65, 0.14);
}

.form-range::-webkit-slider-runnable-track {
  background-color: #1572c4;
}

.form-range::-moz-range-track {
  background-color: #1572c4;
}

.divider.divider-primary .divider-text:before, .divider.divider-primary .divider-text:after {
  border-color: #1572c4;
}

.navbar.bg-primary {
  color: #c4dcf0;
}

.navbar.bg-primary .navbar-brand,
.navbar.bg-primary .navbar-brand a {
  color: #fff;
}

.navbar.bg-primary .navbar-brand:hover, .navbar.bg-primary .navbar-brand:focus,
.navbar.bg-primary .navbar-brand a:hover,
.navbar.bg-primary .navbar-brand a:focus {
  color: #fff;
}

.navbar.bg-primary .navbar-search-wrapper .navbar-search-icon,
.navbar.bg-primary .navbar-search-wrapper .search-input {
  color: #c4dcf0;
}

.navbar.bg-primary .search-input-wrapper .search-input,
.navbar.bg-primary .search-input-wrapper .search-toggler {
  color: #c4dcf0;
  border-top-left-radius: 0;
  border-top-right-radius: 0;
}

.navbar.bg-primary .navbar-nav > .nav-link,
.navbar.bg-primary .navbar-nav > .nav-item > .nav-link,
.navbar.bg-primary .navbar-nav > .nav > .nav-item > .nav-link {
  color: #c4dcf0;
}

.navbar.bg-primary .navbar-nav > .nav-link:hover, .navbar.bg-primary .navbar-nav > .nav-link:focus,
.navbar.bg-primary .navbar-nav > .nav-item > .nav-link:hover,
.navbar.bg-primary .navbar-nav > .nav-item > .nav-link:focus,
.navbar.bg-primary .navbar-nav > .nav > .nav-item > .nav-link:hover,
.navbar.bg-primary .navbar-nav > .nav > .nav-item > .nav-link:focus {
  color: #fff;
}

.navbar.bg-primary .navbar-nav > .nav-link.disabled,
.navbar.bg-primary .navbar-nav > .nav-item > .nav-link.disabled,
.navbar.bg-primary .navbar-nav > .nav > .nav-item > .nav-link.disabled {
  color: #7eb2de !important;
}

.navbar.bg-primary .navbar-nav .show > .nav-link,
.navbar.bg-primary .navbar-nav .active > .nav-link,
.navbar.bg-primary .navbar-nav .nav-link.show,
.navbar.bg-primary .navbar-nav .nav-link.active {
  color: #fff;
}

.navbar.bg-primary .navbar-toggler {
  color: #c4dcf0;
  border-color: rgba(255, 255, 255, 0.15);
}

.navbar.bg-primary .navbar-toggler-icon {
  background-image: url("data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 30 30'><path stroke='rgba(255, 255, 255, 0.8)' stroke-linecap='round' stroke-miterlimit='10' stroke-width='2' d='M4 7h22M4 15h22M4 23h22'/></svg>");
}

.navbar.bg-primary .navbar-text {
  color: #c4dcf0;
}

.navbar.bg-primary .navbar-text a {
  color: #fff;
}

.navbar.bg-primary .navbar-text a:hover, .navbar.bg-primary .navbar-text a:focus {
  color: #fff;
}

.navbar.bg-primary hr {
  border-color: rgba(255, 255, 255, 0.15);
}

.menu.bg-primary {
  background-color: #1572c4 !important;
  color: #c4dcf0;
}

.menu.bg-primary .menu-link,
.menu.bg-primary .menu-horizontal-prev,
.menu.bg-primary .menu-horizontal-next {
  color: #c4dcf0;
}

.menu.bg-primary .menu-link:hover, .menu.bg-primary .menu-link:focus,
.menu.bg-primary .menu-horizontal-prev:hover,
.menu.bg-primary .menu-horizontal-prev:focus,
.menu.bg-primary .menu-horizontal-next:hover,
.menu.bg-primary .menu-horizontal-next:focus {
  color: #fff;
}

.menu.bg-primary .menu-link.active,
.menu.bg-primary .menu-horizontal-prev.active,
.menu.bg-primary .menu-horizontal-next.active {
  color: #fff;
}

.menu.bg-primary .menu-item.disabled .menu-link,
.menu.bg-primary .menu-horizontal-prev.disabled,
.menu.bg-primary .menu-horizontal-next.disabled {
  color: #7eb2de !important;
}

.menu.bg-primary .menu-item.active:not(.open) > .menu-link:not(.menu-toggle), .menu.bg-primary .menu-item.active:not(.open) > .menu-link:not(.menu-toggle)::before {
  color: #fff !important;
  border-color: #fff !important;
}

.menu.bg-primary .menu-item.active > .menu-link:not(.menu-toggle) {
  background: linear-gradient(270deg, #1b76c5 0%, #86b6e0 100%);
}

.menu.bg-primary.menu-horizontal .menu-sub > .menu-item.active > .menu-link:not(.menu-toggle), .menu.bg-primary.menu-horizontal .menu-sub > .menu-item.active > .menu-link:not(.menu-toggle)::before {
  background: #e4eff8;
  color: #1b76c5 !important;
}

.menu.bg-primary.menu-horizontal .menu-inner > .menu-item.active > .menu-link.menu-toggle {
  background: linear-gradient(270deg, #1b76c5 0%, #86b6e0 100%);
}

.menu.bg-primary.menu-horizontal .menu-inner > .menu-item.active > .menu-link.menu-toggle, .menu.bg-primary.menu-horizontal .menu-inner > .menu-item.active > .menu-link.menu-toggle:after {
  color: #fff;
}

.menu.bg-primary .menu-inner-shadow {
  background: linear-gradient(#1572c4 5%, rgba(21, 114, 196, 0.75) 45%, rgba(21, 114, 196, 0.2) 80%, transparent);
}

.menu.bg-primary .menu-text {
  color: #fff;
}

.menu.bg-primary .menu-header {
  color: #98c2e5;
}

.menu.bg-primary hr,
.menu.bg-primary .menu-divider,
.menu.bg-primary .menu-inner > .menu-item.open > .menu-sub::before {
  border-color: rgba(255, 255, 255, 0.15) !important;
}

.menu.bg-primary .menu-block::before {
  background-color: #98c2e5;
}

.menu.bg-primary .ps__thumb-y,
.menu.bg-primary .ps__rail-y.ps--clicking > .ps__thumb-y {
  background: rgba(255, 255, 255, 0.5498682353) !important;
}

.footer.bg-primary {
  color: #c4dcf0;
}

.footer.bg-primary .footer-link {
  color: #c4dcf0;
}

.footer.bg-primary .footer-link:hover, .footer.bg-primary .footer-link:focus {
  color: #c4dcf0;
}

.footer.bg-primary .footer-link.disabled {
  color: #7eb2de !important;
}

.footer.bg-primary .footer-text {
  color: #fff;
}

.footer.bg-primary .show > .footer-link,
.footer.bg-primary .active > .footer-link,
.footer.bg-primary .footer-link.show,
.footer.bg-primary .footer-link.active {
  color: #fff;
}

.footer.bg-primary hr {
  border-color: rgba(255, 255, 255, 0.15);
}

.form-floating > .form-control:focus:not(:-moz-placeholder-shown) ~ label, .form-floating > .form-select:focus:not(:-moz-placeholder-shown) ~ label {
  color: #1572c4;
}

.form-floating > .form-control:focus:not(:-ms-input-placeholder) ~ label, .form-floating > .form-select:focus:not(:-ms-input-placeholder) ~ label {
  color: #1572c4;
}

.form-floating > .form-control:focus ~ label,
.form-floating > .form-control:focus:not(:placeholder-shown) ~ label,
.form-floating > .form-select:focus ~ label,
.form-floating > .form-select:focus:not(:placeholder-shown) ~ label {
  color: #1572c4;
}

.form-floating-outline :not(select):focus + label,
.form-floating-outline :not(select):focus + span {
  color: #1572c4;
}

.form-floating-outline label::after,
.form-floating-outline > span::after {
  background: #fff;
}

.form-floating-outline label.bg-body::after,
.form-floating-outline > span.bg-body::after {
  background: #f4f5fa !important;
}

.svg-illustration svg {
  fill: #1572c4;
}

html:not([dir=rtl]) .border-primary,
html[dir=rtl] .border-primary {
  border-color: #1572c4 !important;
}

a {
  color: #1572c4;
}

a:hover {
  color: #2c80ca;
}

.fill-primary {
  fill: #1572c4;
}

.bg-navbar-theme {
  color: #544f5a;
}

.bg-navbar-theme .navbar-brand,
.bg-navbar-theme .navbar-brand a {
  color: #544f5a;
}

.bg-navbar-theme .navbar-brand:hover, .bg-navbar-theme .navbar-brand:focus,
.bg-navbar-theme .navbar-brand a:hover,
.bg-navbar-theme .navbar-brand a:focus {
  color: #544f5a;
}

.bg-navbar-theme .navbar-search-wrapper .navbar-search-icon,
.bg-navbar-theme .navbar-search-wrapper .search-input {
  color: #544f5a;
}

.bg-navbar-theme .search-input-wrapper .search-input,
.bg-navbar-theme .search-input-wrapper .search-toggler {
  color: #544f5a;
  border-top-left-radius: 0;
  border-top-right-radius: 0;
}

.bg-navbar-theme .navbar-nav > .nav-link,
.bg-navbar-theme .navbar-nav > .nav-item > .nav-link,
.bg-navbar-theme .navbar-nav > .nav > .nav-item > .nav-link {
  color: #544f5a;
}

.bg-navbar-theme .navbar-nav > .nav-link:hover, .bg-navbar-theme .navbar-nav > .nav-link:focus,
.bg-navbar-theme .navbar-nav > .nav-item > .nav-link:hover,
.bg-navbar-theme .navbar-nav > .nav-item > .nav-link:focus,
.bg-navbar-theme .navbar-nav > .nav > .nav-item > .nav-link:hover,
.bg-navbar-theme .navbar-nav > .nav > .nav-item > .nav-link:focus {
  color: #544f5a;
}

.bg-navbar-theme .navbar-nav > .nav-link.disabled,
.bg-navbar-theme .navbar-nav > .nav-item > .nav-link.disabled,
.bg-navbar-theme .navbar-nav > .nav > .nav-item > .nav-link.disabled {
  color: #94919a !important;
}

.bg-navbar-theme .navbar-nav .show > .nav-link,
.bg-navbar-theme .navbar-nav .active > .nav-link,
.bg-navbar-theme .navbar-nav .nav-link.show,
.bg-navbar-theme .navbar-nav .nav-link.active {
  color: #544f5a;
}

.bg-navbar-theme .navbar-toggler {
  color: #544f5a;
  border-color: rgba(84, 79, 90, 0.0769076471);
}

.bg-navbar-theme .navbar-toggler-icon {
  background-image: url("data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 30 30'><path stroke='rgba(137, 134, 141, 0.75)' stroke-linecap='round' stroke-miterlimit='10' stroke-width='2' d='M4 7h22M4 15h22M4 23h22'/></svg>");
}

.bg-navbar-theme .navbar-text {
  color: #544f5a;
}

.bg-navbar-theme .navbar-text a {
  color: #544f5a;
}

.bg-navbar-theme .navbar-text a:hover, .bg-navbar-theme .navbar-text a:focus {
  color: #544f5a;
}

.bg-navbar-theme hr {
  border-color: rgba(84, 79, 90, 0.0769076471);
}

.bg-menu-theme {
  background-color: #f4f5fa !important;
  color: #544f5a;
}

.bg-menu-theme .menu-link,
.bg-menu-theme .menu-horizontal-prev,
.bg-menu-theme .menu-horizontal-next {
  color: #544f5a;
}

.bg-menu-theme .menu-link:hover, .bg-menu-theme .menu-link:focus,
.bg-menu-theme .menu-horizontal-prev:hover,
.bg-menu-theme .menu-horizontal-prev:focus,
.bg-menu-theme .menu-horizontal-next:hover,
.bg-menu-theme .menu-horizontal-next:focus {
  color: #544f5a;
}

.bg-menu-theme .menu-link.active,
.bg-menu-theme .menu-horizontal-prev.active,
.bg-menu-theme .menu-horizontal-next.active {
  color: #544f5a;
}

.bg-menu-theme .menu-item.disabled .menu-link,
.bg-menu-theme .menu-horizontal-prev.disabled,
.bg-menu-theme .menu-horizontal-next.disabled {
  color: #94919a !important;
}

.bg-menu-theme .menu-item.active:not(.open) > .menu-link:not(.menu-toggle), .bg-menu-theme .menu-item.active:not(.open) > .menu-link:not(.menu-toggle)::before {
  color: #fff !important;
  border-color: #fff !important;
}

.bg-menu-theme .menu-item.active > .menu-link:not(.menu-toggle) {
  background: linear-gradient(270deg, #1572c4 0%, #83b4e0 100%);
}

.bg-menu-theme.menu-horizontal .menu-sub > .menu-item.active > .menu-link:not(.menu-toggle), .bg-menu-theme.menu-horizontal .menu-sub > .menu-item.active > .menu-link:not(.menu-toggle)::before {
  background: #e3eef8;
  color: #1572c4 !important;
}

.bg-menu-theme.menu-horizontal .menu-inner > .menu-item.active > .menu-link.menu-toggle {
  background: linear-gradient(270deg, #1572c4 0%, #83b4e0 100%);
}

.bg-menu-theme.menu-horizontal .menu-inner > .menu-item.active > .menu-link.menu-toggle, .bg-menu-theme.menu-horizontal .menu-inner > .menu-item.active > .menu-link.menu-toggle:after {
  color: #fff;
}

.bg-menu-theme .menu-inner-shadow {
  background: linear-gradient(#f4f5fa 5%, rgba(244, 245, 250, 0.75) 45%, rgba(244, 245, 250, 0.2) 80%, transparent);
}

.bg-menu-theme .menu-text {
  color: #544f5a;
}

.bg-menu-theme .menu-header {
  color: #7c7982;
}

.bg-menu-theme hr,
.bg-menu-theme .menu-divider,
.bg-menu-theme .menu-inner > .menu-item.open > .menu-sub::before {
  border-color: transparent !important;
}

.bg-menu-theme .menu-block::before {
  background-color: #7c7982;
}

.bg-menu-theme .ps__thumb-y,
.bg-menu-theme .ps__rail-y.ps--clicking > .ps__thumb-y {
  background: rgba(84, 79, 90, 0.2152611765) !important;
}

@media (min-width: 1200px) {
  .layout-menu-collapsed.layout-menu-hover .bg-menu-theme {
    box-shadow: 0 0.625rem 0.875rem rgba(58, 53, 65, 0.12);
  }
}

.bg-footer-theme {
  color: #1572c4;
}

.bg-footer-theme .footer-link {
  color: #1572c4;
}

.bg-footer-theme .footer-link:hover, .bg-footer-theme .footer-link:focus {
  color: #1572c4;
}

.bg-footer-theme .footer-link.disabled {
  color: #6ea6da !important;
}

.bg-footer-theme .footer-text {
  color: #544f5a;
}

.bg-footer-theme .show > .footer-link,
.bg-footer-theme .active > .footer-link,
.bg-footer-theme .footer-link.show,
.bg-footer-theme .footer-link.active {
  color: #544f5a;
}

.bg-footer-theme hr {
  border-color: rgba(84, 79, 90, 0.0769076471);
}
